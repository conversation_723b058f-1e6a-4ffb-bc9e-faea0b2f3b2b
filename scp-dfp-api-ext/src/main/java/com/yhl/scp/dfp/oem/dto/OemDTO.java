package com.yhl.scp.dfp.oem.dto;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <code>OemDTO</code>
 * <p>
 * 主机厂档案DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:10:38
 */
@ApiModel(value = "主机厂档案DTO")
@Data
@EqualsAndHashCode(callSuper = true)
public class OemDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -69243590197061201L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 销售组织Id
     */
    @ApiModelProperty(value = "销售组织Id")
    private String saleOrgId;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    private String oemName;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 地点编码
     */
    @ApiModelProperty(value = "地点编码")
    private String locationCode;
    /**
     * 地址1
     */
    @ApiModelProperty(value = "地址1")
    private String locationArea1;
    /**
     * 地址2
     */
    @ApiModelProperty(value = "地址2")
    private String locationArea2;
    /**
     * 地址3
     */
    @ApiModelProperty(value = "地址3")
    private String locationArea3;
    /**
     * 地址4
     */
    @ApiModelProperty(value = "地址4")
    private String locationArea4;
    /**
     * 付款条件
     */
    @ApiModelProperty(value = "付款条件")
    private String paymentTerm;
    /**
     * 运输条款
     */
    @ApiModelProperty(value = "运输条款")
    private String transitClause;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    /**
     * 市场属性
     */
    @ApiModelProperty(value = "市场属性")
    private String marketType;
    /**
     * 目标货位
     */
    @ApiModelProperty(value = "目标货位")
    private String targetStockLocation;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * ediLocation
     */
    @ApiModelProperty(value = "ediLocation")
    private String ediLocation;
    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    private String plantCode;
    /**
     * erp客户id
     */
    @ApiModelProperty(value = "erp客户id")
    private String ebsCustomerId;
    /**
     * erp地点id
     */
    @ApiModelProperty(value = "erp地点id")
    private String ebsSiteId;
    /**
     * erp地址用途id
     */
    @ApiModelProperty(value = "erp地址用途id")
    private String shipToSiteUseId;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String siteCountry;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateTime;
    /**
     * edi标志
     */
    @ApiModelProperty(value = "edi标志")
    private String ediFlag;

    /**
     * 理货单模式，MES，GRP
     */
    @ApiModelProperty(value = "理货单模式，MES，GRP")
    private String tallyOrderMode;
    
    /**
     * 自提类型：自提，非自提
     */
    @ApiModelProperty(value = "自提类型：自提，非自提")
    private String pickUpType;
    
    /**
     * ERP地址ID
     */
    @ApiModelProperty(value = "ERP地址ID")
    private String erpCustomerAddressId;
    
    /**
     * 中转库目标货位
     */
    @ApiModelProperty(value = "中转库目标货位")
    private String transferTargetStockLocation;
    
    /**
     * 客户简称
     */
    @ApiModelProperty(value = "客户简称")
    private String customerAbbreviation;
    
}