package com.yhl.scp.dfp.oem.service;

import java.util.List;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.dto.OemVehicleModelDTO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

/**
 * <code>OemVehicleModelService</code>
 * <p>
 * 主机厂车型信息应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:13:17
 */
public interface OemVehicleModelService extends BaseService<OemVehicleModelDTO, OemVehicleModelVO> {

    /**
     * 查询所有
     *
     * @return list {@link OemVehicleModelVO}
     */
    List<OemVehicleModelVO> selectAll();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据主机厂编码查询车型信息
     *
     * @param oemCode
     * @param vehicleCode
     * @return
     */
    List<OemVehicleModelVO> selectVehicleByOemCode(String oemCode, String vehicleCode);

    /**
     * 根据主机厂编码查询车型编码
     * @param oemCode
     * @return
     */
    List<LabelValue<String>> queryVehicleModelCodeByOemCode(String oemCode);


    /**
     * 刷新价格
     */
    void doRefreshPrice();

    /**
     * 更新综合系数
     * @param oemVehicleModelDTO
     */
    void doUpdateComprehensiveCoefficient(OemVehicleModelDTO oemVehicleModelDTO);

    List<OemVehicleModelVO> selectAllVO();

    List<OemVehicleModelVO> selectOemByVehicleCode(List<String> vehicleModelCodeList);

    /**
     * 查询物料所有的车型
     * @return
     */
    List<LabelValue<String>> selectAllVehicleModel();

	List<NewProductStockPointVO> selectProductByOemCode(String oemCode);
}
