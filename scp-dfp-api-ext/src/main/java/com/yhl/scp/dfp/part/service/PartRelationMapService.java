package com.yhl.scp.dfp.part.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.excel.IExcelDynamicDataImport;
import com.yhl.scp.dfp.part.dto.PartRelationMapDTO;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>PartRelationMapService</code>
 * <p>
 * 零件映射关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:06:34
 */
public interface PartRelationMapService extends BaseService<PartRelationMapDTO, PartRelationMapVO>, IExcelDynamicDataImport {

    /**
     * 查询所有
     *
     * @return list {@link PartRelationMapVO}
     */
    List<PartRelationMapVO> selectAll();

    /**
     * 批量删除
     *
     * @param versionDTOList 删除参数
     * @return int
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 导出
     *
     * @param response 响应体
     */
    void export(HttpServletResponse response);

    /**
     * 同步新增零件映射
     *
     * @param list
     * @return
     */
    BaseResponse<Void> updatePartRelationMappingData(List<PartRelationMapVO> list);

    /**
     * 同步新增MDM零件履历数据
     *
     * @param list
     * @return
     */
    BaseResponse<Void> updateMDMPartMappingData(List<PartRelationMapVO> list);

    /**
     * 提报
     *
     * @param beginTime
     * @param endTime
     * @param id
     * @param tenant
     * @param resourceType
     * @param dataBaseName
     * @return
     */
    BaseResponse<Void> submission(String beginTime, String endTime, String id, String tenant, String resourceType, String dataBaseName);

    /**
     * 根据产品编码列表查询客户零件映射集
     *
     * @param productCodes 产品编码
     * @return
     */
    BaseResponse<List<PartRelationMapVO>> getDataByCodes(List<String> productCodes);
    /**
     * 根据客户零件列表查询客户零件映射集
     *
     * @param partNumbers 产品编码
     * @return
     */
    BaseResponse<List<PartRelationMapVO>> getByPartNums(List<String> partNumbers);

    /**
     * 从ERP同步
     * @param params
     */
    void syncPartNumFromErp(Map<String, Object> params);
}