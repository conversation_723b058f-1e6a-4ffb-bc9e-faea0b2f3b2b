package com.yhl.scp.dcp.externalApi.handler.fone;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpToken;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.handler.TokenHandler;
import com.yhl.scp.dcp.token.Token;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName AuthHandler
 * @Description TODO
 * @Date 2025-03-03 11:10:53
 * <AUTHOR>
 * @Version 1.0
 */
@Slf4j
@Component("authHandler4FONE")
public class AuthHandler extends TokenHandler {

    @Override
    protected Token getToken(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        String apiUri = apiConfigVO.getApiUri();
        String apiParams = apiConfigVO.getApiParams();
        String url = apiUri + apiParams;
        if (log.isInfoEnabled()) {
            log.info("apiUri={},params={},url={}", apiUri, apiParams, url);
        }
        String responseData = restTemplate.postForObject(url, null, String.class);
        ErpResponse erpResponse = JSONObject.parseObject(responseData, ErpResponse.class);
        log.info("请求FONE接口TOKEN完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
        Assert.isTrue(erpResponse.getSuccess(), "请求FONE接口TOKEN返回错误!");
        Object data = erpResponse.getData();
        ErpToken erpToken = JSONObject.parseObject(data.toString(), ErpToken.class);
        return Token.builder()
                .token(erpToken.getToken())
                .expiresIn(erpToken.getExpiresIn())
                .build();
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.FONE.getCode(), ApiCategoryEnum.AUTH.getCode());
    }
}
