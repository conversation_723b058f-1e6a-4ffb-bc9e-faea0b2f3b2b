package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductionOrganize;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName SyncCustomerHandler
 * @Description TODO
 * @Date 2024-08-27 11:10:53
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class StockPointHandler extends SyncDataHandler<List<MesProductionOrganize>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesProductionOrganize> mesProductionOrganizes) {
        Date lastUpdateDate = mesProductionOrganizes.stream().map(MesProductionOrganize::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected List<MesProductionOrganize> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取外部生产组织数据为空");
            return null;
        }
        List<MesProductionOrganize> mesProductionOrganizes = JSONArray.parseArray(body, MesProductionOrganize.class);
        return mesProductionOrganizes;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesProductionOrganize> mesProductionOrganizes) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mesProductionOrganizes)) {
            log.error("生产组织为空");
            return null;
        }
          mesProductionOrganizes = mesProductionOrganizes.stream().collect(Collectors.collectingAndThen(
                    Collectors.toMap(
                            MesProductionOrganize::getPlantId,
                            dto -> dto,
                            (dto1, dto2) -> dto1.getLastUpdateDate().after(dto2.getLastUpdateDate()) ? dto1 : dto2
                    ),
                    map -> new ArrayList<>(map.values())
            ));
        newMdsFeign.handleProductionOrganizes(params.get("scenario").toString(), mesProductionOrganizes);
        this.saveSyncCtrl(apiConfigVO, params, mesProductionOrganizes);
        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES库存点数据:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri + apiParams;
            String reqCode = params.get("reqCode").toString();
            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={},reqCode={},lastUpdateDateStr={},currentDate={}", mesToken, apiUri, apiParams,
                        url, reqCode, lastUpdateDateStr, currentDate);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);

            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", 10000);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                    paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "ERP同步生产组织失败！");
                    String body = responseEntity.getBody();
                    log.info("同步MES库存点数据完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    MesResponseData data = mesResponse.getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                            beginTime = endTime;
                        } else {
                            currentPage++;
                        }
                    }
                }
            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.STOCK_POINT.getCode());
    }
}
