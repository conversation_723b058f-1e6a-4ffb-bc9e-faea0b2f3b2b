package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.warehouse.dto.AbroadWarehouseReleaseRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 获取mes系统的fye收发货记录 插入发售或记录日志表
 * author：李杰
 * email: <EMAIL>
 * date: 2025/08/11
 */
@Component
@Slf4j
public class FYEWareHouseReleaseHandler extends SyncDataHandler<List<AbroadWarehouseReleaseRecordDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return params.get("plateCode").toString();
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<AbroadWarehouseReleaseRecordDTO> warehouseReleaseRecordDTOS) {
        if (warehouseReleaseRecordDTOS.isEmpty()) {
            Date date = (Date) params.get("currentDate");
            Date thirtyMinutesAgo = new Date(date.getTime() - (30 * 60 * 1000));
            return DateUtils.dateToString(thirtyMinutesAgo, DateUtils.COMMON_DATE_STR1);
        }
        Date lastUpdateDate = warehouseReleaseRecordDTOS.stream().map(AbroadWarehouseReleaseRecordDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES的FYE收发货记录数据:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            //获取MES的token
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            //mes收发货记录地址接口
            String apiUri = apiConfigVO.getApiUri();
            //获取params参数
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri + "?serverId=" + apiParams;
            Date beginDate;
            Date currentDate;
            if (Objects.nonNull(params.get("triggerType"))) {
                //由于MES设置前后时间不可超过七天，所以将时间分段调用
                beginDate = DateUtils.stringToDate(params.get("lastUpdateDate").toString(), DateUtils.COMMON_DATE_STR1);
                currentDate = DateUtils.stringToDate(params.get("endDate").toString(), DateUtils.COMMON_DATE_STR1);
            } else {
                beginDate = DateUtils.stringToDate(this.getSyncRefValue(apiConfigVO, params), DateUtils.COMMON_DATE_STR1);
                currentDate = new Date();
                params.put("currentDate", currentDate);
            }
            if (beginDate.compareTo(currentDate) > 0) {
                log.error("时间错误，lastUpdateDate不能大于endDate");
                return null;
            }
            if (log.isInfoEnabled()) {
                log.info("mesToken={},url={},lastUpdateDateStr={},currentDate={}", mesToken, url, beginDate, currentDate);
            }
            //请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            //请求体
            int size = Objects.isNull(apiConfigVO.getOffsetSize()) ? 5000 : apiConfigVO.getOffsetSize();
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("reqCode", "FY_PKN_SHIPPINGLIST_FOR_BPIM");
            paramMap.put("pageSize", size);
            //筛选事业部
            List<Map<String, Object>> objects = new ArrayList<>();
            Map<String, Object> queryMap = ImmutableMap.of("cKey", "plantcode", "cValue", params.get("plateCode"));
            objects.add(queryMap);
            paramMap.put("conditions", objects);

            int period = (int) DateUtil.between(beginDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            List<Object> arrayList = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                for (int page = 1; ; page++) {
                    paramMap.put("currentPage", page);
                    paramMap.put("beginTime", DateUtils.dateToString(beginDate, DateUtils.COMMON_DATE_STR1));
                    Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(beginDate, calculatePeriod);
                    if (endDate.compareTo(currentDate) > 0) {
                        endDate = currentDate;
                    }
                    paramMap.put("endTime", DateUtils.dateToString(endDate, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(paramMap), httpHeaders);
                    StopWatch stopWatch = new StopWatch();
                    stopWatch.start();
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    stopWatch.stop();
                    log.info("调用本次FYE的仓库收发货数据耗时:{}", stopWatch.prettyPrint());
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() == statusCodeValue) {
                        String body = responseEntity.getBody();
                        log.info("同步MES的FYE仓库收发货接口完成,返回数据:{}!", body);
                        MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                        MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                        extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                        if (Objects.nonNull(data)) {
                            arrayList.addAll(data.getMessage());
                            if (data.getCurrentPage() >= data.getTotalPage()) {
                                beginDate = endDate;
                                break;
                            }
                        }
                    } else {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                }
            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, arrayList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(arrayList);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<AbroadWarehouseReleaseRecordDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        return JSONObject.parseArray(body, AbroadWarehouseReleaseRecordDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List<AbroadWarehouseReleaseRecordDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MES获取FYE的仓库收发货数据为空");
            if (Objects.isNull(paramMap.get("triggerType"))) {
                this.saveSyncCtrl(apiConfigVO, paramMap, list);
            }
            return null;
        }
        //获取模块标识
        String scenario = paramMap.get("scenario").toString();
        list.stream().forEach(x -> {
                    x.setIsReceive(StringUtils.isEmpty(x.getIsReceive()) ? "N" : x.getIsReceive());
                    x.setSourceType("MES");
                }
        );
        dfpFeign.syncFYEWareHouseReleaseData(scenario, list);
        if (Objects.isNull(paramMap.get("triggerType"))) {
            this.saveSyncCtrl(apiConfigVO, paramMap, list);
        }
        return "同步成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.FYE_WAREHOUSE_RELEASE.getCode());
    }
}
