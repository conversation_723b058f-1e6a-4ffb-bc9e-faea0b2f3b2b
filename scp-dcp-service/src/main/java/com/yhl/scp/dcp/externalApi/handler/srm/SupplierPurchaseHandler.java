package com.yhl.scp.dcp.externalApi.handler.srm;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dcp.externalApi.handler.erp.AuthHandler;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName SupplierHandler
 * @Description TODO
 * @Date 2024-11-26 11:10:53
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class SupplierPurchaseHandler extends SyncDataHandler<List<SrmSupplierPurchase>> {
    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private AuthHandler authHandler;

    @Override
    protected List<SrmSupplierPurchase> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("SRM获取物料与供应商关系为空");
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(body);

        JSONObject  jsObjectService = jsonObject.getJSONObject("Service");
        JSONObject serviceStatus = jsObjectService.getJSONObject("Route").getJSONObject("ServiceResponse");
        String status= serviceStatus.getString("Status");
        if ("FAIL".equals(status)) {
            throw new BusinessException("SRM获取物料与供应商关系接口异常:"+serviceStatus.getString("Desc"));
        }
        JSONObject request=jsObjectService.getJSONObject("Data").getJSONObject("Response").getJSONObject(
                "GetItemAndVendorRelationsResult");
        JSONArray itemAndVendorRelation = request.getJSONArray("ItemAndVendorRelation");
        return JSON.parseArray(itemAndVendorRelation.toJSONString(), SrmSupplierPurchase.class);
    }


    /**
     * 获取最新的更新时间
     *
     * @param srmSupplierPurchases
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<SrmSupplierPurchase> srmSupplierPurchases) {
        Date lastUpdateDate = srmSupplierPurchases.stream().map(SrmSupplierPurchase::getLastUpdateTime).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param srmSupplierPurchases
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                List<SrmSupplierPurchase> srmSupplierPurchases) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(srmSupplierPurchases)) {
            log.error("SRM获取物料与供应商关系为空");
            return null;
        }
        srmSupplierPurchases = srmSupplierPurchases.stream()
                .filter(srmSupplierPurchase -> {
                    String code = srmSupplierPurchase.getItemCode();
                    //材料计划现只用到B类和PVB的,过滤掉不是B类和PVB的数据
                    return code != null && (code.startsWith("AP") || code.startsWith("B") || code.startsWith("K"));
                })
                .collect(Collectors.toMap(
                        SrmSupplierPurchase::getSrmId,
                        dto -> dto,
                        (dto1, dto2) -> dto1.getLastUpdateTime().after(dto2.getLastUpdateTime()) ? dto1 : dto2
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(srmSupplierPurchases)) {
            log.info("SRM获取物料与供应商关系经过过滤后数据为空");
            return null;
        }
        mrpFeign.handleSupplierPurchase(params.get("scenario").toString(), srmSupplierPurchases);
        this.saveSyncCtrl(apiConfigVO, params, srmSupplierPurchases);
        return null;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步SRM获取物料与供应商关系:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String apiBody = apiConfigVO.getApiBody();
            JSONObject jsonObject = JSON.parseObject(apiBody);
            JSONObject request = jsonObject.getJSONObject("Service").getJSONObject("Data").getJSONObject("Request");

            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            request.put("lastUpdateTime", lastUpdateDate);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},bodyStr={}", erpToken, apiUri
                        , systemNumber, lastUpdateDate, apiUri, bodyStr);
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(jsonObject.toJSONString(), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(apiUri, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP供应商基础数据请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();
            log.info("同步SRM获取物料与供应商关系处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(),
                ApiCategoryEnum.SUPPLIER_PURCHASE.getCode());
    }
}
