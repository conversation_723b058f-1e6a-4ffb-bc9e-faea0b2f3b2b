package com.yhl.scp.dcp.externalApi.handler.mes;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description:
 * author：zyh
 * date: 2024/11/13
 */
@Component
@Slf4j
public class RoutingStepResourceHandler extends SyncDataHandler<List<MesMoldChangeTime>> {
    @Resource
    private AuthHandler authHandler;

    @Override
    protected List<MesMoldChangeTime> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES同步生产节拍数据为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, MesMoldChangeTime.class);
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<MesMoldChangeTime> mesMoldChangeTimeS) {
        Date lastUpdateDate = mesMoldChangeTimeS.stream().map(MesMoldChangeTime::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesMoldChangeTime> mesMoldChangeTimes) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mesMoldChangeTimes)) {
            log.error("生产节拍数据为空");
            return null;
        }
        try {
            String stockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                    .map(CollectionValueVO::getCollectionValue)
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));
            String scenario = params.get("scenario").toString();
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, "OP_BUSINESS_AREA", "EXTERNAL_REQ", null);
            String rangeData = scenarioBusinessRange.getData().getRangeData();
            if(StringUtils.isBlank(rangeData)){
                log.error("未找到对应的范围数据！");
                throw new BusinessException("未找到对应的范围数据！");
            }
            // List<NewStockPointVO> newStockPointVOList = newMdsFeign.selectAllStockPoint(mdsScenario.getData());
            // Set<String> planArea = newStockPointVOList.stream()
            //         .map(NewStockPointVO::getPlanArea)
            //         .collect(Collectors.toSet());
            mesMoldChangeTimes = mesMoldChangeTimes.stream()
                    .map(moldChangeTime -> {
                        if (moldChangeTime.getProdLineCode() == null &&
                                stockPoint2.equals(moldChangeTime.getPlantCode()) &&
                                "20".equals(moldChangeTime.getSequenceCode())) {
                            moldChangeTime.setProdLineCode("S2XL01");
                        }
                        return moldChangeTime;
                    }).filter(moldChangeTime -> rangeData.equals(moldChangeTime.getScheduleRegionCode()))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    moldChangeTime -> moldChangeTime.getItemCode() + "_" + moldChangeTime.getProdLineCode()+"_"+moldChangeTime.getSequenceCode(),
                                    dto -> dto,
                                    (dto1, dto2) -> dto1.getLastUpdateDate().after(dto2.getLastUpdateDate()) ? dto1 : dto2
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            newMdsFeign.handleRoutingStepResource(scenario, mesMoldChangeTimes);
            this.saveSyncCtrl(apiConfigVO, params, mesMoldChangeTimes);
            return "同步成功";
        } catch (Exception e) {
            return "处理生产节拍时发生错误: " + e.getMessage();
        }
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES生产节拍:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri  + apiParams;
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={}", mesToken, apiUri, apiParams,
                        url);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            String reqCode = "FY_FINE_BEAT_FOR_BPIM";
            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);

            Date endDate = new Date();
            if (params.containsKey(DcpConstants.SYNC_BATCH_END_TIME)) {
                endDate = (Date) params.get(DcpConstants.SYNC_BATCH_END_TIME);
            }
            int period = (int) DateUtil.between(calculateDate, endDate, DateUnit.DAY);

            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", 10000);
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date syncEndTime = DateUtil.offsetDay(beginTime, calculatePeriod);
                    // 计算出来的日期不能大于需要同步的截至日期
                    if (DateUtil.compare(endDate, syncEndTime) < 0) {
                        syncEndTime = endDate;
                    }
                    paramMap.put("endTime", DateUtils.dateToString(syncEndTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步箱体信息失败！");
                    String body = responseEntity.getBody();
                    log.info("请求MES生产节拍完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                            beginTime = syncEndTime;
                        } else {
                            currentPage++;
                        }
                    }

                }

            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.NEW_PRODUCT_CANDIDATE_RESOURCE.getCode());
    }

}
