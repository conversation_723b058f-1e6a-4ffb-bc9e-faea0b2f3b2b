package com.yhl.scp.dcp.externalApi.handler;

import cn.hutool.crypto.SecureUtil;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.core.SequenceService;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <code>AbstractHandler</code>
 * <p>
 * AbstractHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-02 22:28:55
 */
@Slf4j
public abstract class AbstractHandler<T> implements Handler {

    @Resource
    protected ApiConfigService apiConfigService;

    @Resource
    protected RedisUtil redisUtil;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    protected SequenceService sequenceService;

    @Resource
    protected IpsNewFeign ipsNewFeign;

    @Resource
    protected IpsFeign ipsFeign;

    @Resource
    protected NewMdsFeign newMdsFeign;

    @Resource
    protected ExtApiLogService extApiLogService;

    /**
     * 转化数据结构
     *
     * @param body 响应体
     * @return T
     */
    protected abstract T convertData(String body);

    /**
     * 处理具体内容
     *
     * @param apiConfigVO 接口对象
     * @param params      请求参数
     * @param t           映射对象
     * @return java.lang.String
     */
    protected abstract String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, T t);

    /**
     * 发送请求
     *
     * @param apiConfigVO 接口对象
     * @param params      请求参数
     * @return java.lang.String
     */
    protected abstract String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params);

    /**
     * 处理数据同步
     *
     * @param params 请求参数
     * @return java.lang.String
     */
    public String handle(Map<String, Object> params) {
        String redisKey = null;
        try {
            // 添加锁，对同一租户，同一笔请求上锁，防止重复提交引起数据重复
            String command = this.getCommand();
            redisKey = lock(command, params);
            ApiConfigVO apiConfigVO = apiConfigService.getByCommand(command);
            if (apiConfigVO == null) {
                throw new BusinessException("任务[" + command + "]对应的同步配置为空，请检查配置!");
            }
            String body = callApi(apiConfigVO, params);
            T t = convertData(body);
            return handleBody(apiConfigVO, params, t);
        } finally {
            // 释放锁
            if (StringUtils.isNotBlank(redisKey)) {
                this.redisUtil.delete(redisKey);
            }
        }
    }

    /**
     * 获取Redis的键，如果不需要锁定，则键为空
     *
     * @param command 指令
     * @param params  请求参数
     * @return java.lang.String
     */
    protected String lock(String command, Map<String, Object> params) {
        if (this instanceof TokenHandler) {
            return null;
        }
        String redisKey = "bpim:external:api:" + encodeData(command, params);
        boolean locked = this.redisUtil.setIfAbsent(redisKey, redisKey, 120, TimeUnit.MINUTES);
        if (!locked) {
            throw new BusinessException("接口重复请求!");
        }
        return redisKey;
    }

    public static final String PARAM_SCENARIO = "scenario";

    /**
     * 构建数据键值
     *
     * @param params 请求参数
     * @return java.lang.String
     */
    private String encodeData(String command, Map<String, Object> params) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(command);
        stringBuilder.append("#");
        // 处理其他参数
        if (Objects.nonNull(params)) {
            if (params.containsKey(PARAM_SCENARIO)) {
                String scenario = (String) params.get(PARAM_SCENARIO);
                stringBuilder.append(scenario);
                stringBuilder.append("#");
            }
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!PARAM_SCENARIO.equals(entry.getKey())) {
                    stringBuilder.append(entry.getKey()).append("@").append(entry.getValue());
                    stringBuilder.append("#");
                }
            }
        }
        String md5Data = SecureUtil.md5(stringBuilder.toString());
        log.info("源数据:{},MD5运算后数据:{}", stringBuilder, md5Data);
        return md5Data;
    }

}