package com.yhl.scp.dcp.externalApi.handler.mdm;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.part.vo.PartRelationMDMMapVO;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * description:获取MDM零件映射
 * author：李杰
 * email: <EMAIL>
 * date: 2024/10/30
 */
@Slf4j
@Component("partRelationMappingHandler4MDM")
public class PartRelationMappingHandler extends SyncDataHandler<List<PartRelationMDMMapVO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<PartRelationMDMMapVO> partRelationMDMMapVOS) {
        //TODO:后台未返回lastUpdate时间，将发送的截至时间暂时当做下一次的同步时间
        Date lastUpdateDate = partRelationMDMMapVOS.stream().map(PartRelationMDMMapVO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            if (log.isInfoEnabled()) {
                log.info("开始同步MDM的零件映射数据:{},{}", apiConfigVO, params);
            }
            //获取MDM的token
            String token = authHandler.handle(MapUtil.newHashMap());
            //mdm零件映射地址接口
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String lastDateStr;
            if (Objects.nonNull(params.get("triggerType"))) {
                lastDateStr = params.get("beginTime").toString();
            } else {
                lastDateStr = this.getSyncRefValue(apiConfigVO, params);
            }
            String url = apiUri + "?token=" + token + "&lastUpdateDate=" + lastDateStr;
            if (log.isInfoEnabled()) {
                log.info("token={},apiUri={},systemNumber={},url={}", token, apiUri
                        , systemNumber, url);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() == statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
            }
            Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MDM同步零件映射失败！");
            String body = responseEntity.getBody();
            log.info("同步MDM的零件映射数据请求完成，返回数据:{}!", body);

            ErpResponse erpResponse = JSONObject.parseObject(body, ErpResponse.class);
            return JSONObject.toJSONString(erpResponse.getData());
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<PartRelationMDMMapVO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<PartRelationMDMMapVO> partRelationMDMMapVOS = JSONObject.parseArray(body, PartRelationMDMMapVO.class);
        return partRelationMDMMapVOS;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<PartRelationMDMMapVO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MDM获取零件映射数据为空");
            return null;
        }
        //对象
        List<PartRelationMapVO> arrayList = new ArrayList<>();
        //获取mdm零件映射
        for (PartRelationMDMMapVO vo : list) {
            vo.setSourceType("MDM");
            if (StringUtils.isEmpty(vo.getIsProjectChange())){
                vo.setEffectiveStartTime(null);
            }else{
                if("否".equals(vo.getIsProjectChange())){
                    vo.setEffectiveStartTime(null);
                }
            }
            try {
                PartRelationMapVO partRelationMapVO = new PartRelationMapVO();
                BeanUtils.copyProperties(vo, partRelationMapVO);
                partRelationMapVO.setStockPointId(params.get("organizeId").toString());
                arrayList.add(partRelationMapVO);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        dfpFeign.updateMDMPartMappingData(params.get("scenario").toString(), arrayList);
        if (Objects.isNull(params.get("triggerType"))) {
            this.saveSyncCtrl(apiConfigVO, params, list);
        }
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MDM.getCode(), ApiCategoryEnum.MDM_PART_MAPPING.getCode());
    }

}
