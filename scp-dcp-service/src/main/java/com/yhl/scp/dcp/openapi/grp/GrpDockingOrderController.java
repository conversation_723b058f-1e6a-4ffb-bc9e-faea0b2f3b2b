package com.yhl.scp.dcp.openapi.grp;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.oauth.BpimResponse;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderApiDTO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <code>DockingOrderController</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-24 22:03:23
 */
@RestController
@RequestMapping("/openapi/grp/dockingOrder")
@Slf4j
public class GrpDockingOrderController {

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @PostMapping("feedback")
    @PreAuthorize("hasAuthority('SCOPE_grp.write')")
    public BpimResponse feedback(@RequestParam String scenario, @RequestBody Map<String, String> requestParams) {
        if (Objects.isNull(requestParams)) {
            log.error("请求参数requestParams为空");
            return BpimResponse.builder()
                    .status("200")
                    .statusCodeValue(200)
                    .statusCode("OK")
                    .message("OK")
                    .payload(ImmutableMap.of("success", false, "code", "error.system", "message", "请求参数不能为空", "type", "error", "responseData", ""))
                    .build();
        }
        if (!requestParams.containsKey("payLoad")) {
            log.error("请求参数payLoad为空");
            return BpimResponse.builder()
                    .status("200")
                    .statusCodeValue(200)
                    .statusCode("OK")
                    .message("OK")
                    .payload(ImmutableMap.of("success", false, "code", "error.system", "message", "payLoad不能为空", "type", "error", "responseData", ""))
                    .build();
        }
        try {
            log.info("接受到来自GRP发货单回传请求，报文:{}", requestParams);
            String payLoad = requestParams.get("payLoad");
            DeliveryDockingOrderApiDTO deliveryDockingOrderApiDTO = JSONObject.parseObject(payLoad, DeliveryDockingOrderApiDTO.class);
            deliveryDockingOrderApiDTO.setSourceSystem(ApiSourceEnum.GRP.getCode());
            BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.DFP.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            String dataBaseName =mdsScenario.getData();
            if (StringUtils.isEmpty(dataBaseName)) {
                log.error("没找到对应的scenario:{}数据",scenario);
                return BpimResponse.builder()
                        .status("200")
                        .statusCodeValue(200)
                        .statusCode("OK")
                        .message("OK")
                        .payload(ImmutableMap.of("success", false, "code", "error.system", "message", "没找到对应的scenario数据", "type", "error", "responseData", ""))
                        .build();
            }
            List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
            if (!scenarios.stream().map(Scenario::getDataBaseName).filter(t -> t.equals(dataBaseName)).findAny().isPresent()) {
                log.error("scenario:{}数据不存在",dataBaseName);
                return BpimResponse.builder()
                        .status("200")
                        .statusCodeValue(200)
                        .statusCode("OK")
                        .message("OK")
                        .payload(ImmutableMap.of("success", false, "code", "error.system", "message", "scenario找不到", "type", "error", "responseData", ""))
                        .build();
            }


            BaseResponse<Void> response = dfpFeign.dockingOrderFeedback(dataBaseName, deliveryDockingOrderApiDTO);
            log.info("处理完成GRP发货单回传请求，返回报文:{}", response.getMsg());
            if (response.getSuccess()) {
                return BpimResponse.builder()
                        .status("200")
                        .statusCodeValue(200)
                        .statusCode("OK")
                        .message("OK")
                        .payload(ImmutableMap.of("success", true, "code", "OK", "message", "OK", "type", "success", "responseData", ""))
                        .build();
            } else {
                log.error("失败情况下返回错误信息:{}",response.getMsg());

                return BpimResponse.builder()
                        .status("200")
                        .statusCodeValue(200)
                        .statusCode("OK")
                        .message("OK")
                        .payload(ImmutableMap.of(
                                "success", false,
                                "code", Objects.isNull(response.getCode()) ? "500" : response.getCode(),
                                "message", Objects.isNull(response.getMsg())  ? "系统错误":response.getMsg(),
                                "type", "error"
                        ))
                        .build();
            }
        } catch (Exception e) {
            log.error("处理请求失败", e);
            e.printStackTrace();
            // 异常情况下的返回
            return BpimResponse.builder()
                    .status("200")
                    .statusCodeValue(200)
                    .statusCode("OK")
                    .message("OK")
                    .payload(ImmutableMap.of("success", false, "code", "error.system", "message", e.getMessage(), "type", "error", "responseData", ""))
                    .build();
        }
    }

}
