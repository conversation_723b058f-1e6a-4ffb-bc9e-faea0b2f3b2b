package com.yhl.scp.mds.baseResource.controller;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.baseResource.service.FyStandardResourceService;
import com.yhl.scp.mds.job.StandardResourceJob;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "控制器")
@RestController
@RequestMapping("fyStandardResource")
public class FyStandardResourceController{

    @Resource
    private FyStandardResourceService fyStandardResourceService;

    @Resource
    private StandardResourceJob standardResourceJob;

    @ApiOperation(value = "同步数据")
    @PostMapping(value = "syncData")
    public BaseResponse<Void> syncData(@RequestParam(value = "beginTime") String beginTime,
                                       @RequestParam(value = "endTime") String endTime) {
        return fyStandardResourceService.syncData(beginTime,endTime,null,null);
    }

    @ApiOperation(value = "同步数据")
    @PostMapping(value = "job")
    public BaseResponse<Void> syncData() {
        standardResourceJob.standardResourceJobHandler();
        return BaseResponse.success("数据同步成功");
    }

    @ApiOperation(value = "手动postman同步数据")
    @PostMapping(value = "syncDataByManual")
    public BaseResponse<Void> syncDataByPost(@RequestParam(value = "beginTime") String beginTime,
                                       @RequestParam(value = "endTime") String endTime) {
        return fyStandardResourceService.syncDataByPost(beginTime,endTime);
    }

}
