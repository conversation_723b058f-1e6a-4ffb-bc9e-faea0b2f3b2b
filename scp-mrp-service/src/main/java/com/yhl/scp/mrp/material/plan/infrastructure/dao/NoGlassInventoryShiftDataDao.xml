<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.NoGlassInventoryShiftDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO">
        <!--@Table mrp_no_glass_inventory_shift_data-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_plan_version_id" jdbcType="VARCHAR" property="materialPlanVersionId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="safety_stock_days_min" jdbcType="INTEGER" property="safetyStockDaysMin"/>
        <result column="safety_stock_days_standard" jdbcType="INTEGER" property="safetyStockDaysStandard"/>
        <result column="safety_stock_days_max" jdbcType="INTEGER" property="safetyStockDaysMax"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="product_factory_code" jdbcType="VARCHAR" property="productFactoryCode"/>
        <result column="vehicle_mode_code" jdbcType="VARCHAR" property="vehicleModeCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO">
        <!-- TODO -->
        <result column="material_risk_level" jdbcType="VARCHAR" property="materialRiskLevel"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_plan_version_id,product_code,product_classify,stock_point_code,safety_stock_days_min,safety_stock_days_standard,safety_stock_days_max,product_category,product_factory_code,vehicle_mode_code,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,material_risk_level,product_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
                and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockDaysMin != null">
                and safety_stock_days_min = #{params.safetyStockDaysMin,jdbcType=INTEGER}
            </if>
            <if test="params.safetyStockDaysStandard != null">
                and safety_stock_days_standard = #{params.safetyStockDaysStandard,jdbcType=INTEGER}
            </if>
            <if test="params.safetyStockDaysMax != null">
                and safety_stock_days_max = #{params.safetyStockDaysMax,jdbcType=INTEGER}
            </if>
            <if test="params.productCategory != null and params.productCategory != ''">
                and product_category = #{params.productCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
                and product_factory_code = #{params.productFactoryCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModeCode != null and params.vehicleModeCode != ''">
                and vehicle_mode_code = #{params.vehicleModeCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.userId != null and params.userId != ''">
                and creator = #{params.userId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_data
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_data
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_no_glass_inventory_shift_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_data
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询VO -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_no_glass_inventory_shift_data
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectBySpecialParams" resultMap="VOResultMap">
        SELECT
        id,
        material_plan_version_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        material_risk_level
        FROM
        v_mrp_no_glass_inventory_shift_data
        where 1 =1
        <if test="params.id != null and params.id != ''">
            and id = #{params.id,jdbcType=VARCHAR}
        </if>
        <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
            and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productClassify != null and params.productClassify != ''">
            and product_classify = #{params.productClassify,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointCode != null and params.stockPointCode != ''">
            and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="params.safetyStockDaysMin != null">
            and safety_stock_days_min = #{params.safetyStockDaysMin,jdbcType=INTEGER}
        </if>
        <if test="params.safetyStockDaysStandard != null">
            and safety_stock_days_standard = #{params.safetyStockDaysStandard,jdbcType=INTEGER}
        </if>
        <if test="params.safetyStockDaysMax != null">
            and safety_stock_days_max = #{params.safetyStockDaysMax,jdbcType=INTEGER}
        </if>
        <if test="params.productCategory != null and params.productCategory != ''">
            and product_category = #{params.productCategory,jdbcType=VARCHAR}
        </if>
        <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
            and product_factory_code = #{params.productFactoryCode,jdbcType=VARCHAR}
        </if>
        <if test="params.vehicleModeCode != null and params.vehicleModeCode != ''">
            and vehicle_mode_code = #{params.vehicleModeCode,jdbcType=VARCHAR}
        </if>
        <if test="params.userId != null and params.userId != ''">
            and find_in_set(#{params.userId, jdbcType=VARCHAR}, material_planner) > 0
        </if>
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="selectBySpecialNotDetailParams" resultMap="VOResultMap">
        SELECT
        id,
        material_plan_version_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        material_risk_level
        FROM
        v_mrp_no_glass_inventory_shift_data
        where 1 =1
        <if test="params.id != null and params.id != ''">
            and id = #{params.id,jdbcType=VARCHAR}
        </if>
        <if test="params.materialPlanVersionId != null and params.materialPlanVersionId != ''">
            and material_plan_version_id = #{params.materialPlanVersionId,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productClassify != null and params.productClassify != ''">
            and product_classify = #{params.productClassify,jdbcType=VARCHAR}
        </if>
        <if test="params.stockPointCode != null and params.stockPointCode != ''">
            and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="params.safetyStockDaysMin != null">
            and safety_stock_days_min = #{params.safetyStockDaysMin,jdbcType=INTEGER}
        </if>
        <if test="params.safetyStockDaysStandard != null">
            and safety_stock_days_standard = #{params.safetyStockDaysStandard,jdbcType=INTEGER}
        </if>
        <if test="params.safetyStockDaysMax != null">
            and safety_stock_days_max = #{params.safetyStockDaysMax,jdbcType=INTEGER}
        </if>
        <if test="params.productCategory != null and params.productCategory != ''">
            and product_category = #{params.productCategory,jdbcType=VARCHAR}
        </if>
        <if test="params.productFactoryCode != null and params.productFactoryCode != ''">
            and product_factory_code like concat('%', #{params.productFactoryCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="params.vehicleModeCode != null and params.vehicleModeCode != ''">
            and vehicle_mode_code like concat('%', #{params.vehicleModeCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="params.productName != null and params.productName != ''">
            and product_name like concat('%', #{params.productName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="params.userId != null and params.userId != ''">
            and find_in_set(#{params.userId, jdbcType=VARCHAR}, material_planner) > 0
        </if>
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getLastCreateTime" resultType="java.util.Date">
        select
        create_time
        from mrp_no_glass_inventory_shift_data
        where product_code in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
        limit 1
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_no_glass_inventory_shift_data(
        id,
        material_plan_version_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialPlanVersionId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productClassify,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{safetyStockDaysMin,jdbcType=INTEGER},
        #{safetyStockDaysStandard,jdbcType=INTEGER},
        #{safetyStockDaysMax,jdbcType=INTEGER},
        #{productCategory,jdbcType=VARCHAR},
        #{productFactoryCode,jdbcType=VARCHAR},
        #{vehicleModeCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO">
        insert into mrp_no_glass_inventory_shift_data(id,
                                                      material_plan_version_id,
                                                      product_code,
                                                      product_classify,
                                                      stock_point_code,
                                                      safety_stock_days_min,
                                                      safety_stock_days_standard,
                                                      safety_stock_days_max,
                                                      product_category,
                                                      product_factory_code,
                                                      vehicle_mode_code,
                                                      remark,
                                                      enabled,
                                                      creator,
                                                      create_time,
                                                      modifier,
                                                      modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{materialPlanVersionId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productClassify,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{safetyStockDaysMin,jdbcType=INTEGER},
                #{safetyStockDaysStandard,jdbcType=INTEGER},
                #{safetyStockDaysMax,jdbcType=INTEGER},
                #{productCategory,jdbcType=VARCHAR},
                #{productFactoryCode,jdbcType=VARCHAR},
                #{vehicleModeCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_data(
        id,
        material_plan_version_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=INTEGER},
            #{entity.safetyStockDaysStandard,jdbcType=INTEGER},
            #{entity.safetyStockDaysMax,jdbcType=INTEGER},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModeCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_data(
        id,
        material_plan_version_id,
        product_code,
        product_classify,
        stock_point_code,
        safety_stock_days_min,
        safety_stock_days_standard,
        safety_stock_days_max,
        product_category,
        product_factory_code,
        vehicle_mode_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialPlanVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.safetyStockDaysMin,jdbcType=INTEGER},
            #{entity.safetyStockDaysStandard,jdbcType=INTEGER},
            #{entity.safetyStockDaysMax,jdbcType=INTEGER},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.productFactoryCode,jdbcType=VARCHAR},
            #{entity.vehicleModeCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO">
        update mrp_no_glass_inventory_shift_data
        set material_plan_version_id   = #{materialPlanVersionId,jdbcType=VARCHAR},
            product_code               = #{productCode,jdbcType=VARCHAR},
            product_classify           = #{productClassify,jdbcType=VARCHAR},
            stock_point_code           = #{stockPointCode,jdbcType=VARCHAR},
            safety_stock_days_min      = #{safetyStockDaysMin,jdbcType=INTEGER},
            safety_stock_days_standard = #{safetyStockDaysStandard,jdbcType=INTEGER},
            safety_stock_days_max      = #{safetyStockDaysMax,jdbcType=INTEGER},
            product_category           = #{productCategory,jdbcType=VARCHAR},
            product_factory_code       = #{productFactoryCode,jdbcType=VARCHAR},
            vehicle_mode_code          = #{vehicleModeCode,jdbcType=VARCHAR},
            remark                     = #{remark,jdbcType=VARCHAR},
            enabled                    = #{enabled,jdbcType=VARCHAR},
            modifier                   = #{modifier,jdbcType=VARCHAR},
            modify_time                = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO">
        update mrp_no_glass_inventory_shift_data
        <set>
            <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productClassify != null and item.productClassify != ''">
                product_classify = #{item.productClassify,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockDaysMin != null">
                safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=INTEGER},
            </if>
            <if test="item.safetyStockDaysStandard != null">
                safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=INTEGER},
            </if>
            <if test="item.safetyStockDaysMax != null">
                safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=INTEGER},
            </if>
            <if test="item.productCategory != null and item.productCategory != ''">
                product_category = #{item.productCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModeCode != null and item.vehicleModeCode != ''">
                vehicle_mode_code = #{item.vehicleModeCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_no_glass_inventory_shift_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_plan_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_classify = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productClassify,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMin,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysStandard,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="safety_stock_days_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockDaysMax,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="product_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_factory_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productFactoryCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_mode_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModeCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_no_glass_inventory_shift_data
            <set>
                <if test="item.materialPlanVersionId != null and item.materialPlanVersionId != ''">
                    material_plan_version_id = #{item.materialPlanVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productClassify != null and item.productClassify != ''">
                    product_classify = #{item.productClassify,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockDaysMin != null">
                    safety_stock_days_min = #{item.safetyStockDaysMin,jdbcType=INTEGER},
                </if>
                <if test="item.safetyStockDaysStandard != null">
                    safety_stock_days_standard = #{item.safetyStockDaysStandard,jdbcType=INTEGER},
                </if>
                <if test="item.safetyStockDaysMax != null">
                    safety_stock_days_max = #{item.safetyStockDaysMax,jdbcType=INTEGER},
                </if>
                <if test="item.productCategory != null and item.productCategory != ''">
                    product_category = #{item.productCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.productFactoryCode != null and item.productFactoryCode != ''">
                    product_factory_code = #{item.productFactoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModeCode != null and item.vehicleModeCode != ''">
                    vehicle_mode_code = #{item.vehicleModeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_no_glass_inventory_shift_data
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_no_glass_inventory_shift_data where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
