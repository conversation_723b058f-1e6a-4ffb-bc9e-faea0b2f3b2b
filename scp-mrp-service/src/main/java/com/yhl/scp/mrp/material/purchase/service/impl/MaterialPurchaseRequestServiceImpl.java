package com.yhl.scp.mrp.material.purchase.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPrQuery;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedDTO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import com.yhl.scp.mrp.supplier.dto.MaterialArrivalTrackingBasicDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialPurchaseRequestConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseRequestDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialPurchaseRequestDomainService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequestDTO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementDetailDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseRequestDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequestPO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPurchaseRequirementDetailVO;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>MaterialPurchaseRequestServiceImpl</code>
 * <p>
 * ERP材料采购申请单应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 10:19:36
 */
@Slf4j
@Service
public class MaterialPurchaseRequestServiceImpl extends AbstractService implements MaterialPurchaseRequestService {

    @Resource
    private MaterialPurchaseRequestDao materialPurchaseRequestDao;

    @Resource
    private MaterialPurchaseRequestDomainService materialPurchaseRequestDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MaterialPurchaseRequirementDetailService materialPurchaseRequirementDetailService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private PurchaseOrderInfoService purchaseOrderInfoService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    public BaseResponse<Void> doCreate(MaterialPurchaseRequestDTO materialPurchaseRequestDTO) {
        // 0.数据转换
        MaterialPurchaseRequestDO materialPurchaseRequestDO = MaterialPurchaseRequestConvertor.INSTANCE.dto2Do(materialPurchaseRequestDTO);
        MaterialPurchaseRequestPO materialPurchaseRequestPO = MaterialPurchaseRequestConvertor.INSTANCE.dto2Po(materialPurchaseRequestDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseRequestDomainService.validation(materialPurchaseRequestDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseRequestPO);
        materialPurchaseRequestDao.insertWithPrimaryKey(materialPurchaseRequestPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPurchaseRequestDTO materialPurchaseRequestDTO) {
        // 0.数据转换
        MaterialPurchaseRequestDO materialPurchaseRequestDO = MaterialPurchaseRequestConvertor.INSTANCE.dto2Do(materialPurchaseRequestDTO);
        MaterialPurchaseRequestPO materialPurchaseRequestPO = MaterialPurchaseRequestConvertor.INSTANCE.dto2Po(materialPurchaseRequestDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseRequestDomainService.validation(materialPurchaseRequestDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseRequestPO);
        materialPurchaseRequestDao.update(materialPurchaseRequestPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseRequestDTO> list) {
        List<MaterialPurchaseRequestPO> newList = MaterialPurchaseRequestConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseRequestDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseRequestDTO> list) {
        List<MaterialPurchaseRequestPO> newList = MaterialPurchaseRequestConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseRequestDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialPurchaseRequestDTO> list) {
        List<MaterialPurchaseRequestPO> newList = MaterialPurchaseRequestConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseRequestDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseRequestDao.deleteBatch(idList);
        }
        return materialPurchaseRequestDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseRequestVO selectByPrimaryKey(String id) {
        MaterialPurchaseRequestPO po = materialPurchaseRequestDao.selectByPrimaryKey(id);
        return MaterialPurchaseRequestConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_request")
    public List<MaterialPurchaseRequestVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_request")
    public List<MaterialPurchaseRequestVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseRequestVO> dataList = materialPurchaseRequestDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseRequestServiceImpl target = springBeanUtils.getBean(MaterialPurchaseRequestServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseRequestVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseRequestPO> list = materialPurchaseRequestDao.selectByParams(params);
        return MaterialPurchaseRequestConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseRequestVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_PURCHASE_REQUEST.getCode();
    }

    @Override
    public List<MaterialPurchaseRequestVO> invocation(List<MaterialPurchaseRequestVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doDisposePurchaseRequestJob(Integer moveMinute) {
        log.info("同步材料采购申请单维护采购单号开始");
        //每三十分钟跑一次，每次获取40分钟前变更的数据维护到货跟踪对应数据的预计发货时间和数量
        Date startModifyTime = DateUtils.moveMinute(new Date(), moveMinute);
        List<MaterialPurchaseRequestVO> purchaseRequestList = this.selectByParams(ImmutableMap.of(
                "startModifyTime", startModifyTime));
        purchaseRequestList = purchaseRequestList.stream().filter(
                        e -> StringUtils.isNotEmpty(e.getPurchaseOrderCode())
                                && StringUtils.isNotEmpty(e.getPurchaseOrderLineCode())
                                && StringUtils.isNotEmpty(e.getPurchaseRequestCode())
                                && StringUtils.isNotEmpty(e.getPurchaseRequestLineCode())
                                && !StringUtils.equals(e.getWhetherMatches(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(purchaseRequestList)) {
            log.info("材料采购申请单维护采购单号，未获取到采购申请单数据");
            return;
        }
        // 通过PR号，PR行号分组
        Map<String, MaterialPurchaseRequestVO> purchaseRequestMap = purchaseRequestList.stream()
                .collect(Collectors.toMap(e -> e.getPurchaseRequestCode() + "_" + e.getPurchaseRequestLineCode(), each -> each, (v1, v2) -> v1));
        log.info("当前ERP申请PR号 + PR行号为{}", JSON.toJSONString(purchaseRequestMap.keySet()));
        // 通过PO号，PO行号
        Map<String, MaterialPurchaseRequestVO> purchaseRequestMap02 = purchaseRequestList.stream()
                .collect(Collectors.toMap(e -> e.getPurchaseOrderCode() + "_" + e.getPurchaseOrderLineCode(), each -> each, (v1, v2) -> v1));
        log.info("当前ERP申请PO号 + PO行号为{}", JSON.toJSONString(purchaseRequestMap02.keySet()));
        // 获取对应的材料采购需求数据
        List<MaterialPurchaseRequirementDetailVO> purchaseRequirementDetailList = materialPurchaseRequirementDetailService
                .selectVOByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "combineKeys", new ArrayList<>(purchaseRequestMap.keySet())));

        // 获取全部要货计划
        List<MaterialPlanNeedVO> materialPlanNeedVOList = materialPlanNeedService.selectAll();
        // 根据要货计划号 + 物料编码 + 需求日期分组
        Map<String, MaterialPlanNeedVO> materialPlanNeedMap = materialPlanNeedVOList.stream()
                .collect(Collectors.toMap(data -> String.join("&",
                        data.getMaterialPlanNeedNo(), data.getProductCode(), DateUtils.dateToString(data.getRequirementReleaseDate(), "yyyy-MM-dd")), Function.identity(), (v1, v2) -> v1));

        // 获取对应的采购订单
        List<PurchaseOrderInfoVO> purchaseOrderInfoVOList = purchaseOrderInfoService.selectByParams(ImmutableMap.of("combineKeys", purchaseRequestMap02.keySet()));
        // 根据采购订单号 + 采购订单行号 分组
        Map<String, PurchaseOrderInfoVO> purchaseOrderInfoMap = purchaseOrderInfoVOList.stream()
                .collect(Collectors.toMap(e -> e.getOrderHeader() + "_" + e.getOrderLine(), each -> each, (v1, v2) -> v1));
        // 维护对应的材料到货跟踪数据上的采购单号，采购单行号及到货跟踪数据上的采购单号
        List<MaterialPurchaseRequirementDetailDTO> batchRequirementList = new ArrayList<>();
        List<MaterialArrivalTrackingDTO> batchTrackingList = new ArrayList<>();
        List<MaterialPlanNeedDTO> needList = new ArrayList<>();
        List<MaterialPurchaseRequestDTO> requestList = new ArrayList<>();

        for (MaterialPurchaseRequirementDetailVO purchaseRequirementDetailVO : purchaseRequirementDetailList) {
            // 获取ERP采购申请单
            MaterialPurchaseRequestVO purchaseRequestVO = purchaseRequestMap.get(
                    purchaseRequirementDetailVO.getPurchaseRequestCode()
                            + "_" + purchaseRequirementDetailVO.getPurchaseRequestLineCode());

            if (StringUtils.isEmpty(purchaseRequestVO.getPurchaseOrderCode()) || StringUtils.isEmpty(purchaseRequestVO.getPurchaseOrderLineCode()))
                continue;

            // 根据采购订单号+采购订单行号，获取对应采购订单（新补充逻辑）
            PurchaseOrderInfoVO purchaseOrderInfoVO = purchaseOrderInfoMap.get(purchaseRequestVO.getPurchaseOrderCode() + "_" + purchaseRequestVO.getPurchaseOrderLineCode());
            // 赋值开单量
            BigDecimal qtyBilled = BigDecimal.ZERO;

            if (Objects.isNull(purchaseOrderInfoVO)) {
                log.info("{}未获取到采购订单", purchaseRequirementDetailVO.getPurchaseOrderCode() + "_" + purchaseRequirementDetailVO.getPurchaseOrderLineCode());
            } else if (StringUtils.isNotEmpty(purchaseOrderInfoVO.getNote())) {
                // 根据要货计划号 + 物料编码 + 需求日期去查找可更新的要货计划
                MaterialPlanNeedVO materialPlanNeedVO = materialPlanNeedMap.get(String.join("&",
                        purchaseOrderInfoVO.getNote(), purchaseRequirementDetailVO.getProductCode(), DateUtils.dateToString(purchaseRequirementDetailVO.getRequirementReleaseDate(), "yyyy-MM-dd")));

                log.info("{}未获取到要货计划", String.join("&",
                        purchaseOrderInfoVO.getNote(), purchaseRequirementDetailVO.getProductCode(), DateUtils.dateToString(purchaseRequirementDetailVO.getRequirementReleaseDate(), "yyyy-MM-dd")));

                if (Objects.nonNull(materialPlanNeedVO)) {
                    MaterialPlanNeedDTO needDTO = new MaterialPlanNeedDTO();
                    needDTO.setId(materialPlanNeedVO.getId());
                    // 是否加上采购订单行号
                    needDTO.setPurchaseOrderCode(purchaseOrderInfoVO.getOrderHeader());
                    needList.add(needDTO);
                }

                if (null != purchaseOrderInfoVO.getQtyBilled()) qtyBilled = purchaseOrderInfoVO.getQtyBilled();
            }

            MaterialPurchaseRequirementDetailDTO requirementUpdate = MaterialPurchaseRequirementDetailDTO
                    .builder().id(purchaseRequirementDetailVO.getId())
                    .purchaseOrderCode(purchaseRequestVO.getPurchaseOrderCode())
                    .purchaseOrderLineCode(purchaseRequestVO.getPurchaseOrderLineCode())
                    .build();

            // 到货跟踪
            MaterialArrivalTrackingDTO trackingUpdate = MaterialArrivalTrackingDTO
                    .builder().sourceId(purchaseRequirementDetailVO.getId())
                    .purchaseOrderCode(purchaseRequestVO.getPurchaseOrderCode())
                    .purchaseOrderLineCode(purchaseRequestVO.getPurchaseOrderLineCode())
                    .cancelFlag(purchaseRequestVO.getCancelFlag())
                    .closedCode(purchaseRequestVO.getClosedCode())
                    .qtyBilled(qtyBilled)
                    .build();

            // 如果ERP返回的PO数量和下发的采购需求的需求数量不一致，则更改采购需求和到货跟踪的要货数量为PO返回最新的数量
            // 采购需求
            PurchaseOrderInfoVO orderInfoVO =
                    purchaseOrderInfoMap.get(purchaseRequestVO.getPurchaseOrderCode() + "_" + purchaseRequestVO.getPurchaseOrderLineCode());
            if (Objects.nonNull(orderInfoVO) && null != orderInfoVO.getQuantity()) {
                if (purchaseRequirementDetailVO.getRequireQuantity().compareTo(orderInfoVO.getQuantity()) != 0) {
                    requirementUpdate.setRequireQuantity(orderInfoVO.getQuantity());
                    trackingUpdate.setRequireQuantity(orderInfoVO.getQuantity());
                    trackingUpdate.setWaitDeliveryQuantity(orderInfoVO.getQuantity());
                }
            }

            batchRequirementList.add(requirementUpdate);
            batchTrackingList.add(trackingUpdate);

            MaterialPurchaseRequestDTO materialPurchaseRequestDTO = new MaterialPurchaseRequestDTO();
            materialPurchaseRequestDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
            materialPurchaseRequestDTO.setId(purchaseRequestVO.getId());
            requestList.add(materialPurchaseRequestDTO);
        }
        // 更新采购需求明细 和 到货跟踪
        if (CollUtil.isNotEmpty(batchRequirementList) && CollUtil.isNotEmpty(batchTrackingList)) {
            // 修改采购需求
            materialPurchaseRequirementDetailService.doUpdateBatch(batchRequirementList);

            List<String> sourceIds = batchTrackingList.stream().map(MaterialArrivalTrackingBasicDTO::getSourceId).collect(Collectors.toList());
            List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList =
                    materialArrivalTrackingService.selectByParams(ImmutableMap.of("sourceIds", sourceIds,
                            "arrivalStatus",ArrivalStatusEnum.PLAN_PRUCHASE.getCode()));
            Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap =
                    materialArrivalTrackingVOList.stream().collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getSourceId));

            List<MaterialArrivalTrackingDTO> newBatchTrackingList = new ArrayList<>();
            for (MaterialArrivalTrackingDTO materialArrivalTrackingDTO : batchTrackingList) {
                List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS =
                        materialArrivalTrackingMap.get(materialArrivalTrackingDTO.getSourceId());

                if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOS)) {

                    for (MaterialArrivalTrackingVO materialArrivalTrackingVO : materialArrivalTrackingVOS) {
                        materialArrivalTrackingVO.setPurchaseOrderCode(materialArrivalTrackingDTO.getPurchaseOrderCode());
                        materialArrivalTrackingVO.setPurchaseOrderLineCode(materialArrivalTrackingDTO.getPurchaseOrderLineCode());
                        materialArrivalTrackingVO.setCancelFlag(materialArrivalTrackingDTO.getCancelFlag());
                        materialArrivalTrackingVO.setClosedCode(materialArrivalTrackingDTO.getClosedCode());
                        materialArrivalTrackingVO.setQtyBilled(materialArrivalTrackingDTO.getQtyBilled());
                        materialArrivalTrackingVO.setArrivalStatus(materialArrivalTrackingDTO.getArrivalStatus());
                        materialArrivalTrackingVO.setRequireQuantity(materialArrivalTrackingDTO.getRequireQuantity());
                        materialArrivalTrackingVO.setWaitDeliveryQuantity(materialArrivalTrackingDTO.getWaitDeliveryQuantity());

                        MaterialArrivalTrackingDTO dto = new MaterialArrivalTrackingDTO();
                        BeanUtils.copyProperties(materialArrivalTrackingVO,dto);
                        newBatchTrackingList.add(dto);
                    }
                }
            }

            // 修改到货跟踪
            materialArrivalTrackingService.doUpdateBatchSelective(newBatchTrackingList);
        }

        // 更新要货计划的采购订单号
        if (CollectionUtils.isNotEmpty(needList)) {
            materialPlanNeedService.updateBatchSelective(needList);
        }

        // 更新采购申请单状态
        if (CollectionUtils.isNotEmpty(requestList)){
            this.doUpdateBatchSelective(requestList);
        }

        log.info("同步材料采购申请单维护采购单号开始结束");
    }

    @Override
    public void doPrCancelJob(Integer moveMinute) {
        log.info("PR取消开始");
        // 每次获取前一天
        Date startModifyTime = DateUtils.moveDay(new Date(), -1);

        List<MaterialPurchaseRequestVO> purchaseRequestList = this.selectByParams(ImmutableMap.of(
                "startModifyTime", startModifyTime));

        purchaseRequestList = purchaseRequestList.stream().filter(
                        e -> StringUtils.isNotEmpty(e.getPurchaseRequestCode())
                                && StringUtils.isNotEmpty(e.getPurchaseRequestLineCode())
                                && !StringUtils.equals(e.getWhetherMatches(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());

        // 需要取消的PR
        purchaseRequestList = purchaseRequestList.stream()
                .filter(data ->
                        (StringUtils.isNotEmpty(data.getApprovalStatus()) && !data.getApprovalStatus().equals("APPROVED") &&
                                !data.getApprovalStatus().equals("IN PROCESS")) ||
                                (StringUtils.isNotEmpty(data.getCancelFlag()) && !data.getCancelFlag().equals("NO")) ||
                                (StringUtils.isNotEmpty(data.getClosedCode()) && !data.getClosedCode().equals("OPEN")))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(purchaseRequestList)) {
            log.info("未获取到PR取消数据");
            return;
        }

        List<MaterialPurchaseRequestDTO> updateRequestList = purchaseRequestList.stream().map(data -> {
            return MaterialPurchaseRequestDTO.builder()
                    .id(data.getId())
                    .whetherMatches(YesOrNoEnum.YES.getCode())
                    .build();
        }).collect(Collectors.toList());

        // 通过PR号，PR行号分组
        Map<String, MaterialPurchaseRequestVO> purchaseRequestMap = purchaseRequestList.stream()
                .collect(Collectors.toMap(e -> e.getPurchaseRequestCode() + "_" + e.getPurchaseRequestLineCode(), each -> each, (v1, v2) -> v1));

        if (purchaseRequestMap.isEmpty()){
            return;
        }

        // 获取对应的材料到货跟踪数据
        List<MaterialArrivalTrackingVO> trackingList = materialArrivalTrackingService.selectVOByParams(
                ImmutableMap.of("combineKeys04" , new ArrayList<>(purchaseRequestMap.keySet())));

        List<MaterialArrivalTrackingDTO> updateListTracking = trackingList.stream().map(data -> {
            return MaterialArrivalTrackingDTO.builder()
                    .id(data.getId())
                    .arrivalStatus(ArrivalStatusEnum.CLOSE.getCode()).remark("PR关闭")
                    .build();
        }).collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(updateListTracking)) {
            Lists.partition(updateListTracking, 1000).forEach(materialArrivalTrackingService::doUpdateBatchSelective);
        }

        if (CollectionUtils.isNotEmpty(updateRequestList)) {
            Lists.partition(updateRequestList, 1000).forEach(this::doUpdateBatchSelective);
        }

        log.info("PR取消结束");
    }

    @Override
    public BaseResponse<Void> handleMaterialPurchaseRequest(List<ErpPrQuery> erpPrQuery) {
        if (CollectionUtils.isEmpty(erpPrQuery)) {
            return BaseResponse.success();
        }
        List<MaterialPurchaseRequestDTO> insertDtoS = new ArrayList<>();
        List<MaterialPurchaseRequestDTO> updateDtoS = new ArrayList<>();
        List<MaterialPurchaseRequestPO> oldPos = materialPurchaseRequestDao.selectByMaterialPurchaseIds(erpPrQuery);
        Map<String, MaterialPurchaseRequestPO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getOrgId() + "_" + t.getPurchaseRequestCode() + "_" + t.getPurchaseRequestLineCode()+ "_" + t.getApprovalStatus()+ "_" + t.getCancelFlag()+ "_" + t.getClosedCode(),
                        Function.identity(), (v1, v2) -> v1));
        for (ErpPrQuery erpPrQuerys : erpPrQuery) {
            MaterialPurchaseRequestDTO dto = new MaterialPurchaseRequestDTO();
            String cancelFlag =  "Y".equals(erpPrQuerys.getCancelFlag())? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
            String id =
                    erpPrQuerys.getOrgId() + "_" + erpPrQuerys.getApplicationNo() + "_" + erpPrQuerys.getLineNumber()+ "_" + erpPrQuerys.getApprovalStatus()+ "_" + cancelFlag+ "_" + erpPrQuerys.getClosedCode();
            if (oldPosMap.containsKey(id)) {
                MaterialPurchaseRequestPO oldPo = oldPosMap.get(id);
                BeanUtils.copyProperties(oldPo, dto);
                generateDto(dto, erpPrQuerys,cancelFlag);
                updateDtoS.add(dto);
            } else {
                generateDto(dto, erpPrQuerys,cancelFlag);
                insertDtoS.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncMaterialPurchaseRequest(String tenantId,String scenario) {
        List<NewStockPointVO> stockPointVOList =
                newMdsFeign.selectStockPointByParams(scenario,
                        ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),"organizeType", StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()));
        Map<String, NewStockPointVO> stockPointVOListMap = stockPointVOList.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(),
                        (value1, value2) -> value1));
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode(), "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        NewStockPointVO newStockPointVO = stockPointVOListMap.get(rangeData);
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("orgId", newStockPointVO.getOrganizeId());
        params.put("scenario", scenario);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PR_QUERY.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    private void generateDto(MaterialPurchaseRequestDTO dto, ErpPrQuery erpPrQuery,String cancel) {
        dto.setOrgId(erpPrQuery.getOrgId());
        dto.setPurchaseRequestCode(erpPrQuery.getApplicationNo());
        dto.setPurchaseRequestLineCode(erpPrQuery.getLineNumber());
        dto.setPurchaseOrderCode(erpPrQuery.getOrderNum());
        dto.setPurchaseOrderLineCode(erpPrQuery.getLineNum());
        dto.setApprovalStatus(erpPrQuery.getApprovalStatus());
        dto.setCancelFlag(cancel);
        dto.setClosedCode(erpPrQuery.getClosedCode());
        String cancelFlag = erpPrQuery.getCancelFlag() != null ? erpPrQuery.getCancelFlag() : "N";
        String closedCode = erpPrQuery.getClosedCode() != null ? erpPrQuery.getClosedCode() : "OPEN";
        boolean closeFlag = closedCode.equals("FINALLY CLOSED") || closedCode.equals("CLOSED");
        String modifiedByAgentFlag = erpPrQuery.getModifiedFlag() != null ? erpPrQuery.getModifiedFlag() : "N";

        boolean enabled = cancelFlag.equals("Y") || closeFlag
                || modifiedByAgentFlag.equals("Y");
        dto.setEnabled(enabled ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
    }
}
