package com.yhl.scp.mrp.materialDemand.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import com.yhl.scp.mps.dispatch.enums.OperationEnum;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.excel.listener.ExcelDynamicDataListener;
import com.yhl.scp.mrp.halfsubinventory.service.WarehouseHalfSubinventoryService;
import com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO;
import com.yhl.scp.mrp.material.plan.dto.TransferWarehouseDemandDTO;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.materialDemand.convertor.BigDecimalConverter;
import com.yhl.scp.mrp.materialDemand.convertor.MaterialGrossDemandConvertor;
import com.yhl.scp.mrp.materialDemand.domain.entity.MaterialGrossDemandDO;
import com.yhl.scp.mrp.materialDemand.domain.service.MaterialGrossDemandDomainService;
import com.yhl.scp.mrp.materialDemand.dto.*;
import com.yhl.scp.mrp.materialDemand.handler.MaterialGrossDemandComponentHandler;
import com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandVersionDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandBasicPO;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandPO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandHistoryService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionDetailService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandHistoryVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandSummaryVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.supplier.infrastructure.dao.MaterialSupplierPurchaseDao;
import com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialGrossDemandServiceImpl</code>
 * <p>
 * 材料计划毛需求应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:51:48
 */
@Slf4j
@Service
public class MaterialGrossDemandServiceImpl extends AbstractService implements MaterialGrossDemandService {

    @Resource
    private MaterialGrossDemandDao materialGrossDemandDao;

    @Resource
    private MaterialGrossDemandDomainService materialGrossDemandDomainService;

    @Resource
    private WarehouseHalfSubinventoryService warehouseHalfSubinventoryService;

    @Resource
    @Lazy
    private MaterialGrossDemandComponentHandler materialGrossDemandComponentHandler;

    @Resource
    private MaterialGrossDemandVersionService materialGrossDemandVersionService;

    @Resource
    private MaterialGrossDemandVersionDao materialGrossDemandVersionDao;

    @Resource
    private MaterialSupplierPurchaseDao materialSupplierPurchaseDao;

    @Resource
    private GlassSubstitutionRelationshipService glassSubstitutionRelationshipService;

    @Resource
    private MaterialGrossDemandHistoryService materialGrossDemandHistoryService;

    @Resource
    private MaterialGrossDemandVersionDetailService materialGrossDemandVersionHistoryService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private MdsFeign mdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MrpNewProductStockPointDao mrpNewProductStockPointDao;

    protected static final List<String> noGlassMaterialTypeList = Lists.newArrayList("RA.V", "BB", "BG", "BJ");

    protected static final List<String> demandProductCategory = Lists.newArrayList("RA.V", "PVB", "RA.A", "B");

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialGrossDemandDTO materialGrossDemandDTO) {
        // 0.数据转换
        MaterialGrossDemandDO materialGrossDemandDO = MaterialGrossDemandConvertor.INSTANCE.dto2Do(materialGrossDemandDTO);
        MaterialGrossDemandPO materialGrossDemandPO = MaterialGrossDemandConvertor.INSTANCE.dto2Po(materialGrossDemandDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialGrossDemandDomainService.validation(materialGrossDemandDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialGrossDemandPO);
        materialGrossDemandDao.insertWithPrimaryKey(materialGrossDemandPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialGrossDemandDTO materialGrossDemandDTO) {
        // 0.数据转换
        MaterialGrossDemandDO materialGrossDemandDO = MaterialGrossDemandConvertor.INSTANCE.dto2Do(materialGrossDemandDTO);
        MaterialGrossDemandPO materialGrossDemandPO = MaterialGrossDemandConvertor.INSTANCE.dto2Po(materialGrossDemandDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialGrossDemandDomainService.validation(materialGrossDemandDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialGrossDemandPO);
        materialGrossDemandDao.update(materialGrossDemandPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialGrossDemandDTO> list) {
        List<MaterialGrossDemandPO> newList = MaterialGrossDemandConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialGrossDemandDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialGrossDemandDTO> list) {
        List<MaterialGrossDemandPO> newList = MaterialGrossDemandConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialGrossDemandDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialGrossDemandDao.deleteBatch(idList);
        }
        return materialGrossDemandDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialGrossDemandVO selectByPrimaryKey(String id) {
        MaterialGrossDemandPO po = materialGrossDemandDao.selectByPrimaryKey(id);
        return MaterialGrossDemandConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_GROSS_DEMAND")
    public List<MaterialGrossDemandVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    public Map<String, Object> assemblyParams(MaterialGrossDemandParam materialGrossDemandParam) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(materialGrossDemandParam.getMaterialGrossDemandVersionId())) {
            params.put("materialGrossDemandVersionId", materialGrossDemandParam.getMaterialGrossDemandVersionId());
        }
        if (CollectionUtils.isNotEmpty(materialGrossDemandParam.getPlanUserProductCodeList())){
            params.put("productCodeList", materialGrossDemandParam.getPlanUserProductCodeList());
        }
        if (StringUtils.isNotBlank(materialGrossDemandParam.getProductCode())) {
            params.put("productCode", materialGrossDemandParam.getProductCode().trim());
        }
        if (StringUtils.isNotBlank(materialGrossDemandParam.getProductFactoryCode())) {
            params.put("productFactoryCode", materialGrossDemandParam.getProductFactoryCode().trim());
        }
        if (StringUtils.isNotBlank(materialGrossDemandParam.getVehicleModeCode())) {
            params.put("vehicleModeCode", materialGrossDemandParam.getVehicleModeCode().trim());
        }
        params.put("productClassify", materialGrossDemandParam.getProductClassify());
        List<String> productCategoryList = new ArrayList<>();
        if (StringUtils.isNotBlank(materialGrossDemandParam.getProductCategory())) {
            if (StringUtils.equals("OF", materialGrossDemandParam.getProductCategory())){
                materialGrossDemandParam.setProductCategory("RA.A");
            }
            productCategoryList.add(materialGrossDemandParam.getProductCategory());
        } else {
            if (StringUtils.equals(YesOrNoEnum.YES.getCode(), materialGrossDemandParam.getWhetherGlass())) {
                productCategoryList = Lists.newArrayList("RA.A");
            }
            if (StringUtils.equals(YesOrNoEnum.NO.getCode(), materialGrossDemandParam.getWhetherGlass())) {
                productCategoryList = Lists.newArrayList("PVB", "B");
            }
        }
        params.put("productCategoryList", productCategoryList);
        return params;
    }

    @Override
    public PageInfo<MaterialGrossDemandVO> selectByPage2(MaterialGrossDemandParam materialGrossDemandParam) {
        // 查询权限下的物料数据
        List<NewProductStockPointVO> newProductStockPointVOS = mrpNewProductStockPointDao
                .selectColumnVOByParams(ImmutableMap.of("materialPlanner", SystemHolder.getUserId()));
        if (CollectionUtils.isEmpty(newProductStockPointVOS)){
            return null;
        }
        List<String> planUserProductCodeList = newProductStockPointVOS.stream()
                .map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        materialGrossDemandParam.setPlanUserProductCodeList(planUserProductCodeList);
        // 组装查询查询参数
        Map<String, Object> params = this.assemblyParams(materialGrossDemandParam);
        // 分组查询材料的信息
        PageHelper.startPage(materialGrossDemandParam.getPageNum(), materialGrossDemandParam.getPageSize());
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossDemandDao.selectGroupByParams(params);
        PageInfo<MaterialGrossDemandVO> pageInfo = new PageInfo<>(materialGrossDemandVOList);

        // 查询详细数据
        List<String> productCodeList = materialGrossDemandVOList.stream()
                .map(MaterialGrossDemandVO::getProductCode).distinct()
                .collect(Collectors.toList());
        List<MaterialGrossDemandVO> list = materialGrossDemandDao.selectVOByParams(ImmutableMap.of("productCodeList", productCodeList,
                "materialGrossDemandVersionId", materialGrossDemandParam.getMaterialGrossDemandVersionId()));
        // 按照本厂编码+物料编码分组
        Map<String, List<MaterialGrossDemandVO>> grossDemandDetailGroup = list.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));

        // 本厂编码 本厂名称
        Map<String, String> productFactoryCodeMap = new HashMap<>();
        List<String> productFactoryCodeLit = list.stream().map(MaterialGrossDemandVO::getProductFactoryCode).distinct().collect(Collectors.toList());
        // 获取物料数据
        if (!grossDemandDetailGroup.isEmpty()) {
            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("product_code", "product_name"))
                    .queryParam(ImmutableMap.of("productCodeList", productFactoryCodeLit))
                    .build();
            List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
            productFactoryCodeMap = productStockPointVOList.stream()
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getProductName, (v1, v2) -> v1));
        }

        // 添加排序步骤
        List<String> dateStrList = list.stream().map(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)).distinct().sorted()
                .collect(Collectors.toList());

        for (MaterialGrossDemandVO materialGrossDemandVO : materialGrossDemandVOList) {
            List<MaterialGrossDemandVO> detailList = grossDemandDetailGroup.get(String.join("#", materialGrossDemandVO.getProductFactoryCode(), materialGrossDemandVO.getProductCode()));
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            // 获取详细数据根据日期来汇总
            Map<String, List<MaterialGrossDemandVO>> detailsGroup = detailList.stream().collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)));

            List<MaterialGrossDemandSummaryVO> detailVOList = new ArrayList<>();
            for (Map.Entry<String, List<MaterialGrossDemandVO>> entry : detailsGroup.entrySet()) {
                List<MaterialGrossDemandVO> materialGrossDemandVOS = entry.getValue();
                // 组装每天汇总数据
                MaterialGrossDemandSummaryVO materialGrossDemandSummaryVO = new MaterialGrossDemandSummaryVO();
                BigDecimal dayDemandQuantitySum = materialGrossDemandVOS.stream().map(MaterialGrossDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                materialGrossDemandSummaryVO.setDemandQuantity(dayDemandQuantitySum);
                materialGrossDemandSummaryVO.setDemandTime(entry.getKey());

                List<MaterialGrossDemandHistoryVO> childrenList = new ArrayList<>();
                for (MaterialGrossDemandVO grossDemandVO : materialGrossDemandVOS) {
                    MaterialGrossDemandHistoryVO materialGrossDemandDetailVO = new MaterialGrossDemandHistoryVO();
                    materialGrossDemandDetailVO.setId(grossDemandVO.getId());
                    materialGrossDemandDetailVO.setDemandTime(grossDemandVO.getDemandTime());
                    materialGrossDemandDetailVO.setDemandQuantity(grossDemandVO.getDemandQuantity());
                    materialGrossDemandDetailVO.setDemandSource(grossDemandVO.getDemandSource());
                    childrenList.add(materialGrossDemandDetailVO);
                }
                materialGrossDemandSummaryVO.setChildrenList(childrenList);
                detailVOList.add(materialGrossDemandSummaryVO);
            }
            materialGrossDemandVO.setProductId(detailList.get(0).getProductId());
            materialGrossDemandVO.setProductName(detailList.get(0).getProductName());
            materialGrossDemandVO.setProductCategory(detailList.get(0).getProductCategory());
            materialGrossDemandVO.setProductClassify(detailList.get(0).getProductClassify());
            materialGrossDemandVO.setProductFactoryCode(detailList.get(0).getProductFactoryCode());
            if (StringUtils.isNotEmpty(materialGrossDemandVO.getProductFactoryCode())) {
                materialGrossDemandVO.setProductFactoryName(productFactoryCodeMap.get(materialGrossDemandVO.getProductFactoryCode()));
            }
            materialGrossDemandVO.setVehicleModeCode(detailList.get(0).getVehicleModeCode());
            materialGrossDemandVO.setDataList(dateStrList);
            materialGrossDemandVO.setDetailList(detailVOList);
        }

        PageInfo<MaterialGrossDemandVO> pageInfoResult = new PageInfo<>(materialGrossDemandVOList);
        pageInfoResult.setTotal(pageInfo.getTotal());
        pageInfoResult.setPages(pageInfo.getPages());
        return pageInfoResult;
    }

    @Override
    @Expression(value = "MATERIAL_GROSS_DEMAND")
    public List<MaterialGrossDemandVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialGrossDemandVO> dataList = materialGrossDemandDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialGrossDemandServiceImpl target = SpringBeanUtils.getBean(MaterialGrossDemandServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialGrossDemandVO> selectByParams(Map<String, Object> params) {
        List<MaterialGrossDemandPO> list = materialGrossDemandDao.selectByParams(params);
        return MaterialGrossDemandConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialGrossDemandVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialGrossDemandVO> invocation(List<MaterialGrossDemandVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doComputeDemand(boolean whetherAutomatic, String scenario, String mpsDemandRule) {
        // 需求计算
        StopWatch stopWatch = new StopWatch("组装需求数据明细");
        GrossDemandContextDTO grossDemandContextDTO = new GrossDemandContextDTO();
        String userId = SystemHolder.getUserId();
        Date mrpCalcDate = DateUtils.getDayFirstTime(new Date());
        grossDemandContextDTO.setWhetherAutomatic(whetherAutomatic);
        grossDemandContextDTO.setUserId(userId);
        grossDemandContextDTO.setScenario(scenario);
        grossDemandContextDTO.setMrpCalcDate(mrpCalcDate);

        stopWatch.start("初始化物料数据");
        List<String> planUserProductCodeList = new ArrayList<>();
        // 查询物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = mrpNewProductStockPointDao.selectVOByParams(new HashMap<>());
        if (!whetherAutomatic){
            // 非定时触发
            newProductStockPointVOS.forEach(item -> {
                if (StringUtils.isNotBlank(item.getMaterialPlanner())){
                    String[] split = item.getMaterialPlanner().split(",");
                    List<String> materialPlannerList = new ArrayList<>(Arrays.asList(split));
                    if (materialPlannerList.contains(userId)){
                        planUserProductCodeList.add(item.getProductCode());
                    }
                    item.setMaterialPlannerList(materialPlannerList);
                }
            });
        }
        grossDemandContextDTO.setPlanUserProductCodeList(planUserProductCodeList);

        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        grossDemandContextDTO.setProductStockPointVOMapOfId(productStockPointVOMapOfId);
        grossDemandContextDTO.setProductStockPointVOMapOfProductCode(productStockPointVOMapOfProductCode);
        stopWatch.stop();

        stopWatch.start("初始化工艺路径数据");
        // 查询工艺路径数据
        FeignDynamicParam routingParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "product_id", "stock_point_id", "product_code", "stock_point_code"))
                .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())).build();

        List<RoutingVO> routingVOList = newMdsFeign.selectRoutingByParamOnDynamicColumns(scenario, routingParam);
        Map<String, RoutingVO> routingVOMapOfProductCode = routingVOList.stream()
                .collect(Collectors.toMap(RoutingVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        grossDemandContextDTO.setRoutingVOMapOfProductCode(routingVOMapOfProductCode);
        stopWatch.stop();

        stopWatch.start("初始化路径步骤数据");
        // 查询工艺路径步骤数据
        FeignDynamicParam routingStepParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "routing_id", "sequence_no", "next_routing_step_sequence_no", "yield"))
                .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())).build();
        List<RoutingStepVO> routingStepVOList = newMdsFeign.selectRoutingStepByParamOnDynamicColumns(scenario, routingStepParam);
        Map<String, RoutingStepVO> routingStepMapOfId = routingStepVOList.stream().collect(Collectors.toMap(RoutingStepVO::getId, Function.identity()));
        Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = routingStepVOList.stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        grossDemandContextDTO.setRoutingStepMapOfId(routingStepMapOfId);
        grossDemandContextDTO.setRoutingStepGroupOfRoutingId(routingStepGroupOfRoutingId);
        stopWatch.stop();

        stopWatch.start("初始化步骤输入数据");
        // 查询工艺路径步骤输入数据
        FeignDynamicParam routingStepInputParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "routing_step_id", "input_product_id", "yield", "scrap", "input_factor", "supply_type"))
                .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()))
                .build();
        List<RoutingStepInputVO> routingStepInputVOList = newMdsFeign.selectRoutingStepInputByParamOnDynamicColumns(scenario, routingStepInputParam);
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = routingStepInputVOList.stream()
                .collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));
        grossDemandContextDTO.setRoutingStepInputGroupOfStepId(routingStepInputGroupOfStepId);
        stopWatch.stop();

        stopWatch.start("初始化30天内已发布发货计划");
        // 查询30内的发货计划
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = initDeliveryPlanPublishedInfo(grossDemandContextDTO);
        grossDemandContextDTO.setDeliveryPlanFuture30Days(deliveryPlanFuture30Days);
        stopWatch.stop();

        // 查询最新的产能平衡数据
        CapacityBalanceVersionVO capacityBalanceVersionVO = mpsFeign.selectLatestCapacityBalanceVersionCode(grossDemandContextDTO.getScenario());
        grossDemandContextDTO.setCapacityBalanceVersionVO(capacityBalanceVersionVO);

        List<MaterialGrossDemandPO> insertMaterialGrossDemandList = new ArrayList<>();

        stopWatch.start("组装生产需求");
        // 获取生产需求生成来源
//        List<CollectionValueVO> collectionValueVOList = ipsFeign.getByCollectionCode("SOURCES_OF_DEMAND");
//        // 获取排序号为1的
//        List<CollectionValueVO> filterList = collectionValueVOList.stream()
//                .filter(item -> item.getValueSequence() == 1).collect(Collectors.toList());
//        mpsDemandRule = filterList.get(0).getCollectionValue();
        //1、计算生产需求
        if (StringUtils.equals(mpsDemandRule, "DELIVERY_PLAN_DEMAND")) {
            // 从发货计划获取
            getMpsDemandFromDeliveryPlan(insertMaterialGrossDemandList, grossDemandContextDTO);
        } else {
            // 从MPS生产计划中获取
            getMpsDemandFromMps(insertMaterialGrossDemandList, grossDemandContextDTO);
        }
        stopWatch.stop();

        //2、计算中转库需求
        stopWatch.start("组装中转库需求");
        getGradeInventoryDemand(insertMaterialGrossDemandList, grossDemandContextDTO);
        stopWatch.stop();

        //3、计算委外需求
        stopWatch.start("组装委外需求");
        getOutsourcedDemand(insertMaterialGrossDemandList, grossDemandContextDTO);
        stopWatch.stop();

        stopWatch.start("组装产能平衡需求");
        //4、计算产能平衡需求
        getMcbGrossDemand(insertMaterialGrossDemandList, grossDemandContextDTO);
        stopWatch.stop();

        stopWatch.start("持久化材料毛需求数据");

        Date noGlassEndDate = DateUtils.moveDay(new Date(), 240);
        Date glassEndDate = DateUtils.moveDay(new Date(), 60);

        if (CollectionUtils.isEmpty(insertMaterialGrossDemandList)) {
            return;
        }
        // 过滤出B、RA.A、RA.V的需求
        List<MaterialGrossDemandPO> materialGrossDemandPOS = insertMaterialGrossDemandList.stream()
                .filter(item -> demandProductCategory.contains(item.getProductCategory()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialGrossDemandPOS)) {
            return;
        }
        // 区分出材料、原片需求
        List<MaterialGrossDemandPO> noGlassMaterialGrossDemandPOS = materialGrossDemandPOS.stream()
                .filter(item -> !StringUtils.equals(item.getProductCategory(), "RA.A") &&
                        item.getDemandTime().compareTo(mrpCalcDate) >= 0 && item.getDemandTime().compareTo(noGlassEndDate) <= 0)
                .collect(Collectors.toList());

        List<MaterialGrossDemandPO> glassMaterialGrossDemandPOS = materialGrossDemandPOS.stream()
                .filter(item -> StringUtils.equals(item.getProductCategory(), "RA.A") &&
                        item.getDemandTime().compareTo(mrpCalcDate) >= 0 && item.getDemandTime().compareTo(glassEndDate) <= 0)
                .collect(Collectors.toList());

        List<MaterialGrossDemandPO> totalInsertList = new ArrayList<>();
        // 替代料计算
        // PVB、B类替代
        noGlassMaterialGrossDemandPOS = noGlassDemandInvertSubstitute(grossDemandContextDTO, noGlassMaterialGrossDemandPOS);
        // 原片的需求考虑原片替代映射
        glassMaterialGrossDemandPOS = glassDemandInvertSubstitute(grossDemandContextDTO, glassMaterialGrossDemandPOS);
        totalInsertList.addAll(noGlassMaterialGrossDemandPOS);
        totalInsertList.addAll(glassMaterialGrossDemandPOS);


        List<MaterialGrossDemandPO> insertMergeList = new ArrayList<>();
        // 汇总同物料、本厂编码、来源、时间的数据
        Map<String, List<MaterialGrossDemandPO>> collect = totalInsertList.stream()
                .collect(Collectors.groupingBy(item -> String.join("&",
                        item.getProductCode(),
                        item.getProductFactoryCode(),
                        item.getDemandSource(),
                        DateUtils.dateToString(item.getDemandTime(), "yyyy-MM-dd"))));

        for (Map.Entry<String, List<MaterialGrossDemandPO>> entry : collect.entrySet()) {
            List<MaterialGrossDemandPO> value = entry.getValue();
            MaterialGrossDemandPO materialGrossDemandPO = value.get(0);
            // 求和demandQuantity
            BigDecimal sumDemandQuantity = value.stream().map(MaterialGrossDemandPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            materialGrossDemandPO.setDemandQuantity(sumDemandQuantity);
            insertMergeList.add(materialGrossDemandPO);
        }

        // 获取最新版本
        DynamicDataSourceContextHolder.setDataSource(scenario);
        String versionCode = materialGrossDemandVersionService.getVersionCode();
        MaterialGrossDemandVersionDTO materialGrossDemandVersionDTO = new MaterialGrossDemandVersionDTO();
        materialGrossDemandVersionDTO.setId(UUID.randomUUID().toString());
        materialGrossDemandVersionDTO.setVersionCode(versionCode);

        // 删除之前生成的需求 和 需求版本
        // 需要根据权限删除
        if (whetherAutomatic){
            Lists.partition(planUserProductCodeList, 800).forEach(item -> materialGrossDemandDao.deleteByProductCodeList(item));
        }else {
            materialGrossDemandDao.deleteAll();
        }
        materialGrossDemandVersionService.deleteAll();

        // 创建需求版本
        materialGrossDemandVersionService.doCreate(materialGrossDemandVersionDTO);

        // 创建需求版本历史明细
        MaterialGrossDemandVersionHistoryDTO materialGrossDemandVersionDetailDTO = new MaterialGrossDemandVersionHistoryDTO();
        BeanUtils.copyProperties(materialGrossDemandVersionDTO, materialGrossDemandVersionDetailDTO);
        materialGrossDemandVersionHistoryService.doCreate02(materialGrossDemandVersionDetailDTO);

        // 创建需求
        if (CollectionUtils.isNotEmpty(insertMergeList)) {
            BasePOUtils.insertBatchFiller(insertMergeList);
            insertMergeList.forEach(item -> item.setMaterialGrossDemandVersionId(materialGrossDemandVersionDTO.getId()));
            Lists.partition(insertMergeList, 1000).forEach(item -> materialGrossDemandDao.insertBatchWithPrimaryKey(item));
        }

        // 创建需求历史明细
        List<MaterialGrossDemandHistoryDTO> grossDemandHistoryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(insertMergeList)) {
            noGlassMaterialGrossDemandPOS.forEach(item -> item.setMaterialGrossDemandVersionId(materialGrossDemandVersionDTO.getId()));
            // 转换历史明细
            grossDemandHistoryList = noGlassMaterialGrossDemandPOS.stream()
                    .map(data -> MaterialGrossDemandHistoryDTO.builder()
                            .materialGrossDemandVersionId(materialGrossDemandVersionDTO.getId())
                            .mainProductCode(data.getMainProductCode())
                            .productCode(data.getProductCode())
                            .productId(data.getProductId())
                            .productName(data.getProductName())
                            .productClassify(data.getProductClassify())
                            .productCategory(data.getProductCategory())
                            .productFactoryCode(data.getProductFactoryCode())
                            .vehicleModeCode(data.getVehicleModeCode())
                            .inputFactor(data.getInputFactor())
                            .demandTime(data.getDemandTime())
                            .demandQuantity(data.getDemandQuantity())
                            .unFulfillmentQuantity(data.getUnFulfillmentQuantity())
                            .operationCode(data.getOperationCode())
                            .supplyModel(data.getSupplyModel())
                            .productColor(data.getProductColor())
                            .productThickness(data.getProductThickness())
                            .demandSource(data.getDemandSource())
                            .remark(data.getRemark())
                            .enabled(data.getEnabled())
                            .id(null)
                            .build())
                    .collect(Collectors.toList());
        }
        stopWatch.stop();

        stopWatch.start("更新采购与供应商关系是否专用字段");
        if (CollectionUtils.isNotEmpty(noGlassMaterialGrossDemandPOS)) {
            calculatedSpecific(noGlassMaterialGrossDemandPOS);
        }
        stopWatch.stop();

        // 异步持久化版本明细数据
        if (CollectionUtils.isNotEmpty(grossDemandHistoryList)) {
            Executor executor = Executors.newFixedThreadPool(1);
            // 创建异步任务
            List<MaterialGrossDemandHistoryDTO> finalGrossDemandHistoryList = grossDemandHistoryList;
            CompletableFuture.runAsync(() -> {
                try {
                    if (CollectionUtils.isNotEmpty(finalGrossDemandHistoryList)) {
                        Lists.partition(finalGrossDemandHistoryList, 1000).forEach(item -> materialGrossDemandHistoryService.doCreateBatch(item));
                    }
                } catch (Exception e) {
                    log.error("毛需求创建历史版本数据异常", e);
                    throw new BusinessException("毛需求创建历史版本数据异常", e);
                }
            }, executor);
        }
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    private List<MaterialGrossDemandPO> noGlassDemandInvertSubstitute(GrossDemandContextDTO grossDemandContextDTO,
                                                                      List<MaterialGrossDemandPO> noGlassMaterialGrossDemandPOS) {
        String scenario = grossDemandContextDTO.getScenario();
        // 查询物料替代关系
        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOList = newMdsFeign.getProductSubstitutionRelationshipVOByParams(scenario, new HashMap<>());
        if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOList)) {
            return noGlassMaterialGrossDemandPOS;
        }
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        List<MaterialGrossDemandPO> result = new ArrayList<>();
        // 根据本厂编码和ERP-BOM进行分组
        Map<String, List<ProductSubstitutionRelationshipVO>> substitutionRelationshipGroup = productSubstitutionRelationshipVOList.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));

        Map<String, List<MaterialGrossDemandPO>> noGlassGrossDemandGroup = noGlassMaterialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));
        for (Map.Entry<String, List<MaterialGrossDemandPO>> entry : noGlassGrossDemandGroup.entrySet()) {
            String key = entry.getKey();
            List<MaterialGrossDemandPO> valueList = entry.getValue();
            // 获取替代数据
            List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS = substitutionRelationshipGroup.get(key);
            if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOS)) {
                result.addAll(valueList);
                continue;
            }

            // 主料优先(A), 毛需求全部给到主料，则不需要变化
            if (StringUtils.isNotBlank(productSubstitutionRelationshipVOS.get(0).getRule()) &&
                    "A".equals(productSubstitutionRelationshipVOS.get(0).getRule())) {
                result.addAll(valueList);
                continue;
            }

            // 替代料优先(B), 主料的需求给到替代料
            if (StringUtils.isNotBlank(productSubstitutionRelationshipVOS.get(0).getRule()) &&
                    "B".equals(productSubstitutionRelationshipVOS.get(0).getRule())) {
                // 优先级替代
                result.addAll(noGlassPrioritySubstitution(productStockPointVOMapOfProductCode, productSubstitutionRelationshipVOS, valueList));
                continue;
            }

            // 混合替代(3),判断混合替代标识是否为YES
            if (StringUtils.equals(productSubstitutionRelationshipVOS.get(0).getSubstitutionType(), "3")) {
                result.addAll(noGlassHybridSubstitution(productStockPointVOMapOfProductCode, productSubstitutionRelationshipVOS, valueList));
                continue;
            }

            // 共同替代(2),判断成组替代号是否为空
            if (StringUtils.equals(productSubstitutionRelationshipVOS.get(0).getSubstitutionType(), "2")) {
                result.addAll(noGlassHybridSubstitution(productStockPointVOMapOfProductCode, productSubstitutionRelationshipVOS, valueList));
                continue;
            }

            // 替代优先级(1)，优先级最低的(1)作为替代料需求
            if (StringUtils.equals(productSubstitutionRelationshipVOS.get(0).getSubstitutionType(), "1")) {
                // 优先级替代
                result.addAll(noGlassPrioritySubstitution(productStockPointVOMapOfProductCode, productSubstitutionRelationshipVOS, valueList));
            }
        }
        return result;
    }

    private List<MaterialGrossDemandPO> noGlassPrioritySubstitution(Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode,
                                                                    List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS,
                                                                    List<MaterialGrossDemandPO> valueList) {
        List<MaterialGrossDemandPO> result = new ArrayList<>();
        // 按照优先级排序，只生成优先级为1的替代料的需求，这里也可以获取最低的
        productSubstitutionRelationshipVOS = productSubstitutionRelationshipVOS.stream()
                .sorted(Comparator.comparing(ProductSubstitutionRelationshipVO::getPriority,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        ProductSubstitutionRelationshipVO substitutionRelationshipVO = productSubstitutionRelationshipVOS.get(0);
        for (MaterialGrossDemandPO materialGrossDemandPO : valueList) {
            materialGrossDemandPO.setProductCode(substitutionRelationshipVO.getSubstituteProductCode());
            materialGrossDemandPO.setProductId(null);
            materialGrossDemandPO.setProductName(null);
            if (productStockPointVOMapOfProductCode.containsKey(materialGrossDemandPO.getProductCode())) {
                materialGrossDemandPO.setProductId(productStockPointVOMapOfProductCode.get(materialGrossDemandPO.getProductCode()).getId());
                materialGrossDemandPO.setProductName(productStockPointVOMapOfProductCode.get(materialGrossDemandPO.getProductCode()).getProductName());
            }
            // 需求数量先转化为产品数量
            BigDecimal demandQuantity = materialGrossDemandPO.getDemandQuantity();
            if (null != substitutionRelationshipVO.getInputFactor() && substitutionRelationshipVO.getInputFactor().compareTo(BigDecimal.ZERO) != 0) {
                demandQuantity = materialGrossDemandPO.getDemandQuantity().divide(substitutionRelationshipVO.getInputFactor(), RoundingMode.UP);
            }
            // 需求数量 = 需求数量 * 单耗
            materialGrossDemandPO.setDemandQuantity(demandQuantity.multiply(substitutionRelationshipVO.getUnitSubstitute()).setScale(0, RoundingMode.UP));
            materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
            materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
            materialGrossDemandPO.setSubstituteInputFactor(substitutionRelationshipVO.getInputFactor());
            materialGrossDemandPO.setMainProductCode(substitutionRelationshipVO.getRawProductCode());
            result.add(materialGrossDemandPO);
        }
        return result;
    }

    private List<MaterialGrossDemandPO> noGlassHybridSubstitution(Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode,
                                                                  List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS,
                                                                  List<MaterialGrossDemandPO> materialGrossDemandPOList) {
        List<MaterialGrossDemandPO> result = new ArrayList<>();
        // 过滤出替代单耗大于0的数据
        List<ProductSubstitutionRelationshipVO> filterList = productSubstitutionRelationshipVOS.stream()
                .filter(item -> null != item.getUnitSubstitute() && item.getUnitSubstitute().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return materialGrossDemandPOList;
        }
        for (MaterialGrossDemandPO materialGrossDemandPO : materialGrossDemandPOList) {
            for (ProductSubstitutionRelationshipVO productSubstitutionRelationshipVO : filterList) {
                MaterialGrossDemandPO substitutionMaterialGrossDemandPO = materialGrossDemandDomainService.createNewMaterialGrossDemandPO(materialGrossDemandPO);
                substitutionMaterialGrossDemandPO.setProductCode(productSubstitutionRelationshipVO.getSubstituteProductCode());
                substitutionMaterialGrossDemandPO.setProductId(null);
                substitutionMaterialGrossDemandPO.setProductName(null);
                if (productStockPointVOMapOfProductCode.containsKey(substitutionMaterialGrossDemandPO.getProductCode())) {
                    substitutionMaterialGrossDemandPO.setProductId(productStockPointVOMapOfProductCode.get(substitutionMaterialGrossDemandPO.getProductCode()).getId());
                    substitutionMaterialGrossDemandPO.setProductName(productStockPointVOMapOfProductCode.get(substitutionMaterialGrossDemandPO.getProductCode()).getProductName());
                }
                substitutionMaterialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
                substitutionMaterialGrossDemandPO.setMainProductCode(productSubstitutionRelationshipVO.getRawProductCode());
                // 需求数量先转化为产品数量
                BigDecimal demandQuantity = materialGrossDemandPO.getDemandQuantity();
                if (null != productSubstitutionRelationshipVO.getInputFactor() && productSubstitutionRelationshipVO.getInputFactor().compareTo(BigDecimal.ZERO) != 0) {
                    demandQuantity = materialGrossDemandPO.getDemandQuantity().divide(productSubstitutionRelationshipVO.getInputFactor(), RoundingMode.HALF_UP);
                }
                // 需求数量 = 需求数量 * 单耗
                substitutionMaterialGrossDemandPO.setDemandQuantity(demandQuantity.multiply(productSubstitutionRelationshipVO.getUnitSubstitute()).setScale(0, RoundingMode.UP));
                substitutionMaterialGrossDemandPO.setUnFulfillmentQuantity(substitutionMaterialGrossDemandPO.getDemandQuantity());
                substitutionMaterialGrossDemandPO.setSubstituteInputFactor(productSubstitutionRelationshipVO.getUnitSubstitute());
                result.add(substitutionMaterialGrossDemandPO);
            }
        }
        return result;
    }

    private List<MaterialGrossDemandPO> glassDemandInvertSubstitute(GrossDemandContextDTO grossDemandContextDTO,
                                                                    List<MaterialGrossDemandPO> glassMaterialGrossDemandPOS) {
        // 1.查询原片替代映射
        List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOS = glassSubstitutionRelationshipService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())
        );
        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOS)) {
            return glassMaterialGrossDemandPOS;
        }

        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        List<MaterialGrossDemandPO> result = new ArrayList<>();

        // 根据本厂编码和ERP-BOM进行分组
        Map<String, List<GlassSubstitutionRelationshipVO>> substitutionRelationshipGroup = glassSubstitutionRelationshipVOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));

        Map<String, List<MaterialGrossDemandPO>> glassGrossDemandGroup = glassMaterialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));

        for (Map.Entry<String, List<MaterialGrossDemandPO>> entry : glassGrossDemandGroup.entrySet()) {
            String key = entry.getKey();
            List<MaterialGrossDemandPO> valueList = entry.getValue();
            List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList = substitutionRelationshipGroup.get(key);
            if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOList)) {
                result.addAll(valueList);
                continue;
            }
            // 按照切裁率计算
            glassSubstitutionRelationshipVOList = glassSubstitutionRelationshipVOList.stream()
                    .filter(item -> null != item.getProductionInputFactor() && BigDecimal.ZERO.compareTo(item.getProductionInputFactor()) != 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOList)) {
                result.addAll(valueList);
                continue;
            }
            glassSubstitutionRelationshipVOList.stream()
                    .sorted(Comparator.comparing(GlassSubstitutionRelationshipVO::getCuttingRate, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO = glassSubstitutionRelationshipVOList.get(0);
            for (MaterialGrossDemandPO materialGrossDemandPO : valueList) {
                materialGrossDemandPO.setProductCode(glassSubstitutionRelationshipVO.getProductionSubstituteProductCode());
                materialGrossDemandPO.setProductName(null);
                materialGrossDemandPO.setProductId(null);
                if (productStockPointVOMapOfProductCode.containsKey(materialGrossDemandPO.getProductCode())) {
                    materialGrossDemandPO.setProductId(productStockPointVOMapOfProductCode.get(materialGrossDemandPO.getProductCode()).getId());
                    materialGrossDemandPO.setProductName(productStockPointVOMapOfProductCode.get(materialGrossDemandPO.getProductCode()).getProductName());
                }
                materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
                materialGrossDemandPO.setMainProductCode(glassSubstitutionRelationshipVO.getRawProductCode());
                glassSubstitutionRelationshipVO.setProductionInputFactor(null == glassSubstitutionRelationshipVO.getProductionInputFactor() ?
                        BigDecimal.ONE : glassSubstitutionRelationshipVO.getProductionInputFactor());

                BigDecimal inputFactor = glassSubstitutionRelationshipVO.getGlassInputFactor()
                        .divide(glassSubstitutionRelationshipVO.getProductionInputFactor(), RoundingMode.UP);
                // ERP-BOM数量 * 原片单耗 / 生产单耗
                materialGrossDemandPO.setDemandQuantity(materialGrossDemandPO.getDemandQuantity().multiply(inputFactor).setScale(0, RoundingMode.UP));
                materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
                materialGrossDemandPO.setSubstituteInputFactor(glassSubstitutionRelationshipVO.getProductionInputFactor());
                result.add(materialGrossDemandPO);
            }
        }
        return result;
    }

    private List<MaterialGrossDemandPO> getHybridSubstitutionGrossDemand(MaterialGrossDemandPO glassMaterialGrossDemandPO,
                                                                         List<GlassSubstitutionRelationshipVO> relationshipVOS,
                                                                         Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        List<MaterialGrossDemandPO> result = new ArrayList<>();
        HashMap<String, BigDecimal> substituteInventoryBatchQuantityMap = new HashMap<>();
        List<String> substituteProductCodeList = relationshipVOS.stream()
                .map(GlassSubstitutionRelationshipVO::getSubstituteProductCode)
                .distinct().collect(Collectors.toList());
        substituteProductCodeList.add(glassMaterialGrossDemandPO.getProductCode());
        for (String substituteProductCode : substituteProductCodeList) {
            List<InventoryBatchDetailVO> inventoryBatchDetailList = inventoryBatchDetailMap.get(substituteProductCode);
            if (CollectionUtils.isEmpty(inventoryBatchDetailList)) {
                substituteInventoryBatchQuantityMap.put(substituteProductCode, BigDecimal.ZERO);
                continue;
            }
            // 过滤在需求日期之后的实时库存
            List<InventoryBatchDetailVO> filterList = inventoryBatchDetailList.stream()
                    .filter(item -> DateUtils.stringToDate(item.getAssignedTime(), DateUtils.COMMON_DATE_STR1).compareTo(glassMaterialGrossDemandPO.getDemandTime()) >= 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                substituteInventoryBatchQuantityMap.put(substituteProductCode, BigDecimal.ZERO);
                continue;
            }
            BigDecimal currentQuantitySum = filterList.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            substituteInventoryBatchQuantityMap.put(substituteProductCode, currentQuantitySum);
        }

        // 降序
        Map<String, BigDecimal> sortedMap = substituteInventoryBatchQuantityMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        // 根据排序后的替代产生毛需求
        BigDecimal demandQuantity = glassMaterialGrossDemandPO.getDemandQuantity();
        for (Map.Entry<String, BigDecimal> entry : sortedMap.entrySet()) {
            // 组装替代毛需求数据
            MaterialGrossDemandPO substitutionGrossDemand = assembleSubstituteMaterialGrossDemandPO(entry.getKey(), glassMaterialGrossDemandPO);
            BigDecimal supplyQuantity = entry.getValue();
            if (supplyQuantity.compareTo(demandQuantity) >= 0) {
                substitutionGrossDemand.setDemandQuantity(demandQuantity);
                substitutionGrossDemand.setUnFulfillmentQuantity(demandQuantity);
                result.add(substitutionGrossDemand);
                demandQuantity = BigDecimal.ZERO;
                break;
            }
            if (supplyQuantity.compareTo(demandQuantity) < 0) {
                substitutionGrossDemand.setDemandQuantity(supplyQuantity);
                substitutionGrossDemand.setUnFulfillmentQuantity(supplyQuantity);
                demandQuantity = demandQuantity.subtract(supplyQuantity);
            }
        }
        if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
            MaterialGrossDemandPO substitutionGrossDemand = assembleSubstituteMaterialGrossDemandPO(glassMaterialGrossDemandPO.getProductCode(), glassMaterialGrossDemandPO);
            substitutionGrossDemand.setDemandQuantity(demandQuantity);
            substitutionGrossDemand.setUnFulfillmentQuantity(demandQuantity);
            result.add(substitutionGrossDemand);
        }
        return result;
    }

    private MaterialGrossDemandPO assembleSubstituteMaterialGrossDemandPO(String productCode, MaterialGrossDemandPO originMaterialGrossDemandPO) {
        MaterialGrossDemandPO substitutionGrossDemand = new MaterialGrossDemandPO();
//            substitutionGrossDemand.setProductId();
//            substitutionGrossDemand.setProductName();
        substitutionGrossDemand.setProductCode(productCode);
        substitutionGrossDemand.setDemandTime(originMaterialGrossDemandPO.getDemandTime());
        substitutionGrossDemand.setMaterialGrossDemandVersionId(originMaterialGrossDemandPO.getMaterialGrossDemandVersionId());
        substitutionGrossDemand.setProductClassify(originMaterialGrossDemandPO.getProductClassify());
        substitutionGrossDemand.setProductCategory(originMaterialGrossDemandPO.getProductCategory());
        substitutionGrossDemand.setProductFactoryCode(originMaterialGrossDemandPO.getProductFactoryCode());
        substitutionGrossDemand.setVehicleModeCode(originMaterialGrossDemandPO.getVehicleModeCode());
        substitutionGrossDemand.setInputFactor(originMaterialGrossDemandPO.getInputFactor());
        substitutionGrossDemand.setOperationCode(originMaterialGrossDemandPO.getOperationCode());
        substitutionGrossDemand.setSupplyModel(originMaterialGrossDemandPO.getSupplyModel());
        substitutionGrossDemand.setProductColor(originMaterialGrossDemandPO.getProductColor());
        substitutionGrossDemand.setProductThickness(originMaterialGrossDemandPO.getProductThickness());
        if (StringUtils.equals(originMaterialGrossDemandPO.getProductCode(), productCode)) {
            substitutionGrossDemand.setDemandSource(originMaterialGrossDemandPO.getDemandSource());
        } else {
            substitutionGrossDemand.setDemandSource(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode());
        }
        return substitutionGrossDemand;
    }

    private void calculatedSpecific(List<MaterialGrossDemandPO> noGlassMaterialGrossDemandPOS) {
        Map<String, List<MaterialGrossDemandPO>> grossDemandGroupOfProductCode = noGlassMaterialGrossDemandPOS.stream()
                .collect(Collectors.groupingBy(MaterialGrossDemandBasicPO::getProductCode));

        List<String> materialCodeList = noGlassMaterialGrossDemandPOS.stream()
                .map(MaterialGrossDemandBasicPO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<MaterialSupplierPurchasePO> materialSupplierPurchasePOS = new ArrayList<>();
        // 分批量查询
        Lists.partition(materialCodeList, 1000).forEach(item -> materialSupplierPurchasePOS.addAll(materialSupplierPurchaseDao.selectByParams(ImmutableMap.of("materialCodeList", materialCodeList))));

        if (CollectionUtils.isNotEmpty(materialSupplierPurchasePOS)) {
            for (MaterialSupplierPurchasePO materialSupplierPurchasePO : materialSupplierPurchasePOS) {
                if (!grossDemandGroupOfProductCode.containsKey(materialSupplierPurchasePO.getMaterialCode())) {
                    continue;
                }
                List<MaterialGrossDemandPO> grossDemandVOList = grossDemandGroupOfProductCode.get(materialSupplierPurchasePO.getMaterialCode());
                List<String> productFactoryCodeList = grossDemandVOList.stream()
                        .map(MaterialGrossDemandPO::getProductFactoryCode).distinct()
                        .collect(Collectors.toList());
                if (productFactoryCodeList.size() == 1) {
                    materialSupplierPurchasePO.setSpecific(YesOrNoEnum.YES.getCode());
                } else {
                    materialSupplierPurchasePO.setSpecific(YesOrNoEnum.NO.getCode());
                }
            }
        }
        materialSupplierPurchaseDao.updateBatch(materialSupplierPurchasePOS);
    }


    private List<DeliveryPlanPublishedVO> initDeliveryPlanPublishedInfo(GrossDemandContextDTO grossDemandContextDTO) {
        Date moveDay = DateUtils.moveDay(grossDemandContextDTO.getMrpCalcDate(), 30);
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "product_code", "demand_time", "demand_quantity", "demand_version_id", "create_time", "enabled"))
                .queryParam(ImmutableMap.of("startTimeStr", DateUtils.dateToString(grossDemandContextDTO.getMrpCalcDate()),
                        "endTimeStr", DateUtils.dateToString(moveDay),
                        "enabled", YesOrNoEnum.YES.getCode()))
                .build();
        return dfpFeign.selectDeliveryPlanPublishedByParamOnDynamicColumns(grossDemandContextDTO.getScenario(), feignDynamicParam);
    }

    private void getMpsDemandFromDeliveryPlan(List<MaterialGrossDemandPO> insertGrossDemandList,
                                              GrossDemandContextDTO grossDemandContextDTO) {
        String scenario = grossDemandContextDTO.getScenario();
        // 查询mps_capacity_supply_relationship表,versionId为WEEK的, 只查询本厂的需求
        List<CapacitySupplyRelationshipVO> list = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario,
                ImmutableMap.of("versionId", "WEEK", "supplyModel", SupplyModelEnum.LOCAL.getCode()));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 根据产能供应关系进行展BOM(周产能平衡)
        for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : list) {
            // 组装周产能平衡产生的需求数据
            assembleMpsDeliveryPlanGrossDemandPo(capacitySupplyRelationshipVO, insertGrossDemandList, null, grossDemandContextDTO);
        }
    }

    private void assembleMpsDeliveryPlanGrossDemandPo(CapacitySupplyRelationshipVO capacitySupplyRelationshipVO,
                                                      List<MaterialGrossDemandPO> insertGrossDemandList,
                                                      List<Integer> stepSequenceNoList,
                                                      GrossDemandContextDTO grossDemandContextDTO) {
        String productCode = capacitySupplyRelationshipVO.getProductCode();
        String sourceProductCode = capacitySupplyRelationshipVO.getSourceProductCode();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();

        Map<String, RoutingStepVO> routingStepMapOfId = grossDemandContextDTO.getRoutingStepMapOfId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        NewProductStockPointVO bcProductStockPointVO = productStockPointVOMapOfProductCode.get(sourceProductCode);
        RoutingStepVO routingStepVO = routingStepMapOfId.get(capacitySupplyRelationshipVO.getRoutingStepId());
        // 获取输入物品
        List<RoutingStepInputVO> stepInputVOS = routingStepInputGroupOfStepId.get(capacitySupplyRelationshipVO.getRoutingStepId());
        if (CollectionUtils.isEmpty(stepInputVOS)) {
            return;
        }
        // 如果指定顺序号不为空，并且不在列表里则跳过
        if (CollectionUtils.isNotEmpty(stepSequenceNoList) && !stepSequenceNoList.contains(routingStepVO.getSequenceNo())) {
            return;
        }
        for (RoutingStepInputVO stepInputVO : stepInputVOS) {
            // 获取物品
            NewProductStockPointVO stepInputProductStockPoint = productStockPointVOMapOfId.get(stepInputVO.getInputProductId());
            if (null == stepInputProductStockPoint) {
                continue;
            }
            // 生成毛需求
            generateMpsDeliveryPlanGrossDemand(capacitySupplyRelationshipVO, stepInputProductStockPoint, bcProductStockPointVO,
                    routingStepVO, stepInputVO, insertGrossDemandList, grossDemandContextDTO);
        }
    }

    private void generateMpsDeliveryPlanGrossDemand(CapacitySupplyRelationshipVO capacitySupplyRelationshipVO,
                                                    NewProductStockPointVO stepInputProductStockPoint,
                                                    NewProductStockPointVO bcProductStockPointVO,
                                                    RoutingStepVO routingStepVO,
                                                    RoutingStepInputVO stepInputVO,
                                                    List<MaterialGrossDemandPO> insertGrossDemandList,
                                                    GrossDemandContextDTO grossDemandContextDTO) {
        // 物料类型为毛胚则往下继续拆解，或者生产计划则要一直往下拆
        if (StringUtils.equals(stepInputProductStockPoint.getProductType(), ProductTypeEnum.MPBL.getCode())) {
            // 开始拆解BOM
            recursiveDismantlingBom(capacitySupplyRelationshipVO, null, stepInputProductStockPoint,
                    bcProductStockPointVO, insertGrossDemandList, null, null,
                    grossDemandContextDTO, MrpDemandSourceEnum.MPS_DELIVERY_PLAN.getCode());
            return;
        }
        // 判断是否为采购件、或者为最后一道工序并且为推式
        if (!StringUtils.equals(ProductTypeEnum.P.getCode(), stepInputProductStockPoint.getProductType()) ||
                (StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo()) && "推式".equals(stepInputVO.getSupplyType()))) {
            return;
        }
        // 获取材料计划员下的物料
        Boolean whetherAutomatic = grossDemandContextDTO.getWhetherAutomatic();
        List<String> planUserProductCodeList = grossDemandContextDTO.getPlanUserProductCodeList();
        if (!whetherAutomatic && !planUserProductCodeList.contains(stepInputProductStockPoint.getProductCode())) {
            return;
        }
        if (null == stepInputVO.getYield()) {
            stepInputVO.setYield(BigDecimal.ONE);
        }
        if (null == stepInputVO.getInputFactor()) {
            stepInputVO.setInputFactor(BigDecimal.ONE);
        }
        String productCategory = getProductCategory(stepInputProductStockPoint.getProductClassify());
        MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
        materialGrossDemandPO.setId(UUID.randomUUID().toString());
        materialGrossDemandPO.setProductCode(stepInputProductStockPoint.getProductCode());
        materialGrossDemandPO.setProductName(stepInputProductStockPoint.getProductName());
        materialGrossDemandPO.setProductId(stepInputProductStockPoint.getId());
        materialGrossDemandPO.setProductClassify(stepInputProductStockPoint.getProductClassify());
        materialGrossDemandPO.setProductCategory(productCategory);
        materialGrossDemandPO.setProductFactoryCode(bcProductStockPointVO != null ? bcProductStockPointVO.getProductCode() : null);
        materialGrossDemandPO.setVehicleModeCode(bcProductStockPointVO != null ? bcProductStockPointVO.getVehicleModelCode() : null);
        materialGrossDemandPO.setInputFactor(stepInputVO.getInputFactor());
        materialGrossDemandPO.setDemandTime(capacitySupplyRelationshipVO.getSupplyTime());
        // 需求数量
        BigDecimal demandQuantity = capacitySupplyRelationshipVO.getSupplyQuantity().multiply(stepInputVO.getInputFactor()).divide(stepInputVO.getYield(), RoundingMode.CEILING);
        materialGrossDemandPO.setDemandQuantity(demandQuantity.setScale(0, RoundingMode.UP));
        materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
        materialGrossDemandPO.setSupplyModel(null);
        materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.MPS.getCode());
        insertGrossDemandList.add(materialGrossDemandPO);
    }

    private void recursiveDismantlingBom(CapacitySupplyRelationshipVO capacitySupplyRelationshipVO,
                                         OperationVO operationVO,
                                         NewProductStockPointVO inputProductStockPoint,
                                         NewProductStockPointVO bcProductStockPointVO,
                                         List<MaterialGrossDemandPO> insertGrossDemandList,
                                         Map<String, BigDecimal> gapDemandQuantityMap,
                                         PlanningHorizonVO planningHorizon,
                                         GrossDemandContextDTO grossDemandContextDTO,
                                         String demandSource) {
        Map<String, RoutingVO> routingVOMapOfProductCode = grossDemandContextDTO.getRoutingVOMapOfProductCode();
        Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = grossDemandContextDTO.getRoutingStepGroupOfRoutingId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        RoutingVO routingVO = routingVOMapOfProductCode.get(inputProductStockPoint.getProductCode());
        if (null == routingVO) {
            return;
        }
        List<RoutingStepVO> routingStepVOS = routingStepGroupOfRoutingId.get(routingVO.getId());
        if (CollectionUtils.isEmpty(routingStepVOS)) {
            return;
        }
        for (RoutingStepVO routingStepVO : routingStepVOS) {
            List<RoutingStepInputVO> stepInputVOS = routingStepInputGroupOfStepId.get(routingStepVO.getId());
            if (CollectionUtils.isEmpty(stepInputVOS)) {
                continue;
            }
            for (RoutingStepInputVO stepInputVO : stepInputVOS) {
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfId.get(stepInputVO.getInputProductId());
                if (null == newProductStockPointVO) {
                    continue;
                }
                // 生成生产计划-(周产能平衡)毛需求数据
                if (StringUtils.equals(MrpDemandSourceEnum.MPS_DELIVERY_PLAN.getCode(), demandSource)) {
                    generateMpsDeliveryPlanGrossDemand(capacitySupplyRelationshipVO, newProductStockPointVO, bcProductStockPointVO,
                            routingStepVO, stepInputVO, insertGrossDemandList, grossDemandContextDTO);
                }
                // 生成生产计划毛需求数据
                if (StringUtils.equals(MrpDemandSourceEnum.MPS.getCode(), demandSource)) {
                    generateMpsGrossDemand(operationVO, newProductStockPointVO, bcProductStockPointVO,
                            routingStepVO, stepInputVO, planningHorizon, insertGrossDemandList, grossDemandContextDTO);
                }
                // 生成产能平衡毛需求数据
                if (StringUtils.equals(MrpDemandSourceEnum.MCB.getCode(), demandSource)) {
                    generateMcbGrossDemand(capacitySupplyRelationshipVO, stepInputVO, newProductStockPointVO, bcProductStockPointVO,
                            gapDemandQuantityMap, insertGrossDemandList, grossDemandContextDTO);
                }
            }
        }
    }

    private void getMpsDemandFromMps(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        String scenario = grossDemandContextDTO.getScenario();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();

        Map<String, RoutingStepVO> routingStepMapOfId = grossDemandContextDTO.getRoutingStepMapOfId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();
        // 获取产线组
        List<CollectionValueVO> collectionValueVOList = ipsFeign.getByCollectionCode("PRODUCTION_LINE_GROUP");
        if (CollectionUtils.isEmpty(collectionValueVOList)) {
            // 没维护产线组则获取周产能平衡数据
            getMpsDemandFromDeliveryPlan(insertGrossDemandList, grossDemandContextDTO);
            return;
        }
        List<String> lineGroupCodeList = collectionValueVOList.stream()
                .map(CollectionValueVO::getCollectionValue)
                .collect(Collectors.toList());

        // 获取产线组的物料
        List<MdsProductStockPointBaseVO> productStockPointBaseVOS = newMdsFeign
                .selectProductStockPointBasicVOByParams(scenario, ImmutableMap.of("lineGroupList", lineGroupCodeList));
        if (CollectionUtils.isEmpty(productStockPointBaseVOS)) {
            // 没有对应厂线组的数据则获取周产能平衡数据
            getMpsDemandFromDeliveryPlan(insertGrossDemandList, grossDemandContextDTO);
            return;
        }

        // 获取计划期间数据
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        // 获取物料编码,从生产计划获取
        List<String> productCodeList = productStockPointBaseVOS.stream()
                .map(MdsProductStockPointBaseVO::getProductCode)
                .distinct().collect(Collectors.toList());
        // 获取非包装工序顺号
        List<StandardStepVO> standardStepVOList = newMdsFeign.selectStandardStepAll(scenario);
        // 过滤出包装工艺
        List<StandardStepVO> wrapStandardStepList = standardStepVOList.stream()
                .filter(item -> item.getStandardStepName().equals("包装"))
                .collect(Collectors.toList());
        List<String> wrapStandardStepCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(wrapStandardStepList)) {
            wrapStandardStepCodeList = wrapStandardStepList.stream()
                    .map(item -> String.join("@", item.getStockPointCode(), item.getStandardStepCode()))
                    .collect(Collectors.toList());
        }

        // 生产计划数据
        List<OperationVO> operationVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            operationVOS = mpsFeign.selectOperationByParams(scenario, ImmutableMap.of(
                    "productCodeList", productCodeList,
                    "planStatusList", Lists.newArrayList(OperationEnum.PLANNED.getCode(), OperationEnum.STARTED.getCode()),
                    "parentIdIsNotNull", YesOrNoEnum.YES.getCode()));
        }

        List<OperationVO> mcbOperationVOList = new ArrayList<>();
        // 通过产线组在生产计划获取的需求
        if (CollectionUtils.isNotEmpty(operationVOS)) {
            // 生产计划数据
            List<String> finalWrapStandardStepCodeList = wrapStandardStepCodeList;
            // 过滤出包装工序的
            mcbOperationVOList = operationVOS.stream()
                    .filter(item -> finalWrapStandardStepCodeList.contains(String.join("@", item.getStockPointCode(), item.getRoutingStepSequenceNo().toString())))
                    .collect(Collectors.toList());

            // 过滤出不为包装工序的
            List<OperationVO> voList = operationVOS.stream()
                    .filter(item -> !finalWrapStandardStepCodeList.contains(String.join("@", item.getStockPointCode(), item.getRoutingStepSequenceNo().toString())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(voList)) {
                List<String> operationIdList = voList.stream().map(OperationVO::getId).collect(Collectors.toList());
                // 查询feedback
                List<FeedbackProductionVO> feedbackProductionVOList = mpsFeign.selectFeedbackProductionByOperationIds(scenario, operationIdList);
                Map<String, List<FeedbackProductionVO>> feedbackProductionGroup = feedbackProductionVOList.stream()
                        .collect(Collectors.groupingBy(FeedbackProductionVO::getOperationId));
                for (OperationVO operationVO : voList) {
                    String routingStepId = operationVO.getRoutingStepId();
                    RoutingStepVO routingStepVO = routingStepMapOfId.get(routingStepId);
                    List<RoutingStepInputVO> routingStepInputVOList = routingStepInputGroupOfStepId.get(routingStepId);
                    if (CollectionUtils.isEmpty(routingStepInputVOList)) {
                        continue;
                    }
                    NewProductStockPointVO bcProductStockPointVO = productStockPointVOMapOfProductCode.get(operationVO.getProductCode());
                    // 获取完工数量
                    BigDecimal reportingQuantity = BigDecimal.ZERO;
                    List<FeedbackProductionVO> feedbackProductionVOS = feedbackProductionGroup.get(operationVO.getId());
                    if (CollectionUtils.isNotEmpty(feedbackProductionVOS)) {
                        reportingQuantity = feedbackProductionVOS.stream()
                                .map(FeedbackProductionVO::getReportingQuantity)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    // 需求数量 = 工序排产数量 - 完工数量
                    BigDecimal demandQuantity = operationVO.getQuantity().subtract(reportingQuantity);
                    if (demandQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        continue;
                    }
                    operationVO.setQuantity(demandQuantity);

                    for (RoutingStepInputVO stepInputVO : routingStepInputVOList) {
                        // 获取物品
                        NewProductStockPointVO inputProductStockPoint = productStockPointVOMapOfId.get(stepInputVO.getInputProductId());
                        if (null == inputProductStockPoint) {
                            continue;
                        }
                        // 生成生产计划毛需求计划
                        generateMpsGrossDemand(operationVO, inputProductStockPoint, bcProductStockPointVO,
                                routingStepVO, stepInputVO, planningHorizon, insertGrossDemandList, grossDemandContextDTO);
                    }
                }
            }
        }

        // 获取周产能平衡数据
        // 查询mps_capacity_supply_relationship表,versionId为WEEK的, 只查询本厂的需求
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario,
                ImmutableMap.of("versionId", "WEEK", "supplyModel", SupplyModelEnum.LOCAL.getCode()));

        Map<String, List<Integer>> mcbStepSequenceNoOfProductCode = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mcbOperationVOList)) {
            Map<String, List<OperationVO>> mcbOperationGroup = mcbOperationVOList.stream()
                    .collect(Collectors.groupingBy(OperationVO::getProductCode));
            for (Map.Entry<String, List<OperationVO>> entry : mcbOperationGroup.entrySet()) {
                productCodeList.remove(entry.getKey());
                List<OperationVO> value = entry.getValue();
                List<Integer> stepSequenceNoList = value.stream().map(OperationVO::getRoutingStepSequenceNo).collect(Collectors.toList());
                mcbStepSequenceNoOfProductCode.put(entry.getKey(), stepSequenceNoList);
            }
        }
        // 过滤出从生产计划获取的物料数据
        List<CapacitySupplyRelationshipVO> filterCapacitySupplyRelationList = capacitySupplyRelationshipVOList.stream()
                .filter(item -> !productCodeList.contains(item.getProductCode()))
                .collect(Collectors.toList());

        // 获取周产能平衡的需求
        if (CollectionUtils.isNotEmpty(filterCapacitySupplyRelationList)) {
            // 产能平衡数据
            for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : filterCapacitySupplyRelationList) {
                List<Integer> stepSequenceNoList = mcbStepSequenceNoOfProductCode.get(capacitySupplyRelationshipVO.getProductCode());
                assembleMpsDeliveryPlanGrossDemandPo(capacitySupplyRelationshipVO, insertGrossDemandList, stepSequenceNoList, grossDemandContextDTO);
            }
        }

    }

    private void generateMpsGrossDemand(OperationVO operationVO,
                                        NewProductStockPointVO inputProductStockPoint,
                                        NewProductStockPointVO bcProductStockPoint,
                                        RoutingStepVO routingStepVO,
                                        RoutingStepInputVO stepInputVO,
                                        PlanningHorizonVO planningHorizon,
                                        List<MaterialGrossDemandPO> insertGrossDemandList,
                                        GrossDemandContextDTO grossDemandContextDTO) {
        if (!StringUtils.equals(inputProductStockPoint.getProductType(), ProductTypeEnum.P.getCode())) {
            // 拆解BOM
            recursiveDismantlingBom(null, operationVO, inputProductStockPoint,
                    bcProductStockPoint, insertGrossDemandList, null, planningHorizon,
                    grossDemandContextDTO, MrpDemandSourceEnum.MPS.getCode());
            return;
        }

        // 判断是否为最后一道工序
        if (StringUtils.isEmpty(routingStepVO.getNextRoutingStepSequenceNo()) && "推式".equals(stepInputVO.getSupplyType())) {
            return;
        }

        // 按照权限计算
        Boolean whetherAutomatic = grossDemandContextDTO.getWhetherAutomatic();
        List<String> planUserProductCodeList = grossDemandContextDTO.getPlanUserProductCodeList();
        if (!whetherAutomatic && !planUserProductCodeList.contains(inputProductStockPoint.getProductCode())) {
            return;
        }

        if (null == stepInputVO.getYield()) {
            stepInputVO.setYield(BigDecimal.ONE);
        }
        if (null == stepInputVO.getInputFactor()) {
            stepInputVO.setInputFactor(BigDecimal.ONE);
        }
        // 材料需求数量
        BigDecimal demandQuantity = operationVO.getQuantity().multiply(stepInputVO.getInputFactor()).divide(stepInputVO.getYield(), RoundingMode.CEILING);
        // 工序排产开始时间
        Date startTime = operationVO.getStartTime();
        // 计划期间基准时间
        Date planStartTime = planningHorizon.getPlanStartTime();
        // 需求时间 = MAX(工序排产开始时间,计划期间基准时间)
//                        Date demandTime = startTime.after(planStartTime) ? startTime : planStartTime;
        operationVO.setStartTime(startTime);
        String productCategory = getProductCategory(inputProductStockPoint.getProductClassify());
        MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
        materialGrossDemandPO.setId(UUID.randomUUID().toString());
        materialGrossDemandPO.setProductCode(inputProductStockPoint.getProductCode());
        materialGrossDemandPO.setProductName(inputProductStockPoint.getProductName());
        materialGrossDemandPO.setProductId(inputProductStockPoint.getId());
        materialGrossDemandPO.setProductClassify(inputProductStockPoint.getProductClassify());
        materialGrossDemandPO.setProductCategory(productCategory);
        materialGrossDemandPO.setProductFactoryCode(operationVO.getProductCode());
        materialGrossDemandPO.setVehicleModeCode(null != bcProductStockPoint ? bcProductStockPoint.getVehicleModelCode() : null);
        materialGrossDemandPO.setInputFactor(null);
        materialGrossDemandPO.setDemandTime(startTime);
        materialGrossDemandPO.setDemandQuantity(demandQuantity.setScale(0, RoundingMode.UP));
        materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
        materialGrossDemandPO.setSupplyModel(null);
        materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.MPS.getCode());
        insertGrossDemandList.add(materialGrossDemandPO);
    }

    private String getProductCategory(String productClassify) {
        String productCategory;
        if (productClassify.startsWith("RA.A")) {
            productCategory = "RA.A";
        } else {
            productCategory = productClassify.startsWith("RA.V") ? "PVB" : "B";
        }
        return productCategory;
    }

    private void getOutsourcedDemand(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        // 查询委外需求
        List<OutsourceTransferDemandDetailVO> outsourceTransferDemandDetailList = mpsFeign.selectOutsourceTransferDemandDetailByParams(grossDemandContextDTO.getScenario(),
                ImmutableMap.of("requiredMaterials", YesOrNoEnum.YES.getCode()));

        if (CollectionUtils.isEmpty(outsourceTransferDemandDetailList)) {
            return;
        }
        for (OutsourceTransferDemandDetailVO outsourceTransferDemandDetailVO : outsourceTransferDemandDetailList) {
            Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
            // 获取材料物品
            NewProductStockPointVO productStockPointOfMaterial = productStockPointVOMapOfProductCode.get(outsourceTransferDemandDetailVO.getMaterialsCode());
            // 获取本厂编码物品
            NewProductStockPointVO productStockPointOfBc = productStockPointVOMapOfProductCode.get(outsourceTransferDemandDetailVO.getProductCode());

            String demandMonth = outsourceTransferDemandDetailVO.getDemandMonth();
//            // demandMonth为yyyyy-MM格式，如果是当前月设置为当前时间，不少当前月则设置为demandMonth月的第一天
//            Calendar currentCalendar = Calendar.getInstance();
//            currentCalendar.setTime(mrpCalcDate);
//            int currentYear = currentCalendar.get(Calendar.YEAR);
//            // 注意月份是从0开始的，所以需要加1
//            int currentMonth = currentCalendar.get(Calendar.MONTH) + 1;
//
//            String[] dateParts = demandMonth.split("-");
//            int demandYear = Integer.parseInt(dateParts[0]);
//            int demandMonthValue = Integer.parseInt(dateParts[1]);
//
//            Date demandDate;
//            if (currentYear == demandYear && currentMonth == demandMonthValue) {
//                demandDate = mrpCalcDate;
//            } else {
//                Calendar demandCalendar = Calendar.getInstance();
//                // 月份是从0开始的，所以需要减1
//                demandCalendar.set(demandYear, demandMonthValue - 1, 1);
//                demandDate = demandCalendar.getTime();
//            }

            // 按照权限计算
            Boolean whetherAutomatic = grossDemandContextDTO.getWhetherAutomatic();
            List<String> planUserProductCodeList = grossDemandContextDTO.getPlanUserProductCodeList();
            if (!whetherAutomatic && !planUserProductCodeList.contains(outsourceTransferDemandDetailVO.getProductCode())){
                continue;
            }

            String productCategory = getProductCategory(productStockPointOfMaterial.getProductClassify());
            MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
            materialGrossDemandPO.setId(UUID.randomUUID().toString());
            materialGrossDemandPO.setProductId(productStockPointOfMaterial.getId());
            materialGrossDemandPO.setProductCode(outsourceTransferDemandDetailVO.getMaterialsCode());
            materialGrossDemandPO.setProductName(productStockPointOfMaterial.getProductName());
            materialGrossDemandPO.setProductFactoryCode(null != productStockPointOfBc ? productStockPointOfBc.getProductCode() : null);
            materialGrossDemandPO.setProductClassify(productStockPointOfMaterial.getProductClassify());
            materialGrossDemandPO.setProductCategory(productCategory);
            materialGrossDemandPO.setVehicleModeCode(null != productStockPointOfBc ? productStockPointOfBc.getVehicleModelCode() : null);
            materialGrossDemandPO.setDemandTime(DateUtils.stringToDate(demandMonth));
            materialGrossDemandPO.setDemandQuantity(outsourceTransferDemandDetailVO.getSupplyQuantity() != null ?
                    outsourceTransferDemandDetailVO.getSupplyQuantity().setScale(0, RoundingMode.UP) : BigDecimal.ZERO);
            materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
            materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.OUTSOURCE_TRANSFER.getCode());
            insertGrossDemandList.add(materialGrossDemandPO);
        }
    }

    /**
     * 获取中转库需求
     * @return
     */
    protected void getGradeInventoryDemand(List<MaterialGrossDemandPO> insertGrossDemandList, GrossDemandContextDTO grossDemandContextDTO) {
        String scenario = grossDemandContextDTO.getScenario();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = grossDemandContextDTO.getDeliveryPlanFuture30Days();

        Map<String, RoutingVO> routingVOMapOfProductCode = grossDemandContextDTO.getRoutingVOMapOfProductCode();
        Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = grossDemandContextDTO.getRoutingStepGroupOfRoutingId();
        Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        // 查询中转库半品辅料表（不超过百条数据，查询全表）

        log.info("查询中转库半品辅料表、数据源:{}", scenario);
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<WarehouseHalfSubinventoryVO> warehouseHalfSubinventoryVOS = warehouseHalfSubinventoryService.selectAll();
        // 获取辅料子库存
        List<String> auxiliarySubInventoryList = warehouseHalfSubinventoryVOS.stream()
                .map(WarehouseHalfSubinventoryVO::getAuxiliarySubinventory)
                .distinct().collect(Collectors.toList());
        // 获取辅料货位
        List<String> auxiliaryStorageList = warehouseHalfSubinventoryVOS.stream()
                .map(WarehouseHalfSubinventoryVO::getAuxiliaryStorage)
                .distinct().collect(Collectors.toList());

        // 中转库需求定义：【本厂编码】的物品类型为成品、【工序】为末道工序、【供应方式】为推式的材料

        Map<String, Object> params = new HashMap<>();
        params.put("subinventory", "BPK");
        // 查询实时库存《半品库》
        List<InventoryBatchDetailVO> bpkInventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);

        // 查询实时库存《原材料》
        params.put("subinventory", null);
        params.put("subinventoryList", auxiliarySubInventoryList);
        params.put("freightSpaceList", auxiliaryStorageList);
        List<InventoryBatchDetailVO> materialInventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);

        // 获取中转库原材料库存
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMapOfProductCode = materialInventoryBatchDetailVOList.stream()
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        // 获取最新版本的发货计划
        List<String> deliveryPlanProductCodeList = deliveryPlanFuture30Days.stream()
                .map(DeliveryPlanPublishedVO::getProductCode)
                .distinct().collect(Collectors.toList());

        log.info("材料MRP-------日需求查询结束");
        // 存放缺口数据
        Map<String, BigDecimal> gapDemandQuantityMap = new HashMap<>();
        Map<String, List<InventoryBatchDetailVO>> bpkInventoryBatchDetailVOGroup = bpkInventoryBatchDetailVOList.stream()
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        for (String deliveryPlanProductCode : deliveryPlanProductCodeList) {
            // 半品库的库存数量
            BigDecimal bpkCurrentQuantitySum = BigDecimal.ZERO;
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = bpkInventoryBatchDetailVOGroup.get(deliveryPlanProductCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                bpkCurrentQuantitySum = inventoryBatchDetailVOS.stream()
                        .map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            //【本厂编码】的物品类型为成品、【工序】为末道工序、【供应方式】为推式的材料
            // 获取工艺路径
            RoutingVO routingVO = routingVOMapOfProductCode.get(deliveryPlanProductCode);
            if (null == routingVO) {
                continue;
            }
            List<RoutingStepVO> stepVOList = routingStepGroupOfRoutingId.get(routingVO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepVOList)) {
                continue;
            }
            // 获取末道工序, 顺序号最大的
            stepVOList = stepVOList.stream().filter(item -> null != item.getSequenceNo())
                    .sorted(Comparator.comparing(RoutingStepVO::getSequenceNo).reversed())
                    .collect(Collectors.toList());

            // 判断是否末道工序
            RoutingStepVO routingStepVO = stepVOList.get(0);
            if (null == routingStepVO) {
                continue;
            }
            // 获取末道工序输入物品
            List<RoutingStepInputVO> stepInputVOSList = routingStepInputGroupOfStepId.get(routingStepVO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepInputVOSList)) {
                continue;
            }
            // 获取BOM类型为推式的，并且为采购件和B类
            List<RoutingStepInputVO> filterInputList = stepInputVOSList.stream()
                    .filter(item -> StringUtils.equals("推式", item.getSupplyType()))
                    .collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(filterInputList)) {
                continue;
            }

            // 中转库材料的需求
            for (RoutingStepInputVO routingStepInputVO : filterInputList) {
                NewProductStockPointVO inputNewProductStockPointVO = productStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                // 需要为采购件、并且是B类
                if (null == inputNewProductStockPointVO ||
                        !StringUtils.equals(inputNewProductStockPointVO.getProductType(), "P") ||
                        noGlassMaterialTypeList.stream().noneMatch(materialType -> inputNewProductStockPointVO.getProductClassify().matches(materialType + ".*"))) {
                    continue;
                }

                // 材料需求数量 = 半品库的库存数量 * 材料单耗
                BigDecimal materialDemandQuantity = bpkCurrentQuantitySum.multiply(routingStepInputVO.getInputFactor());

                // 材料现有库存数量
                BigDecimal materialInventoryQuantity = Optional.ofNullable(inventoryBatchDetailVOMapOfProductCode.get(inputNewProductStockPointVO.getProductCode()))
                        .map(list -> list.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add)).orElse(BigDecimal.ZERO);
                if (StringUtils.equals("BRA3520J", routingStepInputVO.getProductCode()) &&
                        (deliveryPlanProductCode.equals("00109LFW00006") || deliveryPlanProductCode.equals("00109LFW00001"))) {
                    log.info("{},半品库存为:{},原材料库存为:{}", deliveryPlanProductCode, materialDemandQuantity, materialInventoryQuantity);
                }
                // 判断材料的库存是否已经获取了
                if (gapDemandQuantityMap.containsKey(inputNewProductStockPointVO.getProductCode())) {
                    BigDecimal gapInventoryQuantity = gapDemandQuantityMap.get(inputNewProductStockPointVO.getProductCode());
                    if (StringUtils.equals("BRA3520J", routingStepInputVO.getProductCode()) &&
                            (deliveryPlanProductCode.equals("00109LFW00006") || deliveryPlanProductCode.equals("00109LFW00001"))) {
                        log.info("{},{},需求量为:{},库存缺口为:{}", deliveryPlanProductCode, inputNewProductStockPointVO.getProductCode(), materialDemandQuantity, gapInventoryQuantity);
                    }
                    if (gapInventoryQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        gapInventoryQuantity = gapInventoryQuantity.abs();
                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), materialDemandQuantity.subtract(gapInventoryQuantity));
                    } else if (gapInventoryQuantity.compareTo(BigDecimal.ZERO) >= 0) {
                        gapInventoryQuantity = gapInventoryQuantity.negate();
                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), materialDemandQuantity.subtract(gapInventoryQuantity));
                    }
                } else {
                    if (StringUtils.equals("BRA3520J", routingStepInputVO.getProductCode()) &&
                            (deliveryPlanProductCode.equals("00109LFW00006") || deliveryPlanProductCode.equals("00109LFW00001"))) {
                        log.info("{},{},库存缺口为:{}", deliveryPlanProductCode, inputNewProductStockPointVO.getProductCode(), materialDemandQuantity.subtract(materialInventoryQuantity));
                    }
                    // 生成缺口或者库存，负数是库存，正数是缺口
                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), materialDemandQuantity.subtract(materialInventoryQuantity));
                }

                // 获取materialDemandQuantity和materialInventoryQuantity的最小值
//                BigDecimal minDemandQuantity = materialDemandQuantity.min(materialInventoryQuantity);
//                if (dayDemandQuantitySum.compareTo(minDemandQuantity) <= 0) {
//                    // 如果原材料库存大于半品库存，作为负缺口
//                    if (materialInventoryQuantity.compareTo(bpkCurrentQuantitySum) > 0) {
//                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), materialDemandQuantity.subtract(materialInventoryQuantity));
//                    } else {
//                        // 产生库存
//                        // rawMaterialsCurrentQuantity 取绝对值
//                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), materialInventoryQuantity.abs());
//                    }
//                    // 则不产生缺口
//                    continue;
//                }

//                if (materialInventoryQuantity.compareTo(BigDecimal.ZERO) == 0) {
//                    // 产生缺口
//                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), materialDemandQuantity);
//                } else {
//                    // 缺口 = 半成品库存 - 原材料库存
//                    BigDecimal demandQuantity = materialDemandQuantity.subtract(materialInventoryQuantity);
//                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), demandQuantity);
//                }
            }
        }

        // 获取发货计划
        List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList = new ArrayList<>();
        Map<String, List<DeliveryPlanPublishedVO>> collect = deliveryPlanFuture30Days.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : collect.entrySet()) {
            String productCode = entry.getKey();
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            value.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
            // 按照时间排序
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfProductCode.get(productCode);
            if (null == newProductStockPointVO) {
                continue;
            }
            // 需要汇总所有原材料的需求
            for (DeliveryPlanPublishedVO deliveryPlanDetailVO : value) {
                // 根据发货计划拆解BOM,拆出推式的原材料需求，推式材料具体的需求时间 = 发货计划时间 - 中转提前期
                getTransitDepotsDemandData(newProductStockPointVO,
                        deliveryPlanDetailVO.getDemandQuantity(), deliveryPlanDetailVO.getDemandTime(),
                        routingVOMapOfProductCode, routingStepGroupOfRoutingId, routingStepInputGroupOfStepId,
                        productStockPointVOMapOfId, transferWarehouseDemandDTOList, grossDemandContextDTO);
            }
        }

        if (CollectionUtils.isEmpty(transferWarehouseDemandDTOList)) {
            log.info("毛需求计算，没有需要生成的中转库需求");
            return;
        }

        // 按照时间来分组
        Map<String, List<TransferWarehouseDemandDTO>> transferWarehouseDemandGroup = transferWarehouseDemandDTOList.stream()
                .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime())));

        // 获取中转库材料需求的时间
        List<String> demandDateStrList = transferWarehouseDemandDTOList.stream()
                .map(item -> DateUtils.dateToString(item.getDemandTime()))
                .distinct().sorted().collect(Collectors.toList());

        for (String demandDateStr : demandDateStrList) {
            List<TransferWarehouseDemandDTO> warehouseDemandDTOList = transferWarehouseDemandGroup.get(demandDateStr);
            if (CollectionUtils.isEmpty(warehouseDemandDTOList)) {
                continue;
            }
            for (TransferWarehouseDemandDTO transferWarehouseDemandDTO : warehouseDemandDTOList) {
                String productCode = transferWarehouseDemandDTO.getProductCode();
                String productFactoryCode = transferWarehouseDemandDTO.getProductFactoryCode();
                BigDecimal demandQuantity = transferWarehouseDemandDTO.getDemandQuantity();
                String productClassify = transferWarehouseDemandDTO.getProductClassify();
                String vehicleModeCode = transferWarehouseDemandDTO.getVehicleModeCode();
                BigDecimal inputFactor = transferWarehouseDemandDTO.getInputFactor();
                String productId = transferWarehouseDemandDTO.getProductId();
                String productName = transferWarehouseDemandDTO.getProductName();
                Date demandDate = transferWarehouseDemandDTO.getDemandTime();
                // 获取材料的缺口,负数为库存，正数为需求
                BigDecimal gapDemandQuantity = gapDemandQuantityMap.get(productCode);
                if (StringUtils.equals("BRA3520J", productCode)) {
                    log.info("材料:{}, 本厂编码:{}, 需求日期:{}, 需求量:{}, 库存缺口:{}", productCode, productFactoryCode, demandDateStr, demandQuantity, gapDemandQuantity);
                }

                if (null != gapDemandQuantity && gapDemandQuantity.compareTo(BigDecimal.ZERO) != 0) {
                    // 计算差异量
                    BigDecimal needDemandQuantity = demandQuantity.add(gapDemandQuantity);
                    if (needDemandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 生成中转库需求
                        MaterialGrossDemandPO materialGrossDemandPO = createMaterialGrossDemandDTO(productCode, productClassify, needDemandQuantity,
                                demandDate, productFactoryCode, vehicleModeCode, inputFactor, productId, productName);
                        insertGrossDemandList.add(materialGrossDemandPO);
                    }
                    // 计算新的缺口
                    if (gapDemandQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        gapDemandQuantity = gapDemandQuantity.add(demandQuantity);
                        gapDemandQuantity = gapDemandQuantity.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : gapDemandQuantity;
                    } else {
                        gapDemandQuantity = BigDecimal.ZERO;
                    }
                } else {
                    if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 生成中转库需求
                        MaterialGrossDemandPO materialGrossDemandPO = createMaterialGrossDemandDTO(productCode, productClassify, demandQuantity,
                                demandDate, productFactoryCode, vehicleModeCode, inputFactor, productId, productName);
                        insertGrossDemandList.add(materialGrossDemandPO);
                    }
                }
                gapDemandQuantityMap.put(productCode, gapDemandQuantity);
            }
        }
        grossDemandContextDTO.setGapDemandQuantityMap(gapDemandQuantityMap);
    }


    // 根据BOM拆解成品所需原材料需求数量(中转库)
    private void getTransitDepotsDemandData(NewProductStockPointVO productStockPointVO,
                                            Integer demandQuantityInt, Date demandTime,
                                            Map<String, RoutingVO> routingVOMap,
                                            Map<String, List<RoutingStepVO>> routingStepGroup,
                                            Map<String, List<RoutingStepInputVO>> routingStepInputGroup,
                                            Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId,
                                            List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList,
                                            GrossDemandContextDTO grossDemandContextDTO) {
        // 生成中转库材料需求
        getTransitDepotsDemand(productStockPointVO, routingVOMap,
                routingStepGroup, routingStepInputGroup, demandQuantityInt,
                demandTime, newProductStockPointVOMapOfId, transferWarehouseDemandDTOList, grossDemandContextDTO);
    }

    private List<RoutingStepVO> getTransitDepotsDemand(NewProductStockPointVO productStockPointVO,
                                                       Map<String, RoutingVO> routingVOMap,
                                                       Map<String, List<RoutingStepVO>> routingStepGroup,
                                                       Map<String, List<RoutingStepInputVO>> routingStepInputGroup,
                                                       Integer demandQuantityInt, Date demandDate,
                                                       Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId,
                                                       List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList,
                                                       GrossDemandContextDTO grossDemandContextDTO) {
        // 根据发货计划拆解BOM,拆出推式的原材料需求，推式材料具体的需求时间 = 发货计划时间 - 中转提前期
        RoutingVO routingVO = routingVOMap.get(productStockPointVO.getProductCode());
        if (null == routingVO) {
            return null;
        }
        // 获取路径步骤
        List<RoutingStepVO> routingStepVOS = routingStepGroup.get(routingVO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(routingStepVOS)) {
            return null;
        }
        // 获取末道步骤
        RoutingStepVO routingStepVO = routingStepVOS.stream()
                .sorted(Comparator.comparing(RoutingStepVO::getSequenceNo))
                .collect(Collectors.toList()).get(routingStepVOS.size() - 1);

        // 获取输入物品
        List<RoutingStepInputVO> stepInputVOS = routingStepInputGroup.get(routingStepVO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepInputVOS)) {
            return routingStepVOS;
        }
        // 过滤出BOM类型为推式的
        List<RoutingStepInputVO> pushInputList = stepInputVOS.stream()
                .filter(item -> StringUtils.equals("推式", item.getSupplyType())).collect(Collectors.toList());

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pushInputList)) {
            // 按照权限计算
            Boolean whetherAutomatic = grossDemandContextDTO.getWhetherAutomatic();
            List<String> planUserProductCodeList = grossDemandContextDTO.getPlanUserProductCodeList();
            for (RoutingStepInputVO routingStepInputVO : pushInputList) {
                NewProductStockPointVO inputProductStockPointVO = newProductStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                // 需要为采购件、并且是B类
                if (null == inputProductStockPointVO ||
                        !StringUtils.equals(inputProductStockPointVO.getProductType(), "P") ||
                        noGlassMaterialTypeList.stream().noneMatch(materialType -> inputProductStockPointVO.getProductClassify().matches(materialType + ".*"))) {
                    continue;
                }
                if (!whetherAutomatic && !planUserProductCodeList.contains(inputProductStockPointVO.getProductCode())) {
                    continue;
                }
                if (null == routingStepInputVO.getYield()) {
                    routingStepInputVO.setYield(BigDecimal.ONE);
                }
                if (null == routingStepInputVO.getInputFactor()) {
                    routingStepInputVO.setInputFactor(BigDecimal.ONE);
                }

                TransferWarehouseDemandDTO transferWarehouseDemandDTO = TransferWarehouseDemandDTO.builder()
                        .productId(inputProductStockPointVO.getId())
                        .productCode(inputProductStockPointVO.getProductCode())
                        .productName(inputProductStockPointVO.getProductName())
                        .productClassify(inputProductStockPointVO.getProductClassify())
                        .productFactoryCode(productStockPointVO.getProductCode())
                        .vehicleModeCode(productStockPointVO.getVehicleModelCode())
                        .inputFactor(routingStepInputVO.getInputFactor())
                        .demandTime(demandDate)
                        // 需求数量
                        .demandQuantity(new BigDecimal(demandQuantityInt).multiply(routingStepInputVO.getInputFactor()).divide(routingStepInputVO.getYield(), RoundingMode.CEILING))
                        .build();
                transferWarehouseDemandDTOList.add(transferWarehouseDemandDTO);
            }
        }
        return routingStepVOS;
    }

    protected MaterialGrossDemandPO createMaterialGrossDemandDTO(String productCode, String productClassify,
                                                                 BigDecimal demandQuantity, Date demandDate,
                                                                 String productFactoryCode, String vehicleModeCode,
                                                                 BigDecimal inputFactor, String productId, String productName) {
        String productCategory = getProductCategory(productClassify);
        MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
        materialGrossDemandPO.setId(UUID.randomUUID().toString());
        materialGrossDemandPO.setProductId(productId);
        materialGrossDemandPO.setProductCode(productCode);
        materialGrossDemandPO.setProductName(productName);
        materialGrossDemandPO.setProductClassify(productClassify);
        materialGrossDemandPO.setProductCategory(productCategory);
        materialGrossDemandPO.setDemandTime(demandDate);
        materialGrossDemandPO.setDemandQuantity(demandQuantity.setScale(0, RoundingMode.UP));
        materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
        materialGrossDemandPO.setProductFactoryCode(productFactoryCode);
        materialGrossDemandPO.setVehicleModeCode(vehicleModeCode);
        materialGrossDemandPO.setInputFactor(inputFactor);
        materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.ZZK.getCode());
        return materialGrossDemandPO;
    }

    private void getMcbGrossDemand(List<MaterialGrossDemandPO> insertGrossDemandList,
                                   GrossDemandContextDTO grossDemandContextDTO) {
        // 获取中转库的剩余供应
        Map<String, BigDecimal> gapDemandQuantityMap = grossDemandContextDTO.getGapDemandQuantityMap();
        // 获取30天已发布的发货计划
        List<DeliveryPlanPublishedVO> deliveryPlanFuture30Days = grossDemandContextDTO.getDeliveryPlanFuture30Days();
        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanPublishedGroup = deliveryPlanFuture30Days.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = new HashMap<>(deliveryPlanPublishedGroup.size());
        // 获取最晚发货计划日期往后的365天的产能平衡数据
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : deliveryPlanPublishedGroup.entrySet()) {
            String productCode = entry.getKey();
            List<DeliveryPlanPublishedVO> planPublishedVOS = entry.getValue();
            planPublishedVOS.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
            Date demandTime = planPublishedVOS.get(planPublishedVOS.size() - 1).getDemandTime();
            // 产能平衡需要从发货计划结束后的一天开始，不包含发货计划结束日期
            lastDeliveryPlanTimeMapOfProductCode.put(productCode, DateUtils.getDayFirstTime(DateUtils.moveDay(demandTime, 1)));
        }

        String scenario = grossDemandContextDTO.getScenario();
        Date mrpCalcDate = grossDemandContextDTO.getMrpCalcDate();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = grossDemandContextDTO.getProductStockPointVOMapOfId();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = grossDemandContextDTO.getProductStockPointVOMapOfProductCode();


        CapacityBalanceVersionVO capacityBalanceVersionVO = grossDemandContextDTO.getCapacityBalanceVersionVO();
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", capacityBalanceVersionVO.getVersionCode());
        params.put("supplyModel", SupplyModelEnum.LOCAL.getCode());
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = mpsFeign.selectCapacitySupplyRelationshipByParams(scenario, params);
        Map<String, List<RoutingStepInputVO>> routingStepInputVOGroupOfStepId = grossDemandContextDTO.getRoutingStepInputGroupOfStepId();

        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)) {
            return;
        }

        Map<String, List<CapacitySupplyRelationshipVO>> capacitySupplyRelationshipVoGroup = capacitySupplyRelationshipVOList.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode));

        for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> entry : capacitySupplyRelationshipVoGroup.entrySet()) {
            List<CapacitySupplyRelationshipVO> list = entry.getValue();
            String sourceProductCode = list.get(0).getSourceProductCode();
            String productCode = entry.getKey();
            List<CapacitySupplyRelationshipVO> resultList = new ArrayList<>();
            // 是否考虑跨周标识
            boolean flag = true;
            // 获取发货计划满足的时间
            Date lastDeliveryPlanTime = lastDeliveryPlanTimeMapOfProductCode.get(sourceProductCode);
            if (lastDeliveryPlanTime == null) {
                lastDeliveryPlanTime = mrpCalcDate;
                flag = false;
            }
            // 获取lastDeliveryPlanTime是周几
            int dayOfWeek = DateUtils.getDayOfWeek(lastDeliveryPlanTime);
            if (dayOfWeek != 1 && flag) {
                // 跨周，需要获取该日期的周一
                Date mondayOfWeek = getMondayOfWeek(lastDeliveryPlanTime);
                // 周一的日期 往后推7天，判断是否有跨月
                Date moveDate = DateUtils.moveDay(mondayOfWeek, 6);
                boolean crossMonth = isCrossMonth(mondayOfWeek, moveDate);
                if (crossMonth) {
                    // 跨月
                    // 获取指定日期所在月份的最后一天
                    Date lastDayOfMonth = getLastDayOfMonth(mondayOfWeek);
                    List<CapacitySupplyRelationshipVO> filterList = list.stream().filter(item ->
                            item.getForecastTime().compareTo(mondayOfWeek) >= 0 &&
                                    item.getForecastTime().compareTo(lastDayOfMonth) <= 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterList)) {
                        // 重新维护数量，需要扣除生产/周产能平衡所占用的天数
                        // 获取月产能在这段时间段内的天数
                        int daysWithBothEnds = getDaysWithBothEnds(mondayOfWeek, lastDayOfMonth);
                        // 获取生产计划在该段时间段内的天数
                        // 生产计划占用天数
                        int occupiedDays = calculateOccupiedDays(mondayOfWeek, lastDayOfMonth, lastDeliveryPlanTime);
                        if (daysWithBothEnds > 0) {
                            for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : filterList) {
                                BigDecimal demandQuantity = capacitySupplyRelationshipVO.getDemandQuantity();
                                // 生产计划占用天数 / 获取月产能在这段时间段内的天数 * demandQuantity
                                demandQuantity = BigDecimal.valueOf(occupiedDays).divide(BigDecimal.valueOf(daysWithBothEnds), 2, RoundingMode.HALF_UP)
                                        .multiply(demandQuantity);
                                capacitySupplyRelationshipVO.setDemandQuantity(demandQuantity);
                                capacitySupplyRelationshipVO.setForecastTime(lastDeliveryPlanTime);
                            }
                        }
                        resultList.addAll(filterList);
                    }
                    // 设置时间为下个月1号
                    lastDeliveryPlanTime = getFirstDayOfNextMonth(lastDeliveryPlanTime);
                } else {
                    // 不跨月
                    // 生产计划占用天数
                    int occupiedDays = calculateOccupiedDays(mondayOfWeek, moveDate, lastDeliveryPlanTime);
                    List<CapacitySupplyRelationshipVO> filterList = list.stream().filter(item ->
                            item.getForecastTime().compareTo(mondayOfWeek) >= 0 &&
                                    item.getForecastTime().compareTo(moveDate) <= 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterList)) {
                        for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : filterList) {
                            BigDecimal demandQuantity = capacitySupplyRelationshipVO.getDemandQuantity();
                            BigDecimal avgDemandQuantity = demandQuantity.divide(new BigDecimal(7), 2, RoundingMode.HALF_UP);
                            // 获取月产能计算天数
                            demandQuantity = avgDemandQuantity.multiply(BigDecimal.valueOf(7 - occupiedDays));
                            capacitySupplyRelationshipVO.setDemandQuantity(demandQuantity);
                            capacitySupplyRelationshipVO.setForecastTime(lastDeliveryPlanTime);
                        }
                        resultList.addAll(filterList);
                    }
                    // 设置时间为下周一
                    lastDeliveryPlanTime = DateUtils.moveDay(moveDate, 1);
                }
            }

            Date moveDay = DateUtils.moveDay(lastDeliveryPlanTime, 240);
            Date finalLastDeliveryPlanTime = lastDeliveryPlanTime;
            List<CapacitySupplyRelationshipVO> filterList = list.stream().filter(item ->
                    item.getForecastTime().compareTo(finalLastDeliveryPlanTime) >= 0 &&
                            item.getForecastTime().compareTo(moveDay) <= 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                resultList.addAll(filterList);
            }
            if (CollectionUtils.isEmpty(resultList)) {
                continue;
            }
            resultList = resultList.stream()
                    .collect(Collectors.collectingAndThen
                            (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CapacitySupplyRelationshipVO::getId))), ArrayList::new));
            // 按照时间排序
            resultList = resultList.stream()
                    .sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getForecastTime))
                    .collect(Collectors.toList());

            NewProductStockPointVO productStockPointOfBc = productStockPointVOMapOfProductCode.get(sourceProductCode);
            for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : resultList) {
                List<RoutingStepInputVO> stepInputVOS = routingStepInputVOGroupOfStepId.get(capacitySupplyRelationshipVO.getRoutingStepId());
                if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(stepInputVOS)) {
                    continue;
                }
                for (RoutingStepInputVO stepInputVO : stepInputVOS) {
                    NewProductStockPointVO inputStockPointVO = productStockPointVOMapOfId.get(stepInputVO.getInputProductId());
                    if (null == inputStockPointVO) {
                        continue;
                    }
                    // 生成产能平衡的需求
                    generateMcbGrossDemand(capacitySupplyRelationshipVO, stepInputVO, inputStockPointVO,
                            productStockPointOfBc, gapDemandQuantityMap, insertGrossDemandList,
                            grossDemandContextDTO);
                }
            }
        }
    }

    private void generateMcbGrossDemand(CapacitySupplyRelationshipVO capacitySupplyRelationshipVO,
                                        RoutingStepInputVO stepInputVO,
                                        NewProductStockPointVO inputStockPointVO,
                                        NewProductStockPointVO productStockPointOfBc,
                                        Map<String, BigDecimal> gapDemandQuantityMap,
                                        List<MaterialGrossDemandPO> insertGrossDemandList,
                                        GrossDemandContextDTO grossDemandContextDTO) {
        if (StringUtils.equals(inputStockPointVO.getProductType(), ProductTypeEnum.MPBL.getCode())) {
            // 开始拆解BOM
            recursiveDismantlingBom(capacitySupplyRelationshipVO, null, inputStockPointVO,
                    productStockPointOfBc, insertGrossDemandList, gapDemandQuantityMap, null,
                    grossDemandContextDTO, MrpDemandSourceEnum.MCB.getCode());
            return;
        }
        if (!StringUtils.equals(inputStockPointVO.getProductType(), ProductTypeEnum.P.getCode())) {
            return;
        }

        // 按照权限计算
        Boolean whetherAutomatic = grossDemandContextDTO.getWhetherAutomatic();
        List<String> planUserProductCodeList = grossDemandContextDTO.getPlanUserProductCodeList();
        if (!whetherAutomatic && !planUserProductCodeList.contains(inputStockPointVO.getProductCode())){
            return;
        }

        BigDecimal demandQuantity = capacitySupplyRelationshipVO.getDemandQuantity().multiply(stepInputVO.getInputFactor());
        // 应该是缺口，数量小于0则是有库存的
        BigDecimal inventoryQuantity = BigDecimal.ZERO;
        BigDecimal gapQuantity = gapDemandQuantityMap.getOrDefault(inputStockPointVO.getProductCode(), BigDecimal.ZERO);
        if (gapQuantity.compareTo(BigDecimal.ZERO) < 0) {
            inventoryQuantity = gapQuantity.abs();
        }
        if (inventoryQuantity.compareTo(BigDecimal.ZERO) > 0) {
            if (demandQuantity.compareTo(inventoryQuantity) <= 0) {
                inventoryQuantity = inventoryQuantity.subtract(demandQuantity);
                inventoryQuantity = inventoryQuantity.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : inventoryQuantity.negate();
                gapDemandQuantityMap.put(inputStockPointVO.getProductCode(), inventoryQuantity);
                return;
            } else {
                demandQuantity = demandQuantity.subtract(inventoryQuantity);
                gapDemandQuantityMap.put(inputStockPointVO.getProductCode(), BigDecimal.ZERO);
            }
        }
        String productCategory = getProductCategory(inputStockPointVO.getProductClassify());
        MaterialGrossDemandPO materialGrossDemandPO = new MaterialGrossDemandPO();
        materialGrossDemandPO.setId(UUID.randomUUID().toString());
        materialGrossDemandPO.setProductId(inputStockPointVO.getId());
        materialGrossDemandPO.setProductCode(inputStockPointVO.getProductCode());
        materialGrossDemandPO.setProductName(inputStockPointVO.getProductName());
        materialGrossDemandPO.setProductClassify(inputStockPointVO.getProductClassify());
        materialGrossDemandPO.setProductCategory(productCategory);
        materialGrossDemandPO.setProductFactoryCode(null != productStockPointOfBc ? productStockPointOfBc.getProductCode() : null);
        materialGrossDemandPO.setVehicleModeCode(null != productStockPointOfBc ? productStockPointOfBc.getVehicleModelCode() : null);
        materialGrossDemandPO.setInputFactor(stepInputVO.getInputFactor());
        materialGrossDemandPO.setDemandTime(capacitySupplyRelationshipVO.getForecastTime());
        materialGrossDemandPO.setDemandQuantity(demandQuantity.setScale(0, RoundingMode.UP));
        materialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getDemandQuantity());
        materialGrossDemandPO.setDemandSource(MrpDemandSourceEnum.MCB.getCode());
        insertGrossDemandList.add(materialGrossDemandPO);
    }

    /**
     * 获取指定日期所在月份的最后一天，并设置时间为23:59:59
     * @param date
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        // 创建Calendar实例并设置为指定日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 将日期设置为当月的最后一天
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);

        // 设置时间为23:59:59.999
        calendar.set(Calendar.HOUR_OF_DAY, 23);   // 24小时制的小时
        calendar.set(Calendar.MINUTE, 59);        // 分钟
        calendar.set(Calendar.SECOND, 59);        // 秒
        calendar.set(Calendar.MILLISECOND, 999);  // 毫秒

        return calendar.getTime();
    }

    /**
     * 获取指定日期所在周的周一（00:00:00）
     * @param date 指定日期
     * @return 该日期所在周的周一
     */
    public static Date getMondayOfWeek(Date date) {
        if (date == null) {
            return null; // 或抛出 IllegalArgumentException
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取当前日期是一周中的第几天（1=周日，2=周一，...，7=周六）
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        // 计算与本周一的差距
        int diff = (dayOfWeek == Calendar.SUNDAY) ? 6 : (dayOfWeek - 2);
        calendar.add(Calendar.DATE, -diff);

        // 设置时间为00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * 判断两个日期是否跨月
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果跨月返回true，否则返回false；如果任何日期为null也返回true
     */
    public static boolean isCrossMonth(Date date1, Date date2) {
        // 处理空值情况
        if (date1 == null || date2 == null) {
            return true;
        }

        // 创建Calendar实例
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();

        // 设置日期
        calendar1.setTime(date1);
        calendar2.setTime(date2);

        // 判断年份和月份是否相同
        int year1 = calendar1.get(Calendar.YEAR);
        int month1 = calendar1.get(Calendar.MONTH);
        int year2 = calendar2.get(Calendar.YEAR);
        int month2 = calendar2.get(Calendar.MONTH);

        // 如果年份不同，肯定跨月
        if (year1 != year2) {
            return true;
        }

        // 如果年份相同但月份不同，也是跨月
        return month1 != month2;
    }

    public static void main(String[] args) {

        List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList = new ArrayList<>();
        TransferWarehouseDemandDTO transferWarehouseDemandDTO = TransferWarehouseDemandDTO.builder()
                .demandTime(new Date())
                .productCode("AAA")
                .productFactoryCode("001")
                .demandQuantity(new BigDecimal("100"))
                .build();
        TransferWarehouseDemandDTO transferWarehouseDemandDTO2 = TransferWarehouseDemandDTO.builder()
                .demandTime(new Date())
                .productCode("AAA")
                .productFactoryCode("002")
                .demandQuantity(new BigDecimal("200"))
                .build();
        transferWarehouseDemandDTOList.add(transferWarehouseDemandDTO);
        transferWarehouseDemandDTOList.add(transferWarehouseDemandDTO2);
        Map<String, BigDecimal> gapDemandQuantityMap = new HashMap<>();

        // 原材料需求数量汇总，生成MRP中转库需求
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 按照原材料分组中转库需求
        Map<String, List<TransferWarehouseDemandDTO>> collect1 = transferWarehouseDemandDTOList.stream()
                .collect(Collectors.groupingBy(TransferWarehouseDemandDTO::getProductCode));

        for (Map.Entry<String, List<TransferWarehouseDemandDTO>> entry : collect1.entrySet()) {
            String productCode = entry.getKey();
            List<TransferWarehouseDemandDTO> value = entry.getValue();
            String productClassify = value.get(0).getProductClassify();
            String productFactoryCode = value.get(0).getProductFactoryCode();
            String vehicleModeCode = value.get(0).getVehicleModeCode();
            BigDecimal inputFactor = value.get(0).getInputFactor();
            String productId = value.get(0).getProductId();
            String productName = value.get(0).getProductName();
            // 获取原材料缺口
            BigDecimal gapDemandQuantity = gapDemandQuantityMap.get(productCode);

            // 使用Map根据demandTime分组，并累加quantity
            Map<String, BigDecimal> mergedQuantities = value.stream()
                    .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime()),
                            Collectors.reducing(BigDecimal.ZERO, TransferWarehouseDemandDTO::getDemandQuantity, BigDecimal::add)));

            // 将Map转换为List并排序
            List<Map.Entry<String, BigDecimal>> sortedEntries = new ArrayList<>(mergedQuantities.entrySet());
            sortedEntries.sort(Comparator.comparing(e -> {
                try {
                    return dateFormat.parse(e.getKey());
                } catch (ParseException ex) {
                    throw new RuntimeException(ex);
                }
            }));

            for (Map.Entry<String, BigDecimal> sortedEntry : sortedEntries) {
                String demandDate = sortedEntry.getKey();
                BigDecimal demandQuantity = sortedEntry.getValue();
                if (null != gapDemandQuantity && gapDemandQuantity.compareTo(BigDecimal.ZERO) != 0) {
                    // 计算差异量
                    BigDecimal needDemandQuantity = demandQuantity.add(gapDemandQuantity);
                    if (needDemandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 生成中转库需求
                        System.out.println("needDemandQuantity = " + needDemandQuantity);
                    }
                    // 计算新的缺口
                    if (gapDemandQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        gapDemandQuantity = gapDemandQuantity.add(demandQuantity);
                        gapDemandQuantity = gapDemandQuantity.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : gapDemandQuantity;
                    } else {
                        gapDemandQuantity = BigDecimal.ZERO;
                    }
                } else {
                    if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 生成中转库需求
                        System.out.println("demandQuantity = " + demandQuantity);
                    }
                }
            }
            gapDemandQuantityMap.put(productCode, gapDemandQuantity);
        }

        System.out.println("&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&");


        Date mondayOfWeek = getMondayOfWeek(DateUtils.stringToDate("2025-07-28"));
        System.out.println("mondayOfWeek = " + DateUtils.dateToString(mondayOfWeek));

        Date moveDay = DateUtils.moveDay(mondayOfWeek, 6);
        System.out.println("moveDay = " + DateUtils.dateToString(moveDay));

        boolean crossMonth = isCrossMonth(mondayOfWeek, moveDay);
        System.out.println("crossMonth = " + crossMonth);

        Date lastDayOfMonth = getLastDayOfMonth(mondayOfWeek);
        System.out.println("lastDayOfMonth = " + DateUtils.dateToString(lastDayOfMonth));

        int dateInterval = getDaysWithBothEnds(mondayOfWeek, lastDayOfMonth);
        System.out.println("间隔天数 = " + dateInterval);

        int i = calculateOccupiedDays(DateUtils.stringToDate("2025-08-18"), DateUtils.stringToDate("2025-08-24"), DateUtils.stringToDate("2025-08-23"));
        int i2 = calculateOccupiedDays(DateUtils.stringToDate("2025-08-25"), DateUtils.stringToDate("2025-08-31"), DateUtils.stringToDate("2025-08-27"));
        System.out.println("i = " + i);
        System.out.println("i2 = " + i2);

        BigDecimal demandQuantity = new BigDecimal("12");
        BigDecimal avgDemandQuantity = demandQuantity.divide(new BigDecimal(7), 2, RoundingMode.HALF_UP);
        // 获取月产能计算天数
        demandQuantity = avgDemandQuantity.multiply(BigDecimal.valueOf(7 - i2));
        System.out.println("demandQuantity = " + demandQuantity);


        Date firstDayOfNextMonth = getFirstDayOfNextMonth(DateUtils.stringToDate("2025-07-28"));
        System.out.println("firstDayOfNextMonth = " + DateUtils.dateToString(firstDayOfNextMonth));

        System.out.println("====================================================");

//        BigDecimal demandQuantity = new BigDecimal("1000");
//        Map<String, BigDecimal> gapDemandQuantityMap = new HashMap<>();
//        gapDemandQuantityMap.put("A", new BigDecimal("-1000"));
//        BigDecimal inventoryQuantity = BigDecimal.ZERO;
//        BigDecimal gapQuantity = gapDemandQuantityMap.get("A");
//        if (gapQuantity.compareTo(BigDecimal.ZERO) < 0) {
//            inventoryQuantity = gapQuantity.abs();
//        }
//
//        if (inventoryQuantity.compareTo(BigDecimal.ZERO) > 0){
//            if (demandQuantity.compareTo(inventoryQuantity) <= 0){
//                inventoryQuantity = inventoryQuantity.subtract(demandQuantity);
//                inventoryQuantity = inventoryQuantity.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : inventoryQuantity.negate();
//                gapDemandQuantityMap.put("A", inventoryQuantity);
//            }else {
//                demandQuantity = demandQuantity.subtract(inventoryQuantity);
//                gapDemandQuantityMap.put("A", BigDecimal.ZERO);
//            }
//        }
        System.out.println("demandQuantity = " + demandQuantity);
        System.out.println("====================================================");

        BigDecimal gapQuantity = new BigDecimal("0");
        BigDecimal bpkDemandQuantity = new BigDecimal("1000");
        gapQuantity = gapQuantity.negate();
        System.out.println("gapQuantity = " + gapQuantity);
        System.out.println("抵消数量=" + bpkDemandQuantity.subtract(gapQuantity));

    }

    /**
     * 根据指定的Date类型日期，获取下个月的1号
     * @param date 指定日期
     * @return 下个月1号的Date对象
     */
    public static Date getFirstDayOfNextMonth(Date date) {
        // 创建Calendar实例并设置为指定日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 月份加1
        calendar.add(Calendar.MONTH, 1);
        // 设置为当月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        // 清空时间部分（可选，根据需求决定是否保留时分秒）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static int calculateOccupiedDays(Date rangeStart, Date rangeEnd, Date date) {
        // 归一化日期，只保留年月日（去除时分秒）
        Date normalizedStart = DateUtils.getDayFirstTime(rangeStart);
        Date normalizedEnd = DateUtils.getDayFirstTime(rangeEnd);
        Date normalizedDate = DateUtils.getDayFirstTime(date);

        // 如果给定日期在区间开始之前，占用0天
        if (normalizedDate.before(normalizedStart)) {
            return 0;
        }
        // 如果给定日期在区间结束之后，占用整个区间的天数
        if (normalizedDate.after(normalizedEnd)) {
            int occupiedDays = daysBetween(normalizedStart, normalizedEnd);
            occupiedDays = occupiedDays > 0 ? occupiedDays - 1 : 0;
            return occupiedDays;
        }
        // 计算从区间开始到给定日期的天数（包括开始和给定日期）
        int occupiedDays = daysBetween(normalizedStart, normalizedDate);
        occupiedDays = occupiedDays > 0 ? occupiedDays - 1 : 0;
        return occupiedDays;
    }

    // 计算两个日期之间的天数（包括开始和结束日期）
    private static int daysBetween(Date start, Date end) {
        long diffMillis = end.getTime() - start.getTime();
        // 将毫秒差转换为天数（注意加1，包括起始和结束日期）
        int days = (int) (diffMillis / (24 * 60 * 60 * 1000)) + 1;
        return days;
    }

    public static int getDaysWithBothEnds(Date date1, Date date2) {
        // 重置时分秒为0
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        cal1.set(Calendar.MILLISECOND, 0);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        cal2.set(Calendar.HOUR_OF_DAY, 0);
        cal2.set(Calendar.MINUTE, 0);
        cal2.set(Calendar.SECOND, 0);
        cal2.set(Calendar.MILLISECOND, 0);

        // 计算毫秒差，转换为天数
        long timeDiff = Math.abs(cal2.getTimeInMillis() - cal1.getTimeInMillis());
        long dayDiff = timeDiff / (1000 * 60 * 60 * 24);

        // 包含首尾，加1
        return (int) dayDiff + 1;
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "材料毛需求导入模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("材料编码*"));
        headers.add(Collections.singletonList("材料名称*"));
        headers.add(Collections.singletonList("物料类别(PVB、B、RA.A)*"));
        headers.add(Collections.singletonList("通用类别*"));
        headers.add(Collections.singletonList("本厂编码"));
        headers.add(Collections.singletonList("车型编码"));
        Date moveDate = DateUtils.moveDay(new Date(), 30);
        List<Date> intervalDates = DateUtils.getIntervalDates(new Date(), moveDate);
        intervalDates.forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.COMMON_DATE_STR3))));
        EasyExcel.write(out).sheet("材料毛需求").head(headers).registerWriteHandler(new CustomColumnWidthHandler()).doWrite(Collections.emptyList());
    }

    @Override
    public BaseResponse<Void> doImportGrossDemand(MultipartFile file) {
        Map<String, String> extMap = MapUtil.newHashMap();
        try {
            if (file.isEmpty()) {
                throw new BusinessException("文件为空");
            }
            ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 6, 0, extMap);
            EasyExcel.read(file.getInputStream(), excelDynamicDataListener).sheet().doReadSync();
        } catch (Exception e) {
            log.error("材料毛需求导入失败:", e);
            throw new BusinessException("导入失败, {0}", e.getLocalizedMessage());
        }
        String successInfo = extMap.get("successInfo");
        String errorInfo = extMap.get("errorInfo");
        if (StringUtils.isBlank(errorInfo)) {
            return BaseResponse.success(successInfo);
        } else {
            return BaseResponse.error(successInfo + errorInfo);
        }
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        List<String> collect = new ArrayList<>(headers.values());
        if (!collect.contains("材料编码*") || !collect.contains("材料名称*") || !collect.contains("物料类别(PVB、B、RA.A)*") || !collect.contains("通用类别*")) {
            throw new BusinessException("导入数据模板错误，请检查");
        }
        if (CollectionUtils.isEmpty(data)) {
            throw new BusinessException("模板数据为空,没有数据");
        }
        String contentType = extMap.get("contentType");
        materialGrossDemandComponentHandler.handle(contentType, headers, data, extMap);
    }

    @Override
    public void doDeleteByProductCodeListAndSource(List<String> productCodeList) {
        materialGrossDemandDao.deleteByProductCodeListAndSource(productCodeList);
    }

    @Override
    public void exportMaterialGrossDemand(HttpServletResponse response, MaterialGrossDemandParam materialGrossDemandParam) {
        try {
            // 查询权限下的物料数据
            List<NewProductStockPointVO> newProductStockPointVOS = mrpNewProductStockPointDao
                    .selectColumnVOByParams(ImmutableMap.of("materialPlanner", SystemHolder.getUserId()));
            if (CollectionUtils.isEmpty(newProductStockPointVOS)){
                return;
            }
            List<String> planUserProductCodeList = newProductStockPointVOS.stream()
                    .map(NewProductStockPointVO::getProductCode)
                    .distinct().collect(Collectors.toList());
            materialGrossDemandParam.setPlanUserProductCodeList(planUserProductCodeList);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("物料毛需求导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 查询最新的版本
//            MaterialGrossDemandVersionPO materialGrossDemandVersionPO = materialGrossDemandVersionDao.selectLastVersion();
            Map<String, Object> params = this.assemblyParams(materialGrossDemandParam);
            List<MaterialGrossDemandVO> list = this.selectByParams(params);
            // 按唯一分组键（本厂编码+物料编码+数据来源）分组
            Map<String, List<MaterialGrossDemandVO>> collect = list.stream()
                    .collect(Collectors.groupingBy(item ->
                            String.join("#",
                                    item.getProductFactoryCode(),
                                    item.getProductCode(),
                                    item.getDemandSource()
                            )
                    ));

            // 动态日期列：提取所有日期并排序
            List<String> dateColumns = list.stream()
                    .map(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3))
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            // 获取本厂编码
            List<String> productFactoryCodeList = list.stream()
                    .map(MaterialGrossDemandVO::getProductFactoryCode)
                    .distinct().collect(Collectors.toList());
            Map<String, String> productCodeToName = new HashMap<>();
            if (CollectionUtils.isNotEmpty(productFactoryCodeList)) {
                List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                        ImmutableMap.of("productCodeList", productFactoryCodeList));
                productCodeToName = productStockPointVOList.stream()
                        .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getProductName, (k1, k2) -> k2));
            }

            // 构建表头（固定列+动态日期列）
            List<List<String>> head = new ArrayList<>();
            head.add(Collections.singletonList("物料"));
            head.add(Collections.singletonList("物料名称"));
            head.add(Collections.singletonList("物料类型"));
            head.add(Collections.singletonList("本厂编码"));
            head.add(Collections.singletonList("产品名称"));
            head.add(Collections.singletonList("数据来源"));
            dateColumns.forEach(date -> head.add(Collections.singletonList(date)));

            // 构建数据行
            List<List<Object>> data = new ArrayList<>();
            for (Map.Entry<String, List<MaterialGrossDemandVO>> entry : collect.entrySet()) {
                List<MaterialGrossDemandVO> groupItems = entry.getValue();
                // 创建一行数据
                List<Object> row = new ArrayList<>();

                // 固定列数据（取分组中的第一条）
                MaterialGrossDemandVO firstItem = groupItems.get(0);
                row.add(firstItem.getProductCode());
                row.add(firstItem.getProductName());
                row.add(firstItem.getProductCategory());
                row.add(firstItem.getProductFactoryCode());
                row.add(productCodeToName.get(firstItem.getProductFactoryCode()));
                row.add(MrpDemandSourceEnum.getDescByCode(firstItem.getDemandSource()));

                // 将当前分组的日期-需求量转换为Map，方便快速查找
                Map<String, BigDecimal> dateToDemand = groupItems.stream()
                        .collect(Collectors.toMap(
                                item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3),
                                MaterialGrossDemandVO::getDemandQuantity,
                                (v1, v2) -> v1));

                // 填充动态日期列
                for (String date : dateColumns) {
                    BigDecimal demand = dateToDemand.getOrDefault(date, BigDecimal.ZERO);
                    row.add(demand);
                }
                // 整行添加一次
                data.add(row);
            }

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .registerConverter(new BigDecimalConverter())
                    .sheet("物料毛需求")
                    .doWrite(data);
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> checkCapacityBalancePublishDate(String scenario) {
        // 最新一致性预测需求版本的发布时间
        ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO = dfpFeign.selectConsistenceDemandForecastVersionLatestPublished(scenario);
        // 最新产能平衡的发布时间
        String capacityBalancePublishDateStr = mpsFeign.selectWeekMaxVersionTime(scenario);

        // 获取一致性需求预测的发布时间、产能平衡的发布时间, 时间相差24小时则提醒
        if (null == consistenceDemandForecastVersionVO && "暂无".contains(capacityBalancePublishDateStr)) {
            return BaseResponse.error("最新的一致性需求预测版本，没有进行产能平衡计算发布，请与生产计划及时确");
        }

        if (consistenceDemandForecastVersionVO != null && !"暂无".contains(capacityBalancePublishDateStr)) {
            Date consistenceDemandForecastPublishDate = consistenceDemandForecastVersionVO.getModifyTime();
            // capacityBalancePublishDateStr去除第一个和最后一个双引号
            capacityBalancePublishDateStr = capacityBalancePublishDateStr.substring(1, capacityBalancePublishDateStr.length() - 1);
            Date capacityBalancePublishDate = DateUtils.stringToDate(capacityBalancePublishDateStr, DateUtils.COMMON_DATE_STR1);
            if (capacityBalancePublishDate.compareTo(consistenceDemandForecastPublishDate) >= 0) {
                return BaseResponse.success();
            }
            if (DateUtils.getDateInterval(consistenceDemandForecastPublishDate, capacityBalancePublishDate) > 1) {
                return BaseResponse.error(String.format("最新的一致性需求预测版本是%s，没有进行产能平衡计算发布，请与生产计划及时确认！", DateUtils.dateToString(consistenceDemandForecastVersionVO.getModifyTime(), DateUtils.COMMON_DATE_STR3)));
            }
        }
        return BaseResponse.success();
    }

    @Override
    public List<MaterialGrossDemandVO> dataConsistentCheck() {
        return materialGrossDemandDao.dataConsistentCheck();
    }

}