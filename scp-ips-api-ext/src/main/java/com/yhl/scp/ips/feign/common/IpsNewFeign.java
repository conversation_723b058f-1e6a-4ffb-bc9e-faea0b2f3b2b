package com.yhl.scp.ips.feign.common;

import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.ips.api.vo.ExtApiConfigVO;
import com.yhl.scp.ips.api.vo.LlmConfigVO;
import com.yhl.scp.ips.rbac.dto.DeptDTO;
import com.yhl.scp.ips.rbac.dto.UserDTO;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.entity.Dept;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.warning.vo.WarningSqlSettingVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <code>IpsFeign</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-23 15:44:47
 */
@FeignClient(value = ServletContextConstants.IPS, path = "/", configuration = {FeignConfig.class}, url = "${ips.feign.url:}")
public interface IpsNewFeign {
    @GetMapping({"/api/scenario/getDefaultScenario"})
    BaseResponse<String> getDefaultScenario(@RequestParam("moduleCode") String moduleCode, @RequestParam("tenantId") String tenantId);

    @GetMapping({"/api/scenario/selectDefaultByTenantId"})
    List<Scenario> selectDefaultByTenantId(@RequestParam("tenantId") String tenantId);

    @GetMapping({"/api/scenario/getScenarioByTenantCode"})
    BaseResponse<String> getScenarioByTenantCode(@RequestParam("moduleCode") String moduleCode, @RequestParam("tenantCode") String tenantCode);

    @GetMapping({"/api/scenario/getScenariosByModuleCode"})
    BaseResponse<List<Scenario>> getScenariosByModuleCode(@RequestParam("moduleCode") String moduleCode);

    @GetMapping({"/api/userList"})
    List<User> userList();

    @GetMapping({"/api/userById"})
    User userById(@RequestParam("id") String id);

    @GetMapping({"/api/scenario/selectByScenario"})
    List<Scenario> selectByScenario(@RequestParam("scenario") String scenario);

    @PostMapping({"/dept/syncDeptData"})
    void syncDeptData(@RequestBody List<DeptDTO> list);

    @PostMapping({"/user/syncUserData"})
    void syncUserData(@RequestBody List<UserDTO> list);

    @GetMapping({"/api/getIpAddress"})
    String getIpAddress(@RequestParam("moduleCode") String moduleCode);

    @GetMapping({"/api/selectUserByUserName"})
    User getUserByUserName(@RequestParam("userName") String userName);

    @GetMapping({"/api/userNameById"})
    User userNameById(@RequestParam("id") String id);

    @PostMapping("api/selectUserByParams")
    List<User> selectUserByParams(@RequestParam("scenario") String scenario, @RequestBody Map<String, Object> params);

    @GetMapping({"/api/scenarioBusinessRange"})
    BaseResponse<ScenarioBusinessRangeVO> getScenarioBusinessRange(@RequestParam("scenario") String scenario,
                                                                   @RequestParam("rangeType") String rangeType,
                                                                   @RequestParam("rangeCategory") String rangeCategory,
                                                                   @RequestParam(value = "apiConfigId", required = false) String apiConfigId);

    @PostMapping({"/api/selectScenarioBusinessByParams"})
    List<ScenarioBusinessRangeVO> selectScenarioBusinessByParams(@RequestBody Map<String, Object> params);


    @GetMapping({"/api/getDeptByCurrentUserId"})
    List<Dept> getDeptByCurrentUserId(@RequestParam("userId") String userId);

    @GetMapping({"/api/selectRoleListByUserId"})
    List<Role> selectRoleListByUserId(@RequestParam("userId") String userId);

    @GetMapping({"/api/getOneWarningSql"})
    List<WarningSqlSettingVO> getWarningSql(@RequestParam("code") String code);

    @GetMapping({"/api/selectUserMessage"})
    List<UserMessageVO> selectUserMessage(@RequestParam("userId") String userId);

    @PostMapping({"/user/syncAllUserData"})
    BaseResponse<String> syncAllUserData(@RequestBody List<UserDTO> list);

    @GetMapping({"/api/llm-config"})
    BaseResponse<LlmConfigVO> getLlmConfig(@RequestParam("llmConfigId") String llmConfigId);

    @PostMapping({"/api/userMessage/saveUserMessage"})
    void saveUserMessage(@RequestBody List<UserMessageDTO> list);

    @GetMapping({"/api/all-config"})
    List<ExtApiConfigVO> selectALLApiConfig();
}
