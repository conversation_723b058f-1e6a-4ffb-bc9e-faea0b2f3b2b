package com.yhl.scp.dfp.oem.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <code>OemDO</code>
 * <p>
 * 主机厂档案DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OemDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 231379798142015495L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 生产组织编码
     */
    private String saleOrgId;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂名称
     */
    private String oemName;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 地点编码
     */
    private String locationCode;
    /**
     * 地址1
     */
    private String locationArea1;
    /**
     * 地址2
     */
    private String locationArea2;
    /**
     * 地址3
     */
    private String locationArea3;
    /**
     * 地址4
     */
    private String locationArea4;
    /**
     * 付款条件
     */
    private String paymentTerm;
    /**
     * 运输条款
     */
    private String transitClause;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 市场属性
     */
    private String marketType;
    /**
     * 目标货位
     */
    private String targetStockLocation;
    /**
     * ediLocation
     */
    private String ediLocation;
    /**
     * 工厂
     */
    private String plantCode;
    /**
     * erp客户id
     */
    private String ebsCustomerId;
    /**
     * erp地点id
     */
    private String ebsSiteId;
    /**
     * erp地址用途id
     */
    private String shipToSiteUseId;
    /**
     * 国家
     */
    private String siteCountry;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * edi标志
     */
    private String ediFlag;
    
    /**
     * 理货单模式，MES，GRP
     */
    private String tallyOrderMode;
    
    /**
     * 自提类型：自提，非自提
     */
    private String pickUpType;
    
    /**
     * ERP地址ID
     */
    private String erpCustomerAddressId;
    
    /**
     * 中转库目标货位
     */
    private String transferTargetStockLocation;
    
    /**
     * 客户简称
     */
    private String customerAbbreviation;

}