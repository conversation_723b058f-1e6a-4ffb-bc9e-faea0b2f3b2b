package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverService;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description: 同步量产移交状态定时任务
 * author：李杰
 * email: <EMAIL>
 * date: 2025/06/13
 */
@Component
@Slf4j
public class ProductionHandoverStatusJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MassProductionHandoverService massProductionHandoverService;

    @XxlJob("productionHandoverStatusJob")
    public ReturnT<String> productionHandoverStatusJob() {
        XxlJobHelper.log("开始运行同步量产移交状态定时任务。");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    massProductionHandoverService.syncStatus(scenario.getTenantId(),null,scenario.getDataBaseName());
                }
            } catch (Exception e) {
                XxlJobHelper.log("零件映射定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
            DynamicDataSourceContextHolder.clearDataSource();
        }
        XxlJobHelper.log("零件映射定时任务结束。");
        return ReturnT.SUCCESS;
    }
}
