package com.yhl.scp.dfp.report.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.delivery.calc.DeliveryPlanCalcSpace;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanDomainService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.report.infrastructure.dao.HighRiskProductOrderDao;
import com.yhl.scp.dfp.report.service.HighRiskProductOrderService;
import com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO;
import com.yhl.scp.dfp.safety.service.SafetyStockLevelService;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;

/**
 * <code>HighRiskProductOrderServiceImpl</code>
 * <p>
 * HighRiskProductOrderServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-14 23:20:59
 */
@Service
public class HighRiskProductOrderServiceImpl implements HighRiskProductOrderService {

    @Resource
    private HighRiskProductOrderDao highRiskProductOrderDao;

    @Resource
    private SafetyStockLevelService safetyStockLevelService;

    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private OemStockPointMapService oemStockPointMapService;
    
    @Resource
    private DeliveryPlanDomainService deliveryPlanDomainService;
    
    @Resource
    private OemService oemService;
    
    @Resource
    private OemTransportTimeService oemTransportTimeService;

    public static final String PRE_OPERATION = "预处理";
    public static final String PAINTING_OPERATION = "印刷";
    public static final String FORMING_OPERATION = "成型";
    public static final String MERGING_OPERATION = "合片";
    public static final String PACKAGING_OPERATION = "包装";

    @Override
    public List<HighRiskProductOrderVO> selectByVersionId(String versionId) {
        return highRiskProductOrderDao.selectByVersionId(versionId);
    }

    private String getSubInventory(String scenario) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                ipsNewFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
        return scenarioBusinessRange.getData().getRangeData();
    }

    private String getSalesOrganization(String scenario) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                ipsNewFeign.getScenarioBusinessRange(scenario, "SALE_ORGANIZATION",
                        "INTERNAL", null);
        return scenarioBusinessRange.getData().getRangeData();
    }
    @Override
    public List<HighRiskProductOrderVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam,
                                                     String organizationCode) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam, organizationCode);
    }

    @Override
    public List<HighRiskProductOrderVO> selectByCondition(String sortParam, String queryCriteriaParam,
                                                          String organizationCode) {
        List<HighRiskProductOrderVO> dataList = highRiskProductOrderDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        setSpecialData(SystemHolder.getScenario(),dataList,organizationCode,sortParam,queryCriteriaParam);
        return dataList;
    }

    /**
     * 特殊赋值
     *
     * @param scenario           数据源
     * @param dataList           高风险产品订单
     * @param organizationCode   组织编码
     * @param sortParam          排序param
     * @param queryCriteriaParam 参数param
     */
    private void setSpecialData(String scenario,
                                List<HighRiskProductOrderVO> dataList,
                                String organizationCode,
                                String sortParam,
                                String queryCriteriaParam) {
        String stockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String stockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));

        Map<String, List<HighRiskProductOrderVO>> detailGroup =
                highRiskProductOrderDao.selectStatistics(sortParam, queryCriteriaParam).stream()
                        .collect(Collectors.groupingBy(x -> String.join(Constants.DELIMITER,
                                x.getOemCode(), x.getProductCode(), x.getVehicleModelCode())));
        List<String> productCodes = dataList.stream().map(HighRiskProductOrderVO::getProductCode)
                .distinct().collect(Collectors.toList());

        List<String> oemCodes = dataList.stream().map(HighRiskProductOrderVO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<SafetyStockLevelVO> safetyStockLevels = safetyStockLevelService.selectStandardStockDays(oemCodes);
        Map<String, String> oemCode2GnStockPointCodeMap = safetyStockLevels.stream()
                .filter(x -> StockPointTypeEnum.GN.getCode().equals(x.getStockPointType()))
                .collect(Collectors.toMap(SafetyStockLevelVO::getOemCode, SafetyStockLevelVO::getStockCode,
                        (v1, v2) -> v1));
        Map<String, BigDecimal> standardStockDayMap = safetyStockLevels.stream().collect(Collectors
                .toMap(x -> String.join(Constants.DELIMITER, x.getOemCode(), x.getStockPointType()),
                        SafetyStockLevelVO::getStandardStockDay, (v1, v2) -> v1));

        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
        List<String> saleOrganizations = newStockPoints.stream().filter(e ->
                        StringUtils.isNotEmpty(e.getOrganizeType())
                                && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<String> productOrganizations = newStockPoints.stream().filter(e ->
                        StringUtils.isNotEmpty(e.getOrganizeType())
                                && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<String> standardStepCodes = newMdsFeign.selectStandardStepAll(scenario)
        		.stream().filter(e -> Objects.equals(organizationCode, e.getStockPointCode()))
        		.map(StandardStepVO::getStandardStepCode)
        		.collect(Collectors.toList());
        Collections.sort(standardStepCodes);
        List<InventoryBatchDetailVO> bcInventoryBatchDetails =
                inventoryBatchDetailService.selectByProductCodes(productCodes, StockPointTypeEnum.BC.getCode());
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = getCargoLocationMap(bcInventoryBatchDetails, scenario);
        String subInventory = getSubInventory(scenario);
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = bcInventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && subInventory.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = bcInventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p ->
                        String.join("-", p.getStockPointCode(), p.getProductCode())));
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = bcInventoryBatchDetails.stream()
                .filter(t -> Objects.equals(organizationCode, t.getStockPointCode()))
                .collect(Collectors.groupingBy(p -> String.join("-", p.getProductCode(), p.getOperationCode())));
        //获取产品成箱片数
        Map<String, Integer> standardLoadMap = getStandardLoadMap(scenario, productCodes);
        
        //获取中转库半品库存，成品库存
        String salesOrganization = getSalesOrganization(scenario);
        List<OemStockPointMapVO> oemStockPointMaps = oemStockPointMapService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodeList", oemCodes,"stockPointCodesNotInList" , Arrays.asList(salesOrganization)));
        Map<String, String> oemStockPointMap = oemStockPointMaps.stream()
        		.collect(Collectors.toMap(OemStockPointMapVO::getOemCode,OemStockPointMapVO::getStockPointCode,(v1, v2) -> v1));
        List<String> stockPointCodeList = new ArrayList<>(oemStockPointMap.values());
        Map<String, String> oemProductInventoryMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(stockPointCodeList)){
        	List<InventoryBatchDetailVO> oemProductInventorys = inventoryBatchDetailService.selectStatisticsInventory(productCodes, stockPointCodeList);
            oemProductInventoryMap = oemProductInventorys.stream()
            		.collect(Collectors.toMap(e -> String.join("&", e.getStockPointCode(), e.getProductCode(), e.getFreightSpaceDescription()), 
            				InventoryBatchDetailVO::getCurrentQuantity,(v1, v2) -> v1));
        }
        //初始化获取在途数据的发货计划计算对象
        DeliveryPlanCalcSpace deliveryPlanCalcSpace = initDeliveryPlanCalcSpace(oemCodes);
        deliveryPlanDomainService.queryOemReceive(deliveryPlanCalcSpace, oemCodes, productCodes, salesOrganization, false);
        List<String> inlandMesOemCodes = deliveryPlanCalcSpace.getInlandMesOemCodes();
        List<String> inlandNotMesOemCodes = deliveryPlanCalcSpace.getInlandNotMesOemCodes();
        List<String> exitNotMesOemCodes = deliveryPlanCalcSpace.getExitNotMesOemCodes();
        List<String> exitMesOemCodes = deliveryPlanCalcSpace.getExitMesOemCodes();
        Map<String, BigDecimal> inlandMesReceiveMap = deliveryPlanCalcSpace.getInlandMesReceiveMap();
        Map<String, BigDecimal> inlandNotMesReceiveMap = deliveryPlanCalcSpace.getInlandNotMesReceiveMap();
        Map<String, BigDecimal> exitNotMesReceiveMap = deliveryPlanCalcSpace.getExitNotMesReceiveMap();
        Map<String, BigDecimal> exitMesReceiveMap = deliveryPlanCalcSpace.getExitMesReceiveMap();
        
        for (HighRiskProductOrderVO item : dataList) {
            String oemCode = item.getOemCode();
            String productCode = item.getProductCode();
            String vehicleModelCode = item.getVehicleModelCode();
            String joinKey = String.join(Constants.DELIMITER, oemCode, productCode, vehicleModelCode);
            item.setDemand7Days(BigDecimal.ZERO);
            item.setDemand15Days(BigDecimal.ZERO);
            item.setDemand30Days(BigDecimal.ZERO);
            if (detailGroup.containsKey(joinKey)) {
                List<HighRiskProductOrderVO> sortedList = detailGroup.get(joinKey).stream().sorted(Comparator
                        .comparing(HighRiskProductOrderVO::getDemandTime)).collect(Collectors.toList());
                int size = sortedList.size();
                BigDecimal demand7Days = sortedList.subList(0, Math.min(size, 7)).stream()
                        .map(HighRiskProductOrderVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal demand15Days = sortedList.subList(0, Math.min(size, 15)).stream()
                        .map(HighRiskProductOrderVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal demand30Days = sortedList.subList(0, Math.min(size, 30)).stream()
                        .map(HighRiskProductOrderVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setDemand7Days(demand7Days);
                item.setDemand15Days(demand15Days);
                item.setDemand30Days(demand30Days);
            }
            // 厂外设定标准安全库存天数
            String outsideKey = String.join(Constants.DELIMITER, oemCode, StockPointTypeEnum.GN.getCode());
            if (standardStockDayMap.containsKey(outsideKey)) {
                item.setOutsideStandardSafetyStockDays(standardStockDayMap.get(outsideKey));
            }
            // 厂内设定标准安全库存天数
            String insideKey = String.join(Constants.DELIMITER, oemCode, StockPointTypeEnum.BC.getCode());
            if (standardStockDayMap.containsKey(outsideKey)) {
                item.setInsideStandardSafetyStockDays(standardStockDayMap.get(insideKey));
            }

            assembleInventory(productCode, finishInventoryMap, cargoLocationMap, item);
            // 发运库库存(在途量)
            BigDecimal shippingInventory = BigDecimal.ZERO;
            //默认取90天
            String oemMesReceiveKey = String.join("&", oemCode, productCode);
            if(inlandMesOemCodes.contains(oemCode)) {
            	//获取主机厂是国内的，数据来源是MES的在途数量
            	shippingInventory = inlandMesReceiveMap.getOrDefault(oemMesReceiveKey, BigDecimal.ZERO);
            }else if (inlandNotMesOemCodes.contains(oemCode)) {
            	//获取主机厂是国内的，数据来源是非MES的在途数量
            	shippingInventory = inlandNotMesReceiveMap.getOrDefault(oemMesReceiveKey, BigDecimal.ZERO);
            }else if (exitNotMesOemCodes.contains(oemCode)) {
            	//获取主机厂是出口的，数据来源是非MES的在途数量
            	shippingInventory = exitNotMesReceiveMap.getOrDefault(oemMesReceiveKey, BigDecimal.ZERO);
            }else if (exitMesOemCodes.contains(oemCode)) {
            	//获取主机厂是出口的，数据来源是MES的在途数量
            	shippingInventory = exitMesReceiveMap.getOrDefault(oemMesReceiveKey, BigDecimal.ZERO);
            }
            item.setShippingInventory(shippingInventory);
            // 厂内成品库存量
            // item.setInsideFgInventory();
            // 半品库存
            List<BigDecimal> stepInventories = new ArrayList<>();
            for (String standardStepCode : standardStepCodes) {
            	stepInventories.add(getInventory(standardStepCode, productCode,
                        operationInventoryMap, cargoLocationMap,stockPoint,stockPoint2));
			}
            item.setStepInventories(stepInventories);
            // 本票需求缺口* 待确认
            item.setDemandGap(null);
            // 标准单件包装量* 待确认
            Integer standardLoad = standardLoadMap.get(productCode);
            BigDecimal standardPackagingQty = standardLoad == null ? null : BigDecimal.valueOf(standardLoad);
            item.setStandardPackagingQty(standardPackagingQty);
            //评审生产量
            item.setReviewProductionQty(BigDecimal.ZERO);
            //中转库半品库存,中转库成品库存
            String oemStockPointCode = oemStockPointMap.get(oemCode);
            BigDecimal totalProductInventory = BigDecimal.ZERO;
            if(StringUtils.isNotEmpty(oemStockPointCode)) {
            	String transferSemiInventoryStr = oemProductInventoryMap
            			.get(String.join("&", oemStockPointCode, productCode, "半品"));
                item.setTransferSemiInventory(StringUtils.isEmpty(transferSemiInventoryStr) ? BigDecimal.ZERO
                		: new BigDecimal(transferSemiInventoryStr));
                // 中转库成品库存
                String transferFgInventoryStr = oemProductInventoryMap
            			.get(String.join("&", oemStockPointCode, productCode, "成品"));
                item.setTransferFgInventory(StringUtils.isEmpty(transferFgInventoryStr) ? BigDecimal.ZERO 
                		: new BigDecimal(transferFgInventoryStr));
            }
            //合计产品库存（中转库成品库存 + 中转库半品库存 + 在途量 + 场内成品库存量 + 在产品库存10-50）
            setTotalProductInventory(item, stepInventories, totalProductInventory);
            //产品&物料合计库存（合计产品库存 + 未分配物料可生产片数） TODO
            item.setTotalMaterialProductInventory(item.getTotalProductInventory());
            //7,15,30天需求缺口（客户需求（取对应天数）+中转库标准安全库存+成品库标准安全库存-成品库存-在产品库存）
            BigDecimal insideFgInventory = item.getInsideFgInventory() == null ? BigDecimal.ZERO :
            	item.getInsideFgInventory();
            BigDecimal totalStepInventorie = stepInventories.stream()
    			    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal demandGap7Days =item.getDemand7Days()
            		.subtract(insideFgInventory).subtract(totalStepInventorie);
            BigDecimal demandGap15Days =item.getDemand15Days()
            		.subtract(insideFgInventory).subtract(totalStepInventorie);
            BigDecimal demandGap30Days =item.getDemand30Days()
            		.subtract(insideFgInventory).subtract(totalStepInventorie);
            item.setDemandGap7Days(demandGap7Days);
            item.setDemandGap15Days(demandGap15Days);
            item.setDemandGap30Days(demandGap30Days);
        }
    }
    
    /**
     * 初始化获取在途数据的发货计划计算对象
     * @param oemCodes
     * @return
     */
	private DeliveryPlanCalcSpace initDeliveryPlanCalcSpace(List<String> oemCodes) {
		DeliveryPlanCalcSpace deliveryPlanCalcSpace = new DeliveryPlanCalcSpace();
        List<OemVO> oemVOList = oemService.selectByParams(ImmutableMap.of("oemCodes", oemCodes));
        deliveryPlanCalcSpace.initOemVOList(oemVOList);
        //主机厂运输时间数据
        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", deliveryPlanCalcSpace.getOemCodeList()));
        //获取主机厂的运输时间
  		Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup = oemTransportTimeVOList.stream()
  				.collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
  		Map<String, Integer> oemTransportTimeMap = new HashMap<>();
  		for (Entry<String, List<OemTransportTimeVO>> transportTimeEntry : oemTransportTimeVOGroup.entrySet()) {
  			List<OemTransportTimeVO> oemTransportTimeVOS = transportTimeEntry.getValue();
  			OemTransportTimeVO oemTransportTimeVO =
  	                  oemTransportTimeVOS.stream().max(Comparator.comparing(OemTransportTimeVO::getPriority))
  	                  .orElse(new OemTransportTimeVO());
  			BigDecimal transportationTime =  null == oemTransportTimeVO.getTransportationTime() ?
  	                  BigDecimal.ZERO : oemTransportTimeVO.getTransportationTime();
  			oemTransportTimeMap.put(transportTimeEntry.getKey(), transportationTime.multiply(BigDecimal.valueOf(60)).intValue());
  		}
  		deliveryPlanCalcSpace.setOemTransportTimeMap(oemTransportTimeMap);
		return deliveryPlanCalcSpace;
	}
    
    /**
     * 合计产品库存（中转库成品库存 + 中转库半品库存 + 在途量 + 场内成品库存量 + 在产品库存10-50）
     * @param item
     * @param stepInventories
     * @param totalProductInventory
     */
	private void setTotalProductInventory(HighRiskProductOrderVO item, List<BigDecimal> stepInventories,
			BigDecimal totalProductInventory) {
		totalProductInventory = totalProductInventory.add(item.getShippingInventory());
		if(item.getInsideFgInventory() != null) {
			totalProductInventory = totalProductInventory.add(item.getInsideFgInventory());
		}
		if(item.getTransferSemiInventory() != null) {
			totalProductInventory = totalProductInventory.add(item.getTransferSemiInventory());
		}
		if(item.getTransferFgInventory() != null) {
			totalProductInventory = totalProductInventory.add(item.getTransferFgInventory());
		}
		BigDecimal totalStepInventorie = stepInventories.stream()
			    .reduce(BigDecimal.ZERO, BigDecimal::add);
		totalProductInventory = totalProductInventory.add(totalStepInventorie);
		item.setTotalProductInventory(totalProductInventory);
	}
    
    /**
     * 获取产品成箱片数
     * @param scenario
     * @param productCodes
     * @return
     */
	private Map<String, Integer> getStandardLoadMap(String scenario, List<String> productCodes) {
		Map<String, Integer> standardLoadMap = new HashMap<>();
        List<ProductBoxRelationVO> productBoxRelationVOS = newMdsFeign.selectProductBoxRelationVOByProductCodeList(scenario, productCodes);
        productBoxRelationVOS = productBoxRelationVOS.stream()
        		.filter( e-> YesOrNoEnum.YES.getCode().equals(e.getEnabled()) 
        				&& e.getPriority() != null && e.getStandardLoad() != null) 
        		.collect(Collectors.toList());
        Map<String, List<ProductBoxRelationVO>> productBoxRelationMap = productBoxRelationVOS.stream()
        		.collect(Collectors.groupingBy(ProductBoxRelationVO::getProductCode));
        for (Entry<String, List<ProductBoxRelationVO>> boxEntrySet : productBoxRelationMap.entrySet()) {
        	List<ProductBoxRelationVO> pbrList = boxEntrySet.getValue();
        	pbrList.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
        	standardLoadMap.put(boxEntrySet.getKey(), pbrList.get(0).getStandardLoad());
		}
		return standardLoadMap;
	}


    public Map<String, SubInventoryCargoLocationVO> getCargoLocationMap(
            List<InventoryBatchDetailVO> inventoryBatchDetails, String scenario) {
        Map<String, SubInventoryCargoLocationVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return result;
        }
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spaceList)) {
            return result;
        }
        return mpsFeign.queryByFreightSpaces(scenario,
                spaceList, StockPointTypeEnum.BC.getCode()).stream().collect(Collectors
                .toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
    }

    private BigDecimal getInventory(String op, String productCode,
                                Map<String, List<InventoryBatchDetailVO>> inventoryMap,
                                Map<String, SubInventoryCargoLocationVO> cargoLocationMap,String stockPoint,String stockPoint2) {
        String opMainKey = String.join("-", productCode, op);
        List<InventoryBatchDetailVO> inventoryBatchDetails = inventoryMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(opMainKey))
                .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                    String freightSpace = p.getFreightSpace();
                    SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
                    return null != subInventoryCargoLocation;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return BigDecimal.ZERO;
        }
        return inventoryBatchDetails.stream().filter(x ->
                StringUtils.isNotBlank(x.getCurrentQuantity())).map(x ->
                new BigDecimal(x.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void assembleInventory(String productCode,
                                   Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                   Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
                                   HighRiskProductOrderVO item) {
        if (finishInventoryMap.containsKey(productCode)) {
            // 维护产品编码对应的成品库存
            List<InventoryBatchDetailVO> finishList = getFinishInventory(finishInventoryMap.get(productCode),
                    cargoLocationMap);
            BigDecimal finishInventory = finishList.stream().map(t ->
                    new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setInsideFgInventory(finishInventory);
        }
    }

    private List<InventoryBatchDetailVO> getFinishInventory(List<InventoryBatchDetailVO> inventoryBatchDetails,
                                                            Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return new ArrayList<>();
        }
        return inventoryBatchDetails.stream().filter(p -> {
            String freightSpace = p.getFreightSpace();
            SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
            return null != subInventoryCargoLocation;
        }).collect(Collectors.toList());
    }

    @Override
    public List<HighRiskProductOrderVO> selectByVOParams(Map<String, Object> params) {
        List<HighRiskProductOrderVO> highRiskProductOrderVOList = highRiskProductOrderDao.selectByVOParams(params);
//        setSpecialData(SystemHolder.getScenario(), highRiskProductOrderVOList, null, null, null);
        return highRiskProductOrderVOList;
    }
}