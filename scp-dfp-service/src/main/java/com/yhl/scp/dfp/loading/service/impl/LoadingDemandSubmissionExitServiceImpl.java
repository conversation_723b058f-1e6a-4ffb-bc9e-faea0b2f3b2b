package com.yhl.scp.dfp.loading.service.impl;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcelFactory;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.excel.listener.ExcelDynamicDataListener;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionExitConvertor;
import com.yhl.scp.dfp.loading.domain.entity.LoadingDemandSubmissionExitDO;
import com.yhl.scp.dfp.loading.domain.service.LoadingDemandSubmissionExitDomainService;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionExitDTO;
import com.yhl.scp.dfp.loading.handler.LoadingComponentExitHandler;
import com.yhl.scp.dfp.loading.infrastructure.dao.LoadingDemandSubmissionExitDao;
import com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionExitPO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailExitService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionExitService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailExitVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionExitVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.utils.BpimDateUtil;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;

import cn.hutool.core.map.MapUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>LoadingDemandSubmissionExitServiceImpl</code>
 * <p>
 * 装车需求提报(出口)应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-04 16:57:45
 */
@Slf4j
@Service
public class LoadingDemandSubmissionExitServiceImpl extends AbstractService implements LoadingDemandSubmissionExitService {

    @Resource
    private LoadingDemandSubmissionExitDao loadingDemandSubmissionExitDao;

    @Resource
    private LoadingDemandSubmissionExitDomainService loadingDemandSubmissionExitDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private LoadingDemandSubmissionDetailExitService loadingDemandSubmissionDetailExitService;

    @Override
    public BaseResponse<Void> doCreate(LoadingDemandSubmissionExitDTO loadingDemandSubmissionExitDTO) {
        // 0.数据转换
        LoadingDemandSubmissionExitDO loadingDemandSubmissionExitDO = LoadingDemandSubmissionExitConvertor.INSTANCE.dto2Do(loadingDemandSubmissionExitDTO);
        LoadingDemandSubmissionExitPO loadingDemandSubmissionExitPO = LoadingDemandSubmissionExitConvertor.INSTANCE.dto2Po(loadingDemandSubmissionExitDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionExitDomainService.validation(loadingDemandSubmissionExitDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(loadingDemandSubmissionExitPO);
        loadingDemandSubmissionExitDao.insert(loadingDemandSubmissionExitPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(LoadingDemandSubmissionExitDTO loadingDemandSubmissionExitDTO) {
        // 0.数据转换
        LoadingDemandSubmissionExitDO loadingDemandSubmissionExitDO = LoadingDemandSubmissionExitConvertor.INSTANCE.dto2Do(loadingDemandSubmissionExitDTO);
        LoadingDemandSubmissionExitPO loadingDemandSubmissionExitPO = LoadingDemandSubmissionExitConvertor.INSTANCE.dto2Po(loadingDemandSubmissionExitDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionExitDomainService.validation(loadingDemandSubmissionExitDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(loadingDemandSubmissionExitPO);
        loadingDemandSubmissionExitDao.updateSelective(loadingDemandSubmissionExitPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<LoadingDemandSubmissionExitDTO> list) {
        List<LoadingDemandSubmissionExitPO> newList = LoadingDemandSubmissionExitConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        loadingDemandSubmissionExitDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<LoadingDemandSubmissionExitDTO> list) {
        List<LoadingDemandSubmissionExitPO> newList = LoadingDemandSubmissionExitConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        loadingDemandSubmissionExitDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return loadingDemandSubmissionExitDao.deleteBatch(idList);
        }
        return loadingDemandSubmissionExitDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public LoadingDemandSubmissionExitVO selectByPrimaryKey(String id) {
        LoadingDemandSubmissionExitPO po = loadingDemandSubmissionExitDao.selectByPrimaryKey(id);
        return LoadingDemandSubmissionExitConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_loading_demand_submission_exit")
    public List<LoadingDemandSubmissionExitVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_loading_demand_submission_exit")
    public List<LoadingDemandSubmissionExitVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<LoadingDemandSubmissionExitVO> dataList = loadingDemandSubmissionExitDao.selectByCondition(sortParam, queryCriteriaParam);
        LoadingDemandSubmissionExitServiceImpl target = springBeanUtils.getBean(LoadingDemandSubmissionExitServiceImpl.class);
        //处理版本查询参数
        Map<String, Object> params = new HashMap<>();
        if(StringUtils.isNotEmpty(queryCriteriaParam)){
        	int index = queryCriteriaParam.indexOf("version_id");
            if (index != -1) {
                params.put("queryCriteriaParam", queryCriteriaParam.substring(index));
            }
        }
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<LoadingDemandSubmissionExitVO> selectByParams(Map<String, Object> params) {
        List<LoadingDemandSubmissionExitPO> list = loadingDemandSubmissionExitDao.selectByParams(params);
        return LoadingDemandSubmissionExitConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<LoadingDemandSubmissionExitVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.LOADING_DEMAND_SUBMISSION_EXIT.getCode();
    }

    @Override
    public List<LoadingDemandSubmissionExitVO> invocation(List<LoadingDemandSubmissionExitVO> dataList, Map<String, Object> params, String invocation) {
    	if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        List<String> submissionExitIds = dataList.stream().map(LoadingDemandSubmissionExitVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(submissionExitIds)) {
            return dataList;
        }
        Map<String, Object> detailParam = MapUtil.newHashMap();
        detailParam.put("submissionExitIds", submissionExitIds);
        List<LoadingDemandSubmissionDetailExitVO> submissionDetailExitVOS = loadingDemandSubmissionDetailExitService.selectByParams(detailParam);
        if (CollectionUtils.isEmpty(submissionDetailExitVOS)) {
            return dataList;
        }
        Map<String, List<LoadingDemandSubmissionDetailExitVO>> submissionDetailExitMap = submissionDetailExitVOS.stream()
                .collect(
                        Collectors.groupingBy(LoadingDemandSubmissionDetailExitVO::getSubmissionExitId, Collectors.toList()));

        //获取用户权限下所有的装车需求提报数据
        Date maxImportDate = null;
        if(params != null && params.containsKey("queryCriteriaParam")) {
        	List<LoadingDemandSubmissionExitVO> allDataList = loadingDemandSubmissionExitDao.selectByCondition(null,
        			params.get("queryCriteriaParam").toString());
        	 Optional<Date> maxImportTime = allDataList.stream()
                     .filter(vo -> vo.getImportTime() != null) // 判空过滤
                     .map(LoadingDemandSubmissionExitVO::getImportTime)
                     .max(Date::compareTo); // 取最大日期
        	 if(maxImportTime.isPresent()) {
        		 maxImportDate = maxImportTime.get();
        	 }
        }

        for (LoadingDemandSubmissionExitVO submissionExitVO : dataList) {
            List<LoadingDemandSubmissionDetailExitVO> detailExitVOS = submissionDetailExitMap.get(submissionExitVO.getId());
            if (CollectionUtils.isEmpty(detailExitVOS)) {
                continue;
            }
            submissionExitVO.setDayData(detailExitVOS.stream().filter(x -> GranularityEnum.DAY.getCode().equals(x.getSubmissionType()))
            		.sorted(Comparator.comparing(LoadingDemandSubmissionDetailExitVO::getDemandTime)).collect(Collectors.toList()));
            submissionExitVO.setMonthData(detailExitVOS.stream().filter(x -> GranularityEnum.MONTH.getCode().equals(x.getSubmissionType()))
            		.sorted(Comparator.comparing(LoadingDemandSubmissionDetailExitVO::getDemandTime)).collect(Collectors.toList()));
            // 添加日期汇总数据计算
            BigDecimal daySummarySum = submissionExitVO.getDayData().stream().filter(e-> e.getDemandQuantity() != null)
            		.map(LoadingDemandSubmissionDetailExitVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            submissionExitVO.setDaySummary(daySummarySum);
            Map<String, List<LoadingDemandSubmissionDetailExitVO>> loadingDemandSubmissionDetailMapOfTime = submissionExitVO.getDayData()
            		.stream().collect(Collectors.groupingBy(
                    detail -> DateUtils.dateToString(DateUtils.stringToDate(detail.getDemandTime()), DateUtils.COMMON_DATE_STR3)));

            Map<String, BigDecimal> daySummary = getDemandTimeQuantityMap(loadingDemandSubmissionDetailMapOfTime);
            List<LoadingDemandSubmissionDetailExitVO> daySummaryList = daySummary.entrySet().stream()
                    .map(entry -> {
                    	LoadingDemandSubmissionDetailExitVO vo = new LoadingDemandSubmissionDetailExitVO();
                        vo.setDemandTime(entry.getKey());
                        vo.setDemandQuantity(entry.getValue());
                        return vo;
                    })
                    .sorted(Comparator.comparing(LoadingDemandSubmissionDetailExitVO::getDemandTime))
                    .collect(Collectors.toList());

            submissionExitVO.setDaySummaryData(daySummaryList);
            //1.ABS((“导入的汇总”-“历史汇总”)/“历史汇总”) * 100% >= 15%，则产品编码字段的字体颜色变为红色
            BigDecimal historyDemandQuantity = submissionExitVO.getHistoryDemandQuantity();
            if(historyDemandQuantity != null && historyDemandQuantity.compareTo(BigDecimal.ZERO) > 0) {
            	BigDecimal totalDemandQuantity = submissionExitVO.getDayData().stream()
                        .map(LoadingDemandSubmissionDetailExitVO::getDemandQuantity).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            	BigDecimal diffDemandQuantity = totalDemandQuantity.subtract(historyDemandQuantity).abs();
            	BigDecimal diffDemandQuantityRate = diffDemandQuantity.multiply(BigDecimal.valueOf(100))
            			.divide(historyDemandQuantity, 0, BigDecimal.ROUND_UP);
            	if(diffDemandQuantityRate.compareTo(BigDecimal.valueOf(15)) >= 0) {
            		submissionExitVO.setBackgroundColor("RED");
            	}
            }
            //2.如果导入时间 = 创建时间，则该行数据的产品编码字段的底色变为绿色，优先级2
            Date importTime = submissionExitVO.getImportTime();
            if(importTime != null && Objects.equals(DateUtils.dateToString(importTime, "yyyy-MM-dd HH:mm"),
            		DateUtils.dateToString(submissionExitVO.getCreateTime(), "yyyy-MM-dd HH:mm"))) {
            	submissionExitVO.setBackgroundColor("GREEN");
            }
            //3.如果导入时间 ≠ 导入时间所有数据中最新的导入时间，则该行数据的产品编码字段的底色变为浅黄色， 优先级1
            if(maxImportDate != null && importTime != null && !Objects.equals(DateUtils.dateToString(submissionExitVO.getImportTime(), "yyyy-MM-dd HH:mm"),
            		DateUtils.dateToString(maxImportDate, "yyyy-MM-dd HH:mm"))) {
            	submissionExitVO.setBackgroundColor("YELLOW");
            }
        }
        return dataList;
    }
    
    private Map<String, BigDecimal> getDemandTimeQuantityMap(Map<String, List<LoadingDemandSubmissionDetailExitVO>> listMap) {
        Map<String, BigDecimal> daySummary = new HashMap<>();
        for (Map.Entry<String, List<LoadingDemandSubmissionDetailExitVO>> entry : listMap.entrySet()) {
            List<LoadingDemandSubmissionDetailExitVO> value = entry.getValue();
            List<LoadingDemandSubmissionDetailExitVO> demandQuantityNullList = value.stream().filter(s -> null == s.getDemandQuantity()).collect(Collectors.toList());
            if (demandQuantityNullList.size() == value.size()) {
                daySummary.put(entry.getKey(), null);
                continue;
            }

            BigDecimal dayQuantitySum = BigDecimal.ZERO;
            for (LoadingDemandSubmissionDetailExitVO detail : value) {
                if (detail.getDemandQuantity() != null) {
                    dayQuantitySum = dayQuantitySum.add(detail.getDemandQuantity());
                }
            }
            daySummary.put(entry.getKey(), dayQuantitySum);
        }
        return daySummary;
    }

    @SneakyThrows
	@Override
	public void exportTemplate(HttpServletResponse response, String templateType) {
		ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "装车需求提报模板(出口)");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("主机厂编码"));
        headers.add(Collections.singletonList("产品编码"));
        headers.add(Collections.singletonList("客户零件号"));
        
        //获取近24周时间
        LocalDate currentMonday = LocalDate.now();
        List<String> allWeekDays = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 第一个日期是当天日期
        allWeekDays.add(currentMonday.format(formatter));
        // 后续23个日期，每周一
        if (currentMonday.getDayOfWeek() == DayOfWeek.MONDAY) {
        	currentMonday = currentMonday.plusWeeks(1);
        }else {
        	currentMonday = currentMonday.with(DayOfWeek.MONDAY).plusWeeks(1);
        }
        for (int i = 1; i < 24; i++) {
        	allWeekDays.add(currentMonday.format(formatter));
            currentMonday = currentMonday.plusWeeks(1);
        }
        if ("ROW".equals(templateType)) {
        	allWeekDays.forEach(day -> headers.add(Collections.singletonList(day)));
            BpimDateUtil.getDatesForMonths(13).forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.YEAR_MONTH))));

        } else if ("COL".equals(templateType)) {
            headers.add(Collections.singletonList("日期"));
            headers.add(Collections.singletonList("数量"));

            // 新增代码：生成最近30天和12个月的数据
            List<List<String>> dataRows = new ArrayList<>();

            // 生成最近30天的数据
            allWeekDays.forEach(day -> {
                List<String> row = new ArrayList<>(Collections.nCopies(4, "")); // 初始化4列
                row.set(3, day); // 日期放在第4列
                dataRows.add(row);
            });

            // 生成12个月的数据
            BpimDateUtil.getDatesForNextNMonths(12).forEach(day -> {
                List<String> row = new ArrayList<>(Collections.nCopies(4, "")); // 初始化4列
                row.set(3, DateUtils.dateToString(day, DateUtils.YEAR_MONTH)); // 日期放在第4列
                dataRows.add(row);
            });

        }

        EasyExcelFactory.write(out).sheet("装车需求提报(出口)").head(headers).registerWriteHandler(new CustomColumnWidthHandler()).doWrite(Collections.emptyList());
	}

	@Override
	public BaseResponse<Void> importDemand(String originVersionId, String importType, String templateType, 
			String projectDemandType, String checkFlag, MultipartFile submissionFile) {
		Map<String, String> extMap = MapUtil.newHashMap();
        extMap.put("originVersionId", originVersionId);
        extMap.put("importType", importType);
        extMap.put("checkFlag", checkFlag);
        extMap.put("projectDemandType", projectDemandType);
        try {
            if (submissionFile.isEmpty()) {
                throw new BusinessException("文件为空");
            }
            ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 2, 0, extMap);
            EasyExcelFactory.read(submissionFile.getInputStream(), excelDynamicDataListener).sheet().doReadSync();
        } catch (Exception e) {
            throw new BusinessException("导入失败, {0}", e.getLocalizedMessage());
        }

        String successInfo = extMap.get("successInfo");
        String errorInfo = extMap.get("errorInfo");
        if (StringUtils.isBlank(errorInfo)) {
            return BaseResponse.success(successInfo);
        } else {
            return BaseResponse.error(successInfo + errorInfo);
        }
	}
	
	@Resource
    private OemService oemService;
	
	@Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private OemVehicleModelService oemVehicleModelService;
    
    @Resource
    @Lazy
    private LoadingComponentExitHandler loadingComponentExitHandler;
	
	@Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        String scenario = SystemHolder.getScenario();
        List<String> collect = new ArrayList<>(headers.values());
        if (!collect.contains("主机厂编码") || !collect.contains("产品编码") || !collect.contains("客户零件号")) {
            throw new BusinessException("导入数据模板错误，请检查");
        }
        if (CollectionUtils.isEmpty(data)) {
            throw new BusinessException("模板数据为空,没有数据");
        }
        String originVersionId = extMap.get("originVersionId");
        String contentType = extMap.get("contentType");
        String templateType = extMap.get("templateType");
        String demandCategory = extMap.get("projectDemandType");
        String checkFlag = extMap.get("checkFlag");

        // 处理第二种模板格式
        if ("COL".equals(templateType)) {
            Map<Integer, String> newHeaders = new LinkedHashMap<>();
            Map<String, Map<Integer, String>> newRowMap = new LinkedHashMap<>();
            // 复制固定列
            for (int i = 0; i < 3; i++) {
                newHeaders.put(i, headers.get(i));
            }
            // 创建日期列
            Set<String> uniqueDates = new TreeSet<>();
            for (Map<Integer, String> row : data) {
                uniqueDates.add(row.get(3));
            }
            // 添加日期列到新的headers
            int columnIndex = 3;
            for (String date : uniqueDates) {
                newHeaders.put(columnIndex++, date);
            }

            for (Map<Integer, String> row : data) {
                String key = (row.get(0) + "|" + row.get(1) + "|" + row.get(2)).trim().toLowerCase();
                Map<Integer, String> newRow = newRowMap.computeIfAbsent(key, k -> {
                    Map<Integer, String> r = new LinkedHashMap<>();
                    for (int i = 0; i < newHeaders.size(); i++) {
                        r.put(i, i < 3 ? row.get(i).trim() : null);
                    }
                    return r;
                });

                String date = row.get(3).trim();
                String quantity = row.get(4);
                int dateColumnIndex = new ArrayList<>(newHeaders.values()).indexOf(date);
                newRow.put(dateColumnIndex, StringUtils.isBlank(quantity) ? null : quantity.trim());
            }
            // 更新headers和data
            headers.clear();
            headers.putAll(newHeaders);
            data.clear();
            data.addAll(newRowMap.values());
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOList = oemService.selectByParams(queryMap);

        Set<String> oemCodes = getOemNamesFromData(data);
        if (CollectionUtils.isEmpty(oemCodes)) {
            throw new BusinessException("未能从数据中获取有效的主机厂编码");
        }

        Map<String, String> oemCodeToNameMap = oemVOList.stream().filter(Objects::nonNull).filter(oemVO -> oemVO.getOemCode() != null && oemVO.getOemName() != null)
        		.collect(Collectors.toMap(OemVO::getOemCode, OemVO::getOemName, (v1, v2) -> v1));
        Set<String> notFindOemCodes = oemCodes.stream().filter(e -> !oemCodeToNameMap.containsKey(e)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notFindOemCodes)) {
            throw new BusinessException("未能匹配到有效的主机厂名称:" + String.join(",", notFindOemCodes));
        }

        //校验主机厂，产品绑定关系
        List<String> productCodes = data.stream().map(row -> row.get(1)).filter(StringUtils::isNotBlank).collect(Collectors.toList());


        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION", "INTERNAL", null);
        String saleRangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> productInfoList = mdsFeign.selectProductStockPointByParams(scenario, ImmutableMap.of(
                "productCodes", productCodes));

        Map<String, NewProductStockPointVO> productInfoMap = productInfoList.stream().collect(Collectors.toMap(e -> e.getProductCode() + "&" + e.getStockPointCode(), Function.identity(), (v1, v2) -> v1));

        Map<String, List<String>> fg2SaMap = mdsFeign.selectFg2SaMap(scenario, productCodes);
        List<String> saCodes = fg2SaMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<String> allProductCodes = new ArrayList<>();
        allProductCodes.addAll(saCodes);
        List<String> workPorductCodes = productInfoList.stream().filter( e-> "制造".equals(e.getSupplyType()))
        		.map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());
        allProductCodes.addAll(workPorductCodes);
        List<String> productStockPointBaseCodes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(allProductCodes)) {
        	productStockPointBaseCodes = mdsFeign.selectProductStockPointBaseByParams(scenario, ImmutableMap.of("productCodeList", allProductCodes))
            		.stream().map(MdsProductStockPointBaseVO::getProductCode).distinct().collect(Collectors.toList());
        }
        //  提取 productCode 并记录对应的行号
        Map<String, Integer> productCodeRowMap = new HashMap<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String productCode = rowMap.get(1);
            if (StringUtils.isNotBlank(productCode)) {
                productCodeRowMap.put(productCode, i + 2);
            }
        }

        Set<String> unmatchedProducts = new HashSet<>();
        for (String productCode : productCodes) {
        	NewProductStockPointVO productInfo = productInfoMap.get(productCode + "&" + saleRangeData);
            if (!productStockPointBaseCodes.contains(productCode) 
            		&& "制造".equals(productInfo.getSupplyType())) {
                unmatchedProducts.add(productCode);
            }
        }

        if (!unmatchedProducts.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder();
            for (String productCode : unmatchedProducts) {
                Integer rowNum = productCodeRowMap.get(productCode);
                errorMsg.append(String.format("行数：%d，产品编码%s未能匹配到有效的产品工艺数据，请联系工艺维护%n", rowNum, productCode));
            }
            throw new BusinessException(errorMsg.toString());
        }
        List<OemVehicleModelVO> oemVehicleModelList = oemVehicleModelService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "oemCodeList", new ArrayList<>(oemCodes)));
        Map<String, List<String>> vehicleOmesMap = oemVehicleModelList.stream().collect(Collectors.groupingBy(OemVehicleModelVO::getOemVehicleModelCode, Collectors.mapping(OemVehicleModelVO::getOemCode, Collectors.toList())));

        //获取当前月的下标和今天到月底的下标
//        List<Integer> monthDayIndexList = getMonthDayIndexList(headers);
//        Integer currentMonthIndex = monthDayIndexList.get(monthDayIndexList.size() - 1);
//        monthDayIndexList.remove(monthDayIndexList.size() - 1);
//        List<Integer> errorIndex = new ArrayList<>();
//        List<Integer> checkIndex = new ArrayList<>();
//        List<String> checkProductCodes = new ArrayList<>();
//        List<String> checkOemCodes = new ArrayList<>();

        //获取生产组织库存点
        BaseResponse<ScenarioBusinessRangeVO> productOrgRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "PRODUCT_ORGANIZATION", "INTERNAL", null);
        String productOrgRangeDate = productOrgRange.getData().getRangeData();
        List<String> productOrgStockPointCodes = Arrays.asList(productOrgRangeDate.split(","));
        List<String> productOrgProductIds = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String omeCode = rowMap.get(0);
            String productCode = rowMap.get(1);
            NewProductStockPointVO productInfo = productInfoMap.get(productCode + "&" + saleRangeData);
            int rowNum = i + 2;
            if (productInfo == null) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "物料未维护，请联系工艺维护");
            }
            if (!YesOrNoEnum.YES.getCode().equals(productInfo.getEnabled())) {
                throw new BusinessException("行数：" + rowNum + ",产品编码不是有效的，请联系工艺维护");
            }
            if (!productStockPointBaseCodes.contains(productCode) && "制造".equals(productInfo.getSupplyType())) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "不存在工艺基础数据，请联系工艺维护");
            }
            if (fg2SaMap.containsKey(productCode) && "制造".equals(productInfo.getSupplyType())) {
                List<String> saCodeList = fg2SaMap.get(productCode);
                List<String> diffSection = new ArrayList<>(com.yhl.platform.common.utils.CollectionUtils.getDiffSection(saCodeList, productStockPointBaseCodes));
                if (CollectionUtils.isNotEmpty(diffSection)) {
                    String join = String.join(",", diffSection);
                    throw new BusinessException("行数：" + rowNum + ",供应类型为制造的物料编码" + join + "不存在工艺基础数据，请联系工艺维护");
                }
            }
            String vehicleModelCode = productInfo.getVehicleModelCode();
            if (StringUtils.isEmpty(vehicleModelCode)) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "对应车型为空,请检查");
            }
            List<String> omeCodes = vehicleOmesMap.get(vehicleModelCode);
            if (CollectionUtils.isEmpty(omeCodes) || !omeCodes.contains(omeCode)) {
                throw new BusinessException("行数：" + rowNum + ",产品编码 " + productCode + "对应车型与主机厂:" + omeCode + "(" + oemCodeToNameMap.get(omeCode) + ")关联为空,请检查");
            }
            //判断采购计划类别的库存点是否在生产组织库存点
            String stockPointCode = productInfo.getPoCategory().split("\\.")[1];
            if (productOrgStockPointCodes.contains(stockPointCode) 
            		&& productInfoMap.containsKey(productCode + "&" + stockPointCode) 
            		&& !productOrgProductIds.contains(productInfo.getId())
            		&& "制造".equals(productInfo.getSupplyType())) {
                productOrgProductIds.add(productInfoMap.get(productCode + "&" + stockPointCode).getId());
            }

//            String currMonthQty = rowMap.get(currentMonthIndex);
//            if (StringUtils.isEmpty(currMonthQty)) {
//                //校验存量数据
//                if (!checkOemCodes.contains(omeCode)) {
//                    checkOemCodes.add(omeCode);
//                }
//                if (!checkProductCodes.contains(productCode)) {
//                    checkProductCodes.add(productCode);
//                }
//                checkIndex.add(i);
//                continue;
//            }
//            //校验当月发货的汇总量（如今天是5月15日，汇总量为2025-05-15 至2025-05-31）是否大于当月预测量
//            BigDecimal totalQty = BigDecimal.ZERO;
//            for (Integer dayIndex : monthDayIndexList) {
//                String dayQty = rowMap.get(dayIndex);
//                if (StringUtils.isNotEmpty(dayQty)) {
//                    totalQty = totalQty.add(new BigDecimal(dayQty));
//                }
//            }
//            if (totalQty.compareTo(new BigDecimal(currMonthQty)) > 0) {
//                errorIndex.add(rowNum);
//            }
        }

        //校验产品工艺路径
        Map<String, String> checkPorductRoutingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productOrgProductIds)) {
            long startTime = System.currentTimeMillis();
            checkPorductRoutingMap = mdsFeign.checkPorductRouting(scenario, productOrgProductIds);
            log.info("装车需求提报导入-校验工艺路径数据耗时：{}", (System.currentTimeMillis() - startTime));
        }
        List<String> errorMsgList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String productCode = rowMap.get(1);
            int rowNum = i + 2;
            if (checkPorductRoutingMap.containsKey(productCode)) {
                errorMsgList.add("行数：" + rowNum + ",产品编码:" + productCode + "," + checkPorductRoutingMap.get(productCode));
                checkPorductRoutingMap.remove(productCode);
            }
        }
        //如果MAP中还有元素，那就是输入物品中的工艺类型为制造的产品
        boolean firstFlag = true;
        for (Entry<String, String> checkPorductEntry : checkPorductRoutingMap.entrySet()) {
            if (firstFlag) {
                errorMsgList.add("供应类型为制造的物料, 产品编码:" + checkPorductEntry.getKey() + "," + checkPorductEntry.getValue());
            } else {
                errorMsgList.add("产品编码:" + checkPorductEntry.getKey() + "," + checkPorductEntry.getValue());
            }
            firstFlag = false;
        }
        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            throw new BusinessException("导入失败：" + String.join("<br/>", errorMsgList));
        }

//        //进行发货数量和预测校验
//        if (CollectionUtils.isNotEmpty(checkOemCodes) && !YesOrNoEnum.YES.getCode().equals(checkFlag)) {
//            //校验存量数据
//            Map<String, Object> loadingParamMap = new HashMap<>();
//            loadingParamMap.put("versionId", originVersionId);
//            loadingParamMap.put("enabled", YesOrNoEnum.YES.getCode());
//            loadingParamMap.put("productCodes", checkProductCodes);
//            loadingParamMap.put("oemCodes", checkOemCodes);
//            loadingParamMap.put("demandCategory", demandCategory);
//            List<LoadingDemandSubmissionVO> oldLodLoadingDemandSubmissionVOS = this.selectByParams(loadingParamMap);
//            Map<String, String> oldLodLoadingDemandSubmissionMap = oldLodLoadingDemandSubmissionVOS.stream().collect(Collectors.toMap(e -> e.getOemCode() + "&" + e.getProductCode(), LoadingDemandSubmissionVO::getId, (v1, v2) -> v1));
//            //查询获取历史的装车需求详细信息（只差月份）
//            Map<String, BigDecimal> submissionDetailVOMap = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(oldLodLoadingDemandSubmissionVOS)) {
//                List<String> oldSubmissionIds = oldLodLoadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
//                List<LoadingDemandSubmissionDetailVO> oldLodLoadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "submissionType", GranularityEnum.MONTH.getCode(), "demandTime", DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH), "submissionIds", oldSubmissionIds));
//                submissionDetailVOMap = oldLodLoadingDemandSubmissionDetailVOS.stream().filter(e -> e.getDemandQuantity() != null).collect(Collectors.toMap(LoadingDemandSubmissionDetailVO::getSubmissionId, LoadingDemandSubmissionDetailVO::getDemandQuantity, (v1, v2) -> v1));
//            }
//            for (int i = 0; i < data.size(); i++) {
//                if (!checkIndex.contains(i)) {
//                    continue;
//                }
//                Integer rowNum = i + 2;
//                Map<Integer, String> rowMap = data.get(i);
//                String omeCode = rowMap.get(0);
//                String productCode = rowMap.get(1);
//                String submissionId = oldLodLoadingDemandSubmissionMap.get(omeCode + "&" + productCode);
//                if (submissionId == null) {
//                    continue;
//                }
//                BigDecimal totalQty = submissionDetailVOMap.getOrDefault(submissionId, BigDecimal.ZERO);
//                BigDecimal totalDayQty = BigDecimal.ZERO;
//                for (Integer dayIndex : monthDayIndexList) {
//                    String dayQty = rowMap.get(dayIndex);
//                    if (StringUtils.isNotEmpty(dayQty)) {
//                        totalDayQty = totalDayQty.add(new BigDecimal(dayQty));
//                    }
//                }
//                if (totalDayQty.compareTo(totalQty) > 0) {
//                    errorIndex.add(rowNum);
//                }
//            }
//        }
//        if (CollectionUtils.isNotEmpty(errorIndex) && !YesOrNoEnum.YES.getCode().equals(checkFlag)) {
//            Collections.sort(errorIndex);
//            String errorIndexMsg = errorIndex.stream().map(Object::toString).collect(Collectors.joining(", "));
//            throw new BusinessException("行数" + errorIndexMsg + "，发货数量大于预测数量，如确认请点击确定按钮");
//        }

        if ("full".equals(extMap.get("importType"))) {
            // 全量导入 先删除需求提报全部数据
            List<String> waitDeleteIds = loadingDemandSubmissionDetailExitService.selectByOriginIdAndOemCode(originVersionId, null, contentType);
            this.deleteData(originVersionId);
            // 删除历史提报详情数据
            loadingDemandSubmissionDetailExitService.doDelete(waitDeleteIds);
        }

        // 对每个oemCode分别处理数据
        extMap.put("oemCode", String.join(",", oemCodes));
        loadingComponentExitHandler.handle(contentType, headers, data, extMap);
    }
	
	private void deleteData(String originVersionId) {
        loadingDemandSubmissionExitDao.deleteData(originVersionId, null);
    }
	
	private Set<String> getOemNamesFromData(List<Map<Integer, String>> data) {
        return data.stream().map(row -> row.get(0)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }
	
	/**
     * 获取当前月的下标和今天到月底的下标
     *
     * @param headers 表头列表
     */
    private List<Integer> getMonthDayIndexList(Map<Integer, String> headers) {
        LocalDate today = LocalDate.now();
        // 获取本月的最后一天
        LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 创建列表存储每天的字符串格式
        List<String> currMonthDayList = new ArrayList<>();
        // 从今天开始遍历到最后一天
        for (LocalDate date = today; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
            currMonthDayList.add(date.format(formatter));
        }
        String currYearMonth = DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH);
        Integer currYearMonthIndex = 0;
        List<Integer> indexList = new ArrayList<>();
        for (Entry<Integer, String> headerEntry : headers.entrySet()) {
            if (currMonthDayList.contains(headerEntry.getValue())) {
                indexList.add(headerEntry.getKey());
            }
            if (currYearMonth.equals(headerEntry.getValue())) {
                currYearMonthIndex = headerEntry.getKey();
            }
        }
        indexList.add(currYearMonthIndex);
        return indexList;
    }

	@Override
	public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
		if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        List<String> submissionExitIds = versionDTOList.stream().map(RemoveVersionDTO::getId).distinct().collect(Collectors.toList());
        // 删除主数据
        loadingDemandSubmissionExitDao.deleteBatch(submissionExitIds);
        // 删除明细数据
        loadingDemandSubmissionDetailExitService.deleteBySubmissionExitIds(submissionExitIds);
        return 1;
	}

}
