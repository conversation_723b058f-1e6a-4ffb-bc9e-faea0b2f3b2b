package com.yhl.scp.dfp.clean.service.impl;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.convertor.CleanDemandDataExitConvertor;
import com.yhl.scp.dfp.clean.domain.entity.CleanDemandDataExitDO;
import com.yhl.scp.dfp.clean.domain.service.CleanDemandDataExitDomainService;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDetailExitDTO;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataExitDTO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailExitDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataExitDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataDetailExitPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataExitPO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailExitService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataExitService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataExitVO;
import com.yhl.scp.dfp.common.enums.DemandTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandVersionPO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailExitService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionExitService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailExitVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionExitVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionFutureExitVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.service.OemProductLineMapService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemProductLineMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import cn.hutool.core.map.MapUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>CleanDemandDataExitServiceImpl</code>
 * <p>
 * 日需求数据(出口)应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06 11:06:17
 */
@Slf4j
@Service
public class CleanDemandDataExitServiceImpl extends AbstractService implements CleanDemandDataExitService {
	
	public static final String YMD_PATTERN = "yyyyMMdd";

    public static final String YM_PATTERN = "yyyyMM";

    @Resource
    private CleanDemandDataExitDao cleanDemandDataExitDao;

    @Resource
    private CleanDemandDataExitDomainService cleanDemandDataExitDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private CleanDemandDataDetailExitDao cleanDemandDataDetailExitDao;
    
    @Resource
    private DemandVersionDao demandVersionDao;
    
    @Resource
    private NewMdsFeign newMdsFeign;
    
    @Resource
    private LoadingDemandSubmissionExitService loadingDemandSubmissionExitService;
    
    @Resource
    private CleanDemandDataDetailExitService cleanDemandDataDetailExitService;
    
    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;
    
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    
    @Resource
    private LoadingDemandSubmissionDetailExitService loadingDemandSubmissionDetailExitService;
    
    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;
    
    @Resource
    private ConsistenceDemandForecastDataDao consistenceDemandForecastDataDao;

    @Resource
    private ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;
    
    @Resource
    private OemProductLineMapService oemProductLineMapService;
    
    @Resource
    DfpResourceCalendarService dfpResourceCalendarService;
    
    @Resource
    private OemService oemService;

    @Override
    public BaseResponse<Void> doCreate(CleanDemandDataExitDTO cleanDemandDataExitDTO) {
        // 0.数据转换
        CleanDemandDataExitDO cleanDemandDataExitDO = CleanDemandDataExitConvertor.INSTANCE.dto2Do(cleanDemandDataExitDTO);
        CleanDemandDataExitPO cleanDemandDataExitPO = CleanDemandDataExitConvertor.INSTANCE.dto2Po(cleanDemandDataExitDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        cleanDemandDataExitDomainService.validation(cleanDemandDataExitDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(cleanDemandDataExitPO);
        cleanDemandDataExitDao.insert(cleanDemandDataExitPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CleanDemandDataExitDTO cleanDemandDataExitDTO) {
        // 0.数据转换
        CleanDemandDataExitDO cleanDemandDataExitDO = CleanDemandDataExitConvertor.INSTANCE.dto2Do(cleanDemandDataExitDTO);
        CleanDemandDataExitPO cleanDemandDataExitPO = CleanDemandDataExitConvertor.INSTANCE.dto2Po(cleanDemandDataExitDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        cleanDemandDataExitDomainService.validation(cleanDemandDataExitDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(cleanDemandDataExitPO);
        cleanDemandDataExitDao.update(cleanDemandDataExitPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CleanDemandDataExitDTO> list) {
        List<CleanDemandDataExitPO> newList = CleanDemandDataExitConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        cleanDemandDataExitDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<CleanDemandDataExitDTO> list) {
        List<CleanDemandDataExitPO> newList = CleanDemandDataExitConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        cleanDemandDataExitDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return cleanDemandDataExitDao.deleteBatch(idList);
        }
        return cleanDemandDataExitDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CleanDemandDataExitVO selectByPrimaryKey(String id) {
        CleanDemandDataExitPO po = cleanDemandDataExitDao.selectByPrimaryKey(id);
        return CleanDemandDataExitConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_clean_demand_data_exit")
    public List<CleanDemandDataExitVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_clean_demand_data_exit")
    public List<CleanDemandDataExitVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CleanDemandDataExitVO> dataList = cleanDemandDataExitDao.selectByCondition(sortParam, queryCriteriaParam);
        CleanDemandDataExitServiceImpl target = springBeanUtils.getBean(CleanDemandDataExitServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CleanDemandDataExitVO> selectByParams(Map<String, Object> params) {
        List<CleanDemandDataExitPO> list = cleanDemandDataExitDao.selectByParams(params);
        return CleanDemandDataExitConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CleanDemandDataExitVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CLEAN_DEMAND_DATA_EXIT.getCode();
    }

    @Override
    public List<CleanDemandDataExitVO> invocation(List<CleanDemandDataExitVO> dataList, Map<String, Object> params, String invocation) {
    	if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        // 设置明细数据
        List<String> demandExitIds = dataList.stream().map(CleanDemandDataExitVO::getId).collect(Collectors.toList());

        List<CleanDemandDataDetailExitPO> cleanDemandDataDetailExitList = cleanDemandDataDetailExitDao
        		.selectByCleanDemandDataExitIds(demandExitIds);
        Map<String, List<CleanDemandDataDetailExitPO>> detailMap = cleanDemandDataDetailExitList.stream()
                .collect(Collectors.groupingBy(CleanDemandDataDetailExitPO::getCleanDemandDataExitId));
        for (CleanDemandDataExitVO data : dataList) {
            List<CleanDemandDataDetailExitPO> details = detailMap.get(data.getId());
            if (CollectionUtils.isNotEmpty(details)) {
                Map<String, String> detailList = details.stream().collect(Collectors
                        .toMap(x -> DateUtils.dateToString(x.getDemandTime(), YMD_PATTERN),
                                x -> x.getDemandQuantity() == null ? "" : x.getDemandQuantity().toString(),
                                (t1, t2) -> t2));
                data.setDetailList(detailList);
            }
        }
        return dataList;
    }

	@Override
	public List<LabelValue<String>> selectOemDropdown(String versionId) {
		return cleanDemandDataExitDao.selectOemDropdown(versionId).stream()
                .map(x -> new LabelValue<>(x.getLabel() + "(" + x.getValue() + ")", x.getValue()))
                .sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList());
	}

	@Override
	public void doRecalculate(String versionCode) {
		Map<String, Object> params = new HashMap<>(2);
        params.put("versionCode", versionCode);
        params.put("versionType", VersionTypeEnum.CLEAN_DEMAND.getCode());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到需要重新计算的日需求版本");
        }
        processDataByVersionId(demandVersionPO.getId());
	}
	
	/**
     * 根据版本号处理数据
     *
     * @param versionId 版本ID
     */
    @SneakyThrows
    public void processDataByVersionId(String versionId) {
    	// 查询当前用户负责的物料数据
    	List<NewProductStockPointVO> newProductStockPointVOS =
                this.newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                        ImmutableMap.of("orderPlanner", SystemHolder.getUserId(),
                        		"enabled", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("当前用户没有配置物料数据权限");
        }
        List<String> orderPlannerProductCodes = newProductStockPointVOS.stream()
        		.map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
    	ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
        // 获取对应版本数据
        List<CleanDemandDataExitVO> cleanDemandDataExitPOList = this.selectByParams(ImmutableMap
        		.of("versionId", versionId, "productCodeList", orderPlannerProductCodes));
        List<String> deletedDataExitIds = cleanDemandDataExitPOList.stream().map(CleanDemandDataExitVO::getId)
    			.collect(Collectors.toList());
        //过滤无效物料权限数据
        List<String> unEnabledProductCodes = newProductStockPointVOS.stream()
        		.filter( e-> YesOrNoEnum.NO.getCode().equals(e.getEnabled()))
        		.map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(unEnabledProductCodes)) {
        	orderPlannerProductCodes.removeAll(unEnabledProductCodes);
        	if(CollectionUtils.isEmpty(orderPlannerProductCodes)) {
        		//执行删除逻辑，不计算
        		this.doDelete(deletedDataExitIds);
                cleanDemandDataDetailExitDao.deleteByDataIds(deletedDataExitIds);
                return;
        	}
        }
        
        //查询装车需求提报数据
        DemandVersionPO demandVersionPO = demandVersionDao.selectByPrimaryKey(versionId);
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", demandVersionPO.getOriginVersionId());
        params.put("productCodes", orderPlannerProductCodes);
        List<LoadingDemandSubmissionExitVO> loadingDemandSubmissionExitVOS = loadingDemandSubmissionExitService.selectByParams(params)
                .stream().filter(item -> StringUtils.isNotBlank(item.getProductCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(loadingDemandSubmissionExitVOS)) {
        	//执行删除逻辑，不计算
    		this.doDelete(deletedDataExitIds);
            cleanDemandDataDetailExitDao.deleteByDataIds(deletedDataExitIds);
            return;
        }
        Map<String,List<LoadingDemandSubmissionExitVO>> loadingDemandSubmissionDemandExitMap =
        		loadingDemandSubmissionExitVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionExitVO::getDemandCategory));
        //获取主机厂
        List<String> oemCodeList = loadingDemandSubmissionExitVOS.stream().map(LoadingDemandSubmissionExitVO::getOemCode)
                .distinct().collect(Collectors.toList());
        Map<String, String> oemBusinessTypeMap = oemService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), "oemCodes" , oemCodeList)).stream()
        		.collect(Collectors.toMap(OemVO::getOemCode,OemVO::getBusinessType,(v1, v2) -> v1));
        //获取装车需求提报主表id
        List<String> submissionExitIds = loadingDemandSubmissionExitVOS.stream().map(LoadingDemandSubmissionExitVO::getId).collect(Collectors.toList());
        // 汇总当月当天前仓库收发货数据
        List<String> productCodeList = loadingDemandSubmissionExitVOS.stream().map(LoadingDemandSubmissionExitVO::getProductCode)
                .distinct().collect(Collectors.toList());
        Map<String, Object> productParams = new HashMap<>();
        productParams.put("productCodeList", productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(), productParams);
        List<String> vehicleModeCodes = Lists.newArrayList(productVehicleModelMap.values());
        
        String currentMonthFirstDay = DateUtils.dateToString(DateUtils.getCurrentMonthFirstDay(), DateUtils.COMMON_DATE_STR3);
        Date currentDate = new Date();
        String currentMonth = DateUtils.dateToString(currentDate, DateUtils.YEAR_MONTH);
        String currentMonthCurrentDay = DateUtils.dateToString(currentDate, DateUtils.COMMON_DATE_STR3);
        try {    
        	//主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
	        CompletableFuture<Map<String, BigDecimal>> releaseMapFuture = CompletableFuture.supplyAsync(
	                () -> getWarehouseReleaseRecordDeliveryQtyMap(productCodeList,
	        				productVehicleModelMap, vehicleModeCodes, currentMonthFirstDay, currentMonthCurrentDay), threadPoolExecutor);
	        
	        //主机厂，车型，产品编码对应的发货数量(获取仓库发货至中转库的发货数量)
	        CompletableFuture<Map<String, BigDecimal>> releaseToMapFuture = CompletableFuture.supplyAsync(
	                () -> getWarehouseReleaseToWarehouseDeliveryQtyMap(productCodeList,
	    					productVehicleModelMap, vehicleModeCodes, currentMonthFirstDay, currentMonthCurrentDay), threadPoolExecutor);
	
			//异步查询装车需求提报数据，资源日历
	        CompletableFuture<LoadingDemandSubmissionFutureExitVO> loadingDemandSubmissionDetailFuture = CompletableFuture.supplyAsync(() -> {
	        		LoadingDemandSubmissionFutureExitVO result = new LoadingDemandSubmissionFutureExitVO();
	            	Map<String, Object> detailQueryMap = MapUtil.newHashMap();
	        		detailQueryMap.put("submissionExitIds", submissionExitIds);
	        		List<LoadingDemandSubmissionDetailExitVO> loadingDemandSubmissionDetailExitVOS = loadingDemandSubmissionDetailExitService
	        				.selectByParams(detailQueryMap);
	        		//日装车需求提报数据
	                List<LoadingDemandSubmissionDetailExitVO> loadingDemandSubmissionDetailExitVOSDay = loadingDemandSubmissionDetailExitVOS.stream()
	                        .filter(item -> StringUtils.equals(GranularityEnum.DAY.getCode(), item.getSubmissionType())).collect(Collectors.toList());
	                Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailDayMap = CollectionUtils.isEmpty(loadingDemandSubmissionDetailExitVOSDay)
	                        ? MapUtil.newHashMap() : loadingDemandSubmissionDetailExitVOSDay.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailExitVO::getSubmissionExitId));
	                result.setDetailDayMap(detailDayMap);
	                
	                //时间字符串格式转换，将yyyy-MM-dd HH:mm:ss 转换为 yyyy-MM-dd
	                String format1 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
	                loadingDemandSubmissionDetailExitVOSDay.forEach(item -> {
	                    if (StringUtils.isNotBlank(item.getDemandTime()) && item.getDemandTime().matches(format1)){
	                        item.setDemandTime(DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(),
	                                DateUtils.COMMON_DATE_STR1), DateUtils.COMMON_DATE_STR3));
	                    }
	                });
	                Map<String, List<ResourceCalendarVO>> monthCalendarMap = getMonthCalendarMap(loadingDemandSubmissionDetailExitVOSDay);
	                result.setMonthCalendarMap(monthCalendarMap);
	                
	                //月装车需求提报数据
	                List<LoadingDemandSubmissionDetailExitVO> loadingDemandSubmissionDetailExitVOSMonth = loadingDemandSubmissionDetailExitVOS.stream()
	                        .filter(item -> StringUtils.equals(GranularityEnum.MONTH.getCode(), item.getSubmissionType())).collect(Collectors.toList());
	                Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailMonthMap = CollectionUtils.isEmpty(loadingDemandSubmissionDetailExitVOSMonth)
	                        ? MapUtil.newHashMap() : loadingDemandSubmissionDetailExitVOSMonth.stream()
	                        		.collect(Collectors.groupingBy(LoadingDemandSubmissionDetailExitVO::getSubmissionExitId));
	                result.setDetailMonthMap(detailMonthMap);
	        		return result;
	        }, threadPoolExecutor);
	
	        
	        // 第一层key获取一致性需求预测的data、第二层key获取一致性需求预测的dataDetail
	        CompletableFuture<Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>>> consistenceRealtionMapFuture = CompletableFuture.supplyAsync(() -> {
	        	// 根据计划周期取已发布的最新版一致性业务预测版本
	            String forecastVersionId = consistenceDemandForecastVersionDao.selectLatestPublishedVersionId(null);
	            // 根据版本ID和预测类型和年月获取数据
	            Map<String, Object> consistenceVersionParams = new HashMap<>();
	            consistenceVersionParams.put("versionId", forecastVersionId);
	            List<ConsistenceDemandForecastDataPO> consistenceDataList = consistenceDemandForecastDataDao.selectByParams(consistenceVersionParams);
	        	Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap = new HashMap<>();
	            if (CollectionUtils.isNotEmpty(consistenceDataList)) {
	                List<String> demandForecastIds = consistenceDataList.stream().map(ConsistenceDemandForecastDataPO::getId)
	                        .collect(Collectors.toList());
	                List<ConsistenceDemandForecastDataDetailPO> consistenceDetailList = consistenceDemandForecastDataDetailDao
	                        .selectByConsistenceDemandForecastDataIds(demandForecastIds).stream()
	                        .filter(item -> item.getForecastTime() != null).collect(Collectors.toList());
	                Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceDetailMap;
	                if (CollectionUtils.isNotEmpty(consistenceDetailList)) {
	                    consistenceDetailMap = consistenceDetailList.stream().collect(Collectors
	                            .groupingBy(ConsistenceDemandForecastDataDetailPO::getConsistenceDemandForecastDataId, Collectors
	                                    .toMap(x -> DateUtils.dateToString(x.getForecastTime(), DateUtils.YEAR_MONTH),
	                                            Function.identity(),
	                                            (k1, k2) -> k1)));
	
	                    // 获取主机厂、车型、产品编码和一致性业务预测数据的关联关系
	                    Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> finalConsistenceDetailMap = consistenceDetailMap;
	                    consistenceRealtionMap = consistenceDataList.stream().collect(Collectors
	                            .toMap(x -> String.join("&", x.getOemCode(), x.getVehicleModelCode(), x.getProductCode()),
	                                    y -> finalConsistenceDetailMap.get(y.getId()),
	                                    (k1, k2) -> k1));
	                }
	            }
	            return consistenceRealtionMap;
	        }, threadPoolExecutor);
	        Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap = new HashMap<>();
	        //查询主机厂厂线信息
	        List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapService.selectByOemCodeList(oemCodeList);
	        CompletableFuture<Map<String, List<ResourceCalendarVO>>> resourceCodeCalendarMapFuture = null;
	        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
	            oemProductLineMapVOMap = oemProductLineMapVOS.stream().collect(Collectors
	                    .groupingBy(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode())));
	            List<String> lineCodeList = oemProductLineMapVOS.stream().map(OemProductLineMapVO::getLineCode)
	                    .distinct().collect(Collectors.toList());
	            resourceCodeCalendarMapFuture = CompletableFuture.supplyAsync(() -> {
	            	// 查询装车日历
	            	Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
	                List<ResourceCalendarVO> resourceCalendarVOS =
	                        dfpResourceCalendarService.selectResourceByStandardResourceIds(lineCodeList);
	                if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
	                	resourceCodeCalendarMap = resourceCalendarVOS.stream().collect(Collectors
	                            .groupingBy(item -> String.join("&", item.getStandardResourceId(), item.getPhysicalResourceId())));
	                }
	                return resourceCodeCalendarMap;
	            }, threadPoolExecutor);
	        }
	        //获取异步查询的装车需求提报数据，资源日历
	        LoadingDemandSubmissionFutureExitVO loadingDemandSubmissionFutureExitVO = loadingDemandSubmissionDetailFuture.join();
	        Map<String, List<ResourceCalendarVO>> monthCalendarMap = loadingDemandSubmissionFutureExitVO.getMonthCalendarMap();
	        Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailDayMap = loadingDemandSubmissionFutureExitVO.getDetailDayMap();
	        Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailMonthMap = loadingDemandSubmissionFutureExitVO.getDetailMonthMap();
	        //获取主机厂、车型、产品编码和一致性业务预测数据的关联关系
	        Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap = consistenceRealtionMapFuture.get();
	        //主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
	        Map<String, BigDecimal> releaseMap = releaseMapFuture.join();
	        Map<String, BigDecimal> releaseToMap = releaseToMapFuture.get();
	        //获取装车日历
	        Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
	        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
	        	resourceCodeCalendarMap = resourceCodeCalendarMapFuture.get();
	        }
	        //重新计算后需要新增的数据
	        List<CleanDemandDataExitDTO> cleanDemandDataExitDTOS = Collections.synchronizedList(new LinkedList<>());
	        List<CleanDemandDataDetailExitDTO> cleanDemandDataDetailExitDTOS = Collections.synchronizedList(new LinkedList<>());
	        for (List<LoadingDemandSubmissionExitVO> list : loadingDemandSubmissionDemandExitMap.values()){
	            processDataByDemandCategory(demandVersionPO.getId(),list,productVehicleModelMap,
	                    oemProductLineMapVOMap,resourceCodeCalendarMap,detailDayMap,detailMonthMap,
	                    consistenceRealtionMap,currentMonth,releaseMap, releaseToMap, monthCalendarMap,
	                    cleanDemandDataExitDTOS, cleanDemandDataDetailExitDTOS, oemBusinessTypeMap);
	        }
        	List<CompletableFuture<Integer>> completableFutureList = new ArrayList<>();
        	if (CollectionUtils.isNotEmpty(cleanDemandDataExitDTOS)) {
    			Lists.partition(cleanDemandDataExitDTOS, 1000).forEach(subList -> {
    				CompletableFuture<Integer> insertDataFuture = CompletableFuture.supplyAsync(() -> {
    					this.doCreateBatch(subList);
    					return 1;
    				}, threadPoolExecutor);
    				completableFutureList.add(insertDataFuture);
    			});
    		}
        	if (CollectionUtils.isNotEmpty(cleanDemandDataDetailExitDTOS)) {
    			Lists.partition(cleanDemandDataDetailExitDTOS, 2000).forEach(subList -> {
    				CompletableFuture<Integer> insertDetailFuture = CompletableFuture.supplyAsync(() -> {
    					cleanDemandDataDetailExitService.doCreateBatch(subList);
    					return 1;
    				}, threadPoolExecutor);
    				completableFutureList.add(insertDetailFuture);
    			});
    		}
        	//等待插入结束
        	completableFutureList.forEach( e -> {
        		e.join();
        	});
        	//异步执行删除
    		if (CollectionUtils.isNotEmpty(deletedDataExitIds)) {
    			//删除原有数据
                this.doDelete(deletedDataExitIds);
                cleanDemandDataDetailExitDao.deleteByDataIds(deletedDataExitIds);
    		}
		} catch (Exception e) {
			 log.error("零件需求汇总-日需求计算失败：", e);
			 throw new BusinessException("零件需求汇总-日需求计算失败");
		}finally {
			CustomThreadPoolFactory.closeOrShutdown(threadPoolExecutor);
		}
    	
    }
    
    public void processDataByDemandCategory(String versionId,
            List<LoadingDemandSubmissionExitVO> loadingDemandSubmissionVOS,
            Map<String, String> productVehicleModelMap,
            Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap,
            Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap,
            Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailDayMap,
            Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailMonthMap,
            Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap,
            String currentMonth,Map<String, BigDecimal> releaseMap, Map<String, BigDecimal> releaseToMap, 
            Map<String, List<ResourceCalendarVO>> monthCalendarMap,
            List<CleanDemandDataExitDTO> cleanDemandDataDTOS, List<CleanDemandDataDetailExitDTO> cleanDemandDataDetailDTOS,
            Map<String, String> oemBusinessTypeMap){
    	List<String> addedLoadDemandDetailIdList = new ArrayList<>();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern(DateUtils.COMMON_DATE_STR1);
        DateTimeFormatter formatterMonth = DateTimeFormatter.ofPattern("yyyy-MM");
        //todo 前置做了demandTime的数据处理，此处应该不需要再单独做判断
        String format1 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
        String format2 = "\\d{4}-\\d{2}-\\d{2}";
        Random random = new Random();
        loadingDemandSubmissionVOS.parallelStream().forEach( loadingDemandSubmissionVO -> {
            String oemCode = loadingDemandSubmissionVO.getOemCode();
            String productCode = loadingDemandSubmissionVO.getProductCode();
            String demandCategory = loadingDemandSubmissionVO.getDemandCategory();
            String vehicleModelCode = productVehicleModelMap.get(productCode);
            // 获取对应的厂线数据 主机厂 + 车型
            List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapVOMap.get(String.join("&", oemCode,
                    vehicleModelCode));

            //获取主机厂装车日历数据
            //需要根据主机厂 + 厂线去获取装车日历数据
            // -->变成产线 + 车型获取装车日历数据
            Map<String, List<ResourceCalendarVO>> oemResourceCalendarsMapOfMonth = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
                List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
                oemProductLineMapVOS.forEach(oemProductLineMapVO -> {
                    List<ResourceCalendarVO> list = resourceCodeCalendarMap.get(String
                            .join("&", oemProductLineMapVO.getLineCode(),vehicleModelCode));
                    if (CollectionUtils.isNotEmpty(list)){
                        //一天当中有多个日历,进行去重,只留一天
                        Map<String,ResourceCalendarVO> map =
                                list.stream().collect(Collectors.toMap(
                                        item -> DateUtils.dateToString(item.getWorkDay()),
                                item -> item,(existing, replacement) -> existing));
                        resourceCalendarVOS.addAll(new ArrayList<>(map.values()));
                    }
                });
                if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
                    oemResourceCalendarsMapOfMonth = resourceCalendarVOS.stream().collect(Collectors
                            .groupingBy(item -> DateUtils.dateToString(item.getWorkDay(), "yyyy-MM")));
                }
            }
            String submissionExitId = UUIDUtil.getUUID();
            CleanDemandDataExitDTO cleanDemandDataDTO = new CleanDemandDataExitDTO();
            cleanDemandDataDTO.setId(submissionExitId);
            cleanDemandDataDTO.setVersionId(versionId);
            cleanDemandDataDTO.setDemandCategory(loadingDemandSubmissionVO.getDemandCategory());
            cleanDemandDataDTO.setOemCode(oemCode);
            cleanDemandDataDTO.setVehicleModelCode(vehicleModelCode);
            cleanDemandDataDTO.setProductCode(productCode);
            cleanDemandDataDTO.setPartName(loadingDemandSubmissionVO.getPartNumber());
            cleanDemandDataDTO.setDemandType(DemandTypeEnum.LOADING_DEMAND.getCode());
            cleanDemandDataDTO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
            cleanDemandDataDTOS.add(cleanDemandDataDTO);
            List<LoadingDemandSubmissionDetailExitVO> submissionDetailVOS = detailDayMap.get(loadingDemandSubmissionVO.getId());
            if (CollectionUtils.isNotEmpty(submissionDetailVOS)) {
            	//获取一致性预测需求数据
                Map<String, ConsistenceDemandForecastDataDetailPO> consistenceDemandForecastDataDetailPOMap = consistenceRealtionMap
                        .get(String.join("&", cleanDemandDataDTO.getOemCode(),
                                cleanDemandDataDTO.getVehicleModelCode(), cleanDemandDataDTO.getProductCode()));

                //获取月份数据
                List<LoadingDemandSubmissionDetailExitVO> monthSubmissionDetailVOList = detailMonthMap.get(loadingDemandSubmissionVO.getId());
                Map<String, List<LoadingDemandSubmissionDetailExitVO>> monthDetailMapOfMonth = new HashMap<>();
                if (CollectionUtils.isNotEmpty(monthSubmissionDetailVOList)) {
                    monthDetailMapOfMonth = monthSubmissionDetailVOList.stream().collect(Collectors
                            .groupingBy(LoadingDemandSubmissionDetailExitVO::getDemandTime));
                }
                //根据月份数据去重
                List<String> monthList = submissionDetailVOS.stream()
                        .map(item -> DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM"))
                        .distinct().collect(Collectors.toList());

                Map<String, List<LoadingDemandSubmissionDetailExitVO>> dayDetailMapOfMonth = submissionDetailVOS.stream().collect(Collectors
                        .groupingBy(item -> DateUtils
                                .dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM")));
                //by需求时间分组，如果时间不为空，不进行均分，则用此map数据创建日需求
                Map<String, List<LoadingDemandSubmissionDetailExitVO>> detailVOMapOfDemandTime =
                        submissionDetailVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailExitVO::getDemandTime));
                
                
                for (String month : monthList) {
                    // 获取月份的天数
                    List<Date> dayDateList = new ArrayList<>();
                    BigDecimal daySummaryQuantity = BigDecimal.ZERO;
                    List<LoadingDemandSubmissionDetailExitVO> detailVOList = dayDetailMapOfMonth.get(month);
                    List<LoadingDemandSubmissionDetailExitVO> avgQuantityDayDetailList = new ArrayList<>();
                    if (CollectionUtils.isEmpty(detailVOList)) {
                        continue;
                    }
                    List<String> demandTimeList =
                            detailVOList.stream().map(LoadingDemandSubmissionDetailExitVO::getDemandTime).distinct().collect(Collectors.toList());
                    // 获取日期最早的数据
                    LoadingDemandSubmissionDetailExitVO earliestDetailVO = detailVOList.stream()
                    		.min(Comparator.comparing(LoadingDemandSubmissionDetailExitVO::getDemandTime)).get();
                    LocalDate earliestLocalDate = null;
                    if (earliestDetailVO.getDemandTime().matches(format1)){
                        earliestLocalDate = LocalDate.parse(earliestDetailVO.getDemandTime(), formatter2);
                    }else if (earliestDetailVO.getDemandTime().matches(format2)){
                        earliestLocalDate = LocalDate.parse(earliestDetailVO.getDemandTime(), formatter);
                    }else {
                        throw new BusinessException("日期格式和预期不匹配:"+earliestDetailVO.getDemandTime());
                    }
                    //从这个月最早的时间开始，获取这天和这个月每周一的时间
                    List<LocalDate> currentMonthWeekDays = new ArrayList<>();
                	// 第一个日期是当天日期
                	LocalDate currentMonday = earliestLocalDate;
                	currentMonthWeekDays.add(currentMonday);
                	// 后续23个日期，每周一
                	if (currentMonday.getDayOfWeek() == DayOfWeek.MONDAY) {
                		currentMonday = currentMonday.plusWeeks(1);
                	}else {
                		currentMonday = currentMonday.with(DayOfWeek.MONDAY).plusWeeks(1);
                	}
                	for (int i = 1; i < 10; i++) {
                		currentMonthWeekDays.add(currentMonday);
                		currentMonday = currentMonday.plusWeeks(1);
                		if(!month.equals(formatterMonth.format(currentMonday))) {
                			break;
                		}
                	}
                    
                    List<ResourceCalendarVO> resourceCalendarVOList = oemResourceCalendarsMapOfMonth.get(month);
                    if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
                    	resourceCalendarVOList = monthCalendarMap.get(month);
                    }
                    //过滤resourceCalendarVOList在earliestLocalDate等于或者之后的日期
                    resourceCalendarVOList = resourceCalendarVOList.stream().filter(item -> item.getWorkDay().after(DateUtils.stringToDate(earliestDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3))
                    || item.getWorkDay().equals(DateUtils.stringToDate(earliestDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3))).collect(Collectors.toList());
                    //装车日历时间范围
                    Map<Date, List<ResourceCalendarVO>> resourceCalendarVOMap =
                            resourceCalendarVOList.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getWorkDay));
                    List<String> oemResourceCalendars = resourceCalendarVOList.stream().map(item -> DateUtils
                            .dateToString(item.getWorkDay())).distinct().collect(Collectors.toList());

                    // 日需求值为null的数量
                    // int nullCount = 0;
                    //Date earliest = null,lastest = null;
                    //取月份剩余天数
                    int valueCount = 0;
                    for (LoadingDemandSubmissionDetailExitVO loadingDemandSubmissionDetailVO : detailVOList) {
                        Date demandDate = DateUtils.stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(),
                                DateUtils.COMMON_DATE_STR3);
                        //计算需要平摊的日需求数据，需求数量为null并且存在装车日历中(如果装车日历不为空)
                        if (null == loadingDemandSubmissionDetailVO.getDemandQuantity()
                                && (CollectionUtils.isEmpty(oemResourceCalendars)
                                || oemResourceCalendars.contains(loadingDemandSubmissionDetailVO.getDemandTime()))) {
                            avgQuantityDayDetailList.add(loadingDemandSubmissionDetailVO);
                            dayDateList.add(demandDate);
                            if (resourceCalendarVOMap.get(demandDate)!=null){
                                valueCount += resourceCalendarVOMap.get(demandDate).size();
                            }
                        } else {
                            if (null != loadingDemandSubmissionDetailVO.getDemandQuantity()) {
                                daySummaryQuantity = daySummaryQuantity.add(loadingDemandSubmissionDetailVO.getDemandQuantity());
                            }
                        }
                    }
                    
                    // 需要填充本月没有的预测数据
                    for (LocalDate localDate : currentMonthWeekDays) {
                        // 日期需要在装车需求提报中不存在的并且是往后的数据
                        String dateStr = localDate.format(formatter);
                        if (!demandTimeList.contains(dateStr) && earliestLocalDate.isBefore(localDate)){
                            if (oemResourceCalendars.contains(dateStr)){
                                Date d = DateUtils.stringToDate(dateStr, DateUtils.COMMON_DATE_STR3);
                                dayDateList.add(d);
                                if (resourceCalendarVOMap.get(d)!=null){
                                    valueCount += resourceCalendarVOMap.get(d).size();
                                }
                            }
                            CleanDemandDataDetailExitDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailExitDTO();
                            cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                            cleanDemandDataDetailDTO.setCleanDemandDataExitId(submissionExitId);
                            cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(dateStr,
                                    DateUtils.COMMON_DATE_STR3));
                            cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(dayDateList)) {
                        //先获取一致性预测需求的数据，如果为空则取装车提报的数据
                        BigDecimal monthQuantity = BigDecimal.ZERO;
                        if (null != consistenceDemandForecastDataDetailPOMap
                                && consistenceDemandForecastDataDetailPOMap.containsKey(month)) {
                            ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO = consistenceDemandForecastDataDetailPOMap.get(month);
                            monthQuantity = consistenceDemandForecastDataDetailPO.getForecastQuantity() != null
                                    ? consistenceDemandForecastDataDetailPO.getForecastQuantity() : BigDecimal.ZERO;
                        } else {
                            List<LoadingDemandSubmissionDetailExitVO> monthLoadingDemandSubmissionDetailVO =
                                    monthDetailMapOfMonth.get(month);
                            if (CollectionUtils.isNotEmpty(monthLoadingDemandSubmissionDetailVO)) {
                                for (LoadingDemandSubmissionDetailExitVO vo : monthLoadingDemandSubmissionDetailVO){
                                    monthQuantity = vo.getDemandQuantity() != null
                                            ? vo.getDemandQuantity() : BigDecimal.ZERO;
                                }
                            }
                        }

                        //向上取整
                        if (null == monthQuantity || monthQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                            for (LoadingDemandSubmissionDetailExitVO demandSubmissionDetailVO : avgQuantityDayDetailList) {
                                CleanDemandDataDetailExitDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailExitDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setCleanDemandDataExitId(submissionExitId);
                                if (StringUtils.isNotBlank(demandSubmissionDetailVO.getDemandTime())) {
                                    cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(demandSubmissionDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                                }
                                cleanDemandDataDetailDTO.setDemandQuantity(0);
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                                addedLoadDemandDetailIdList.add(demandSubmissionDetailVO.getId());
                            }
                        } else {
                            BigDecimal subtract = monthQuantity.subtract(daySummaryQuantity);
                            if (month.equals(currentMonth)) {
                                String uniqueKey = String.join("&", oemCode, vehicleModelCode, productCode, month);
                                //扣减仓库收发货/中转库发货数量
                                String businessType = oemBusinessTypeMap.get(oemCode);
                                if(OemBusinessTypeEnum.MTS.getCode().equals(businessType)
                            			&& ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(demandCategory)) {
                                    subtract = subtract.subtract(releaseToMap.getOrDefault(uniqueKey, BigDecimal.ZERO));
                                }else {
                                	subtract = subtract.subtract(releaseMap.getOrDefault(uniqueKey, BigDecimal.ZERO));
                                }
                            }
                            List<Integer> shardNumList = new ArrayList<>();
                            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                                if (valueCount > 0){
                                    shardNumList = distributeEvenly(subtract.intValue(), valueCount);
                                }

                            }

                            // 循环日期
                            // 排序dayDateList
                            dayDateList.sort(Comparator.comparing(Date::getTime));

                            int index = 0;
                            for (int i = 0; i < dayDateList.size(); i++) {
                                // 新增
                                Date date = dayDateList.get(i);
                                List<LoadingDemandSubmissionDetailExitVO> loadingDemandSubmissionDetailVOS =
                                        detailVOMapOfDemandTime.get(DateUtils.dateToString(dayDateList.get(i), DateUtils.COMMON_DATE_STR3));
                                CleanDemandDataDetailExitDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailExitDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(DateUtils.dateToString(dayDateList.get(i), DateUtils.COMMON_DATE_STR3), DateUtils.COMMON_DATE_STR3));
                                cleanDemandDataDetailDTO.setCleanDemandDataExitId(submissionExitId);
                                Integer demandQuantity = 0;
                                if (shardNumList.size() - 1 >= index) {
                                    demandQuantity = shardNumList.get(index);
                                }
                                if (resourceCalendarVOMap.get(date)!=null){
                                    demandQuantity = demandQuantity * resourceCalendarVOMap.get(date).size();
                                    index += resourceCalendarVOMap.get(date).size();
                                }else {
                                    index++;
                                }
                                if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOS) && demandQuantity < 0){
                                    continue;
                                }
                                cleanDemandDataDetailDTO.setDemandQuantity(demandQuantity);
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                                if (CollectionUtils.isNotEmpty(loadingDemandSubmissionDetailVOS)){
                                    for (LoadingDemandSubmissionDetailExitVO loadingDemandSubmissionDetailVO : loadingDemandSubmissionDetailVOS) {
                                        addedLoadDemandDetailIdList.add(loadingDemandSubmissionDetailVO.getId());
                                    }
                                }
                            }
                        }
                    }
                }

                for (Map.Entry<String, List<LoadingDemandSubmissionDetailExitVO>> entry : detailVOMapOfDemandTime.entrySet()) {
                    List<LoadingDemandSubmissionDetailExitVO> loadingDemandSubmissionDetailVOList = entry.getValue();
                    //避免重复添加
                    boolean exist = false;
                    for (LoadingDemandSubmissionDetailExitVO loadingDemandSubmissionDetailVO : loadingDemandSubmissionDetailVOList){
                        if (addedLoadDemandDetailIdList.contains(loadingDemandSubmissionDetailVO.getId())) {
                            exist = true;
                            break;
                        }
                    }
                    if (exist){
                        continue;
                    }
                    CleanDemandDataDetailExitDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailExitDTO();
                    cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                    cleanDemandDataDetailDTO.setCleanDemandDataExitId(submissionExitId);
                    LoadingDemandSubmissionDetailExitVO loadingDemandSubmissionDetailVO = loadingDemandSubmissionDetailVOList.get(0);
                    if (StringUtils.isNotBlank(loadingDemandSubmissionDetailVO.getDemandTime())) {
                        cleanDemandDataDetailDTO.setDemandTime(DateUtils
                                .stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                    }
                    int demandQuantity = 0;
                    for (LoadingDemandSubmissionDetailExitVO detailVO : loadingDemandSubmissionDetailVOList){
                        if (Objects.nonNull(detailVO.getDemandQuantity())) {
                            demandQuantity = demandQuantity + detailVO.getDemandQuantity().intValue();
                        }
                    }
                    cleanDemandDataDetailDTO.setDemandQuantity(demandQuantity);

                    cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                }
            }

        });
    }
    
    /**
     * 获取指定月份的所有天数
     *
     * @param yearMonthStr 指定的年份和月份，格式为"yyyy-MM"
     * @return 该月份的所有日期列表
     */
    public static List<LocalDate> getAllDaysInMonth(String yearMonthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, formatter);
        int daysInMonth = yearMonth.lengthOfMonth();
        List<LocalDate> dates = new ArrayList<>();
        for (int i = 1; i <= daysInMonth; i++) {
            LocalDate date = yearMonth.atDay(i);
            dates.add(date);
        }
        return dates;
    }
    
    public static List<Integer> distributeEvenly(int total, int parts) {
        List<Integer> distribution = new ArrayList<>();
        int baseValue = total / parts;  // 每个部分的基础值
        int remainder = total % parts;  // 处理余数

        for (int i = 0; i < parts; i++) {
            if (i < remainder) {
                // 前面的部分每个加1，以处理余数
                distribution.add(baseValue + 1);
            } else {
                distribution.add(baseValue);
            }
        }

        return distribution;
    }
    
    
    /**
     * 主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
     * @param productCodeList
     * @param productVehicleModelMap
     * @param vehicleModeCodes
     * @param currentMonthFirstDay
     * @param currentMonthCurrentDay
     * @return
     */
	private Map<String, BigDecimal> getWarehouseReleaseRecordDeliveryQtyMap(List<String> productCodeList,
			Map<String, String> productVehicleModelMap, List<String> vehicleModeCodes, String currentMonthFirstDay,
			String currentMonthCurrentDay) {
		Map<String, BigDecimal> releaseMap = new HashMap<>();
        if (!currentMonthCurrentDay.equals(currentMonthFirstDay)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("beginDate", currentMonthFirstDay);
            releaseParams.put("endDate", currentMonthCurrentDay);
            releaseParams.put("productCodes", productCodeList);
            List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecords = warehouseReleaseRecordService.selectMonthVOByParams(releaseParams);
            warehouseReleaseRecords.forEach(item -> {
                String itemCode = item.getItemCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
                item.setVehicleModelCode(vehicleModelCode);
            });
            releaseMap = warehouseReleaseRecords.stream().collect(Collectors
                    .toMap(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode(), item.getItemCode(), item.getYearMonth()),
                            WarehouseReleaseRecordMonthVO::getSumQty, (k1, k2) -> k1));
        }
		return releaseMap;
	}
	
	/**
     * 主机厂，车型，产品编码对应的发货数量(获取仓库发货至中转库的发货数量)
     * @param productCodeList
     * @param productVehicleModelMap
     * @param vehicleModeCodes
     * @param currentMonthFirstDay
     * @param currentMonthCurrentDay
     * @return
     */
	private Map<String, BigDecimal> getWarehouseReleaseToWarehouseDeliveryQtyMap(List<String> productCodeList,
			Map<String, String> productVehicleModelMap, List<String> vehicleModeCodes, String currentMonthFirstDay,
			String currentMonthCurrentDay) {
		Map<String, BigDecimal> releaseMap = new HashMap<>();
        if (!currentMonthCurrentDay.equals(currentMonthFirstDay)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("beginDate", currentMonthFirstDay);
            releaseParams.put("endDate", currentMonthCurrentDay);
            releaseParams.put("productCodes", productCodeList);
            List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService.selectMonthVOByParams(releaseParams);
            warehouseReleaseToWarehouses.forEach(item -> {
                String itemCode = item.getItemCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
                item.setVehicleModelCode(vehicleModelCode);
            });
            releaseMap = warehouseReleaseToWarehouses.stream().collect(Collectors
                    .toMap(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode(), item.getItemCode(), item.getYearMonth()),
                    		WarehouseReleaseToWarehouseMonthVO::getSumQty, (k1, k2) -> k1));
        }
		return releaseMap;
	}
	
	private Map<String, List<ResourceCalendarVO>> getMonthCalendarMap(
			List<LoadingDemandSubmissionDetailExitVO> loadingDemandSubmissionDetailExitVOSDay) {
		List<String> monthList = loadingDemandSubmissionDetailExitVOSDay.stream()
                .map(item -> DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM"))
                .distinct().collect(Collectors.toList());
        Collections.sort(monthList);
        Date firstMonthDate = DateUtils.stringToDate(monthList.get(0),DateUtils.YEAR_MONTH);
        Date lastMonthDate = DateUtils.stringToDate(monthList.get(monthList.size() - 1),DateUtils.YEAR_MONTH);
        Date firstDay = DateUtils.getMonthFirstDay(firstMonthDate);
        Date lastDay = DateUtils.getMonthLastDay(lastMonthDate);
        List<Date> dateList = DateUtils.getIntervalDates(firstDay,lastDay);
        List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
        for (Date d : dateList){
            if (isWeekend(d)){
                continue;
            }
            ResourceCalendarVO resourceCalendarVO = new ResourceCalendarVO();
            resourceCalendarVO.setWorkDay(d);
            resourceCalendarVOS.add(resourceCalendarVO);
        }
        //按照月份分组
        Map<String, List<ResourceCalendarVO>> monthCalendarMap = resourceCalendarVOS.stream()
        		.collect(Collectors.groupingBy(e -> DateUtils.dateToString(e.getWorkDay(), DateUtils.YEAR_MONTH)));
		return monthCalendarMap;
	}
	
	protected boolean isWeekend(Date date){
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }

	@Override
	public List<CleanDemandDataExitVO> selectByPrimaryKeys(List<String> ids) {
		List<CleanDemandDataExitPO> cleanDemandDataPOList = cleanDemandDataExitDao.selectByPrimaryKeys(ids);
        return CleanDemandDataExitConvertor.INSTANCE.po2Vos(cleanDemandDataPOList);
	}

}
