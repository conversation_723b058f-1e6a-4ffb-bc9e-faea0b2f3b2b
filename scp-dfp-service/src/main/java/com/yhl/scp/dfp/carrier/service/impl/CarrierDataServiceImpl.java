package com.yhl.scp.dfp.carrier.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.carrier.dto.CarrierConnectCustomerDTO;
import com.yhl.scp.dfp.carrier.service.CarrierConnectCustomerService;
import com.yhl.scp.dfp.carrier.vo.CarrierConnectCustomerVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.carrier.convertor.CarrierDataConvertor;
import com.yhl.scp.dfp.carrier.domain.entity.CarrierDataDO;
import com.yhl.scp.dfp.carrier.domain.service.CarrierDataDomainService;
import com.yhl.scp.dfp.carrier.dto.CarrierDataDTO;
import com.yhl.scp.dfp.carrier.infrastructure.dao.CarrierDataDao;
import com.yhl.scp.dfp.carrier.infrastructure.po.CarrierDataPO;
import com.yhl.scp.dfp.carrier.service.CarrierDataService;
import com.yhl.scp.dfp.carrier.vo.CarrierDataVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarrierDataServiceImpl extends AbstractService implements CarrierDataService {

    @Resource
    private CarrierDataDao carrierDataDao;

    @Resource
    private CarrierDataDomainService carrierDataDomainService;

    @Resource
    private CarrierConnectCustomerService carrierConnectCustomerService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    public BaseResponse<Void> doCreate(CarrierDataDTO carrierDataDTO) {
        // 0.数据转换
        CarrierDataDO carrierDataDO = CarrierDataConvertor.INSTANCE.dto2Do(carrierDataDTO);
        CarrierDataPO carrierDataPO = CarrierDataConvertor.INSTANCE.dto2Po(carrierDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        carrierDataDomainService.validation(carrierDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(carrierDataPO);
        carrierDataDao.insert(carrierDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CarrierDataDTO carrierDataDTO) {
        // 0.数据转换
        CarrierDataDO carrierDataDO = CarrierDataConvertor.INSTANCE.dto2Do(carrierDataDTO);
        CarrierDataPO carrierDataPO = CarrierDataConvertor.INSTANCE.dto2Po(carrierDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        carrierDataDomainService.validation(carrierDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(carrierDataPO);
        carrierDataDao.update(carrierDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CarrierDataDTO> list) {
        List<CarrierDataPO> newList = CarrierDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        carrierDataDao.insertBatch(newList);
    }

    @Override
    public void doNewCreateBatch(List<CarrierDataDTO> list) {
        List<CarrierDataPO> newList = CarrierDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setId(newList.get(i).getId());
        }
        carrierDataDao.insertBatchWithPrimaryKey(newList);
    }


    @Override
    public void doUpdateBatch(List<CarrierDataDTO> list) {
        List<CarrierDataPO> newList = CarrierDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        carrierDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return carrierDataDao.deleteBatch(idList);
        }
        return carrierDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CarrierDataVO selectByPrimaryKey(String id) {
        CarrierDataPO po = carrierDataDao.selectByPrimaryKey(id);
        return CarrierDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CARRIER_DATA")
    public List<CarrierDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CARRIER_DATA")
    public List<CarrierDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CarrierDataVO> dataList = carrierDataDao.selectByCondition(sortParam, queryCriteriaParam);
        CarrierDataServiceImpl target = SpringBeanUtils.getBean(CarrierDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CarrierDataVO> selectByParams(Map<String, Object> params) {
        List<CarrierDataPO> list = carrierDataDao.selectByParams(params);
        return CarrierDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CarrierDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    /**
     * 全量同步承运商数据
     *
     * @param list 同步的数据集合
     */
    @Override
    public BaseResponse<Void> syncCarrierData(List<CarrierDataDTO> list) {
        //承运商与客户关系新增表
        List<CarrierConnectCustomerDTO> insertWithCustomerList = ListUtil.list(false);
        //承运商新增表
        List<CarrierDataDTO> insertCarrierList = ListUtil.list(false);
        //承运商更新表
        List<CarrierDataDTO> updateCarrierList = ListUtil.list(false);

        //获取该组织下的数据
        String ebsOrgId = list.get(0).getEbsOrgId();
        //查询bpim中已有的数据
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("ebsOrgId", ebsOrgId);
        map.put("sourceType", ApiSourceEnum.GRP.getCode());
        List<CarrierDataVO> carrierDataVOS = this.selectByParams(map);
        //判断bpim是否存在数据
        if (carrierDataVOS.isEmpty()) {
            //插入承运商数据
            this.doNewCreateBatch(list);
            list.forEach(x -> {
                List<CarrierConnectCustomerDTO> customerInfoDtoS = x.getCustomerInfoDtos();
                if (!customerInfoDtoS.isEmpty() && ObjectUtil.isNotNull(customerInfoDtoS.get(0))) {
                    customerInfoDtoS.forEach(y -> y.setCurrentCarrierId(x.getId()));
                    insertWithCustomerList.addAll(customerInfoDtoS);
                }
            });
            //插入承运商与客户数据
            carrierConnectCustomerService.doCreateBatch(insertWithCustomerList);
        } else {
            //根据查询整个旧数据转化为map，以供应商ID为key
            Map<Integer, CarrierDataVO> collect = carrierDataVOS.stream().collect(Collectors.toMap(
                    CarrierDataVO::getCarrierId,
                    x -> x
            ));
            //获取整个旧数据的供应商id集合
            Set<Integer> existingIds = collect.keySet();
            //获取同步的数据供应商id集合
            List<Integer> carrierIds = list.stream().map(CarrierDataDTO::getCarrierId).collect(Collectors.toList());
            //获取存在的旧数据
            List<CarrierDataVO> oldCarrierVOList = carrierIds.stream().filter(collect::containsKey)
                    .map(collect::get)
                    .collect(Collectors.toList());
            //旧数据的id集合
            List<String> oldIds = oldCarrierVOList.stream().map(CarrierDataVO::getId).collect(Collectors.toList());
            if (!oldIds.isEmpty()) {
                Map<String, Object> carrierCustomerMap = MapUtil.newHashMap();
                carrierCustomerMap.put("currentCarrierIds", oldIds);
                List<CarrierConnectCustomerVO> carrierConnectCustomerVOS = carrierConnectCustomerService.selectByParams(carrierCustomerMap);
                //删除 同步数据中对应对应的旧客户数据
                List<String> ids = carrierConnectCustomerVOS.stream().map(CarrierConnectCustomerVO::getId).collect(Collectors.toList());
                carrierConnectCustomerService.doDelete(ids);
            }
            //未同步的旧数据，默认失效
            carrierDataVOS.removeAll(oldCarrierVOList);
            if (!carrierDataVOS.isEmpty()) {
                List<CarrierDataDTO> noStatusList = carrierDataVOS.stream()
                        .filter(x -> YesOrNoEnum.YES.getCode().equals(x.getEnabled()))
                        .map(x -> {
                            x.setEnabled(YesOrNoEnum.NO.getCode());
                            CarrierDataDTO carrierDataDTO = new CarrierDataDTO();
                            BeanUtils.copyProperties(x, carrierDataDTO);
                            return carrierDataDTO;
                        })
                        .collect(Collectors.toList());
                updateCarrierList.addAll(noStatusList);
            }
            list.forEach(x -> {
                if (existingIds.contains(x.getCarrierId())) {
                    CarrierDataVO carrierDataVO = collect.get(x.getCarrierId());
                    x.setId(carrierDataVO.getId());
                    x.setCreateTime(carrierDataVO.getCreateTime());
                    x.setCreator(carrierDataVO.getCreator());
                    x.setEnabled(YesOrNoEnum.YES.getCode());
                    x.setVersionValue(1);
                    updateCarrierList.add(x);
                    //获取同步的客户数据
                    List<CarrierConnectCustomerDTO> customerInfoDtoS = x.getCustomerInfoDtos();
                    if (!customerInfoDtoS.isEmpty() && ObjectUtil.isNotNull(customerInfoDtoS.get(0))) {
                        customerInfoDtoS.forEach(y -> y.setCurrentCarrierId(x.getId()));
                        insertWithCustomerList.addAll(customerInfoDtoS);
                    }
                } else {
                    //新的承运商数据
                    insertCarrierList.add(x);
                }
            });
            //更新承运商
            if (!updateCarrierList.isEmpty()) {
                this.doUpdateBatch(updateCarrierList);
            }
            //新增承运商
            if (!insertCarrierList.isEmpty()) {
                this.doNewCreateBatch(insertCarrierList);
                insertCarrierList.forEach(x -> {
                    List<CarrierConnectCustomerDTO> customerInfoDtoS = x.getCustomerInfoDtos();
                    if (!customerInfoDtoS.isEmpty()) {
                        customerInfoDtoS.forEach(y -> y.setCurrentCarrierId(x.getId()));
                        insertWithCustomerList.addAll(customerInfoDtoS);
                    }
                });
            }
            //新增客户数据
            if (!insertWithCustomerList.isEmpty()) {
                carrierConnectCustomerService.doCreateBatch(insertWithCustomerList);
            }
        }
        return BaseResponse.success("数据插入成功");
    }

    /**
     * 同步承运商
     * @param tenantCode 租户名称
     * @param scenario 数据库名称
     */
    @Override
    public BaseResponse<Void> syncData(String tenantCode, String scenario) {
        try {
            log.info("开始同步承运商");
            //获取MES仓库收发货记录日志数据
            HashMap<String, Object> map = MapUtil.newHashMap();
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantCode();
                scenario = SystemHolder.getScenario();
            }
            List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectAllStockPoint(scenario);
            //获取采购组织和生产组织
            for (NewStockPointVO vo : newStockPointVOS) {
                String interfaceFlag = vo.getInterfaceFlag();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(interfaceFlag)) {
                    String[] split = interfaceFlag.split(",");
                    boolean flag = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.CARRIER.getCode().equals(x));
                    if (flag) {
                        map.put("orgId",Integer.valueOf(vo.getOrganizeId()));
                        map.put("scenario",scenario);
                        newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.GRP.getCode(),
                                ApiCategoryEnum.CARRIER.getCode(), map);
                    }
                }
            }
            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步承运商数据报错,{}", e.getMessage());
            throw new BusinessException("同步承运商数据报错", e.getMessage());
        }
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<CarrierDataVO> invocation(List<CarrierDataVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
