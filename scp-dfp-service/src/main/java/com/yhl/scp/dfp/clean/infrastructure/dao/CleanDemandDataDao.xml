<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO">
        <!--@Table fdp_clean_demand_data-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="demand_type" jdbcType="VARCHAR" property="demandType"/>
        <result column="version_status" jdbcType="VARCHAR" property="versionStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.clean.vo.CleanDemandDataVO">
        <!-- TODO -->
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
    </resultMap>
    
    <resultMap id="VOProductInventoryMap" extends="BaseResultMap" type="com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="demand_type" jdbcType="VARCHAR" property="demandType"/>
        <result column="market_type" jdbcType="VARCHAR" property="marketType"/>
        <result column="customer_abbreviation" jdbcType="VARCHAR" property="customerAbbreviation"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id
        ,version_id,demand_category,oem_code,vehicle_model_code,product_code,part_name,demand_type,version_status,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,oem_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIds != null and params.versionIds.size() > 0">
                and version_id in
                <foreach collection="params.versionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.partName != null and params.partName != ''">
                and part_name = #{params.partName,jdbcType=VARCHAR}
            </if>
            <if test="params.demandType != null and params.demandType != ''">
                and demand_type = #{params.demandType,jdbcType=VARCHAR}
            </if>
            <if test="params.versionStatus != null and params.versionStatus != ''">
                and version_status = #{params.versionStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="Base_Where_Condition_Product_Inventory">
        <where>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.partName != null and params.partName != ''">
                and part_name = #{params.partName,jdbcType=VARCHAR}
            </if>
            <if test="params.demandType != null and params.demandType != ''">
                and demand_type = #{params.demandType,jdbcType=VARCHAR}
            </if>
            <if test="params.marketType != null and params.marketType != ''">
                and market_type = #{params.marketType,jdbcType=VARCHAR}
            </if>
            <if test="params.customerAbbreviation != null and params.customerAbbreviation != ''">
                and customer_abbreviation = #{params.customerAbbreviation,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_clean_demand_data
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_clean_demand_data
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_clean_demand_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_clean_demand_data
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_clean_demand_data
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectOemDropdown" parameterType="java.lang.String" resultType="com.yhl.platform.common.LabelValue">
        select distinct
            oem_code as `value`,
            oem_name as `label`
        from v_fdp_clean_demand_data
        where version_id = #{versionId,jdbcType=VARCHAR}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_clean_demand_data(
        id,
        version_id,
        demand_category,
        oem_code,
        vehicle_model_code,
        product_code,
        part_name,
        demand_type,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{demandCategory,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{demandType,jdbcType=VARCHAR},
        #{versionStatus,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO">
        insert into fdp_clean_demand_data(id,
                                          version_id,
                                          demand_category,
                                          oem_code,
                                          vehicle_model_code,
                                          product_code,
                                          part_name,
                                          demand_type,
                                          version_status,
                                          remark,
                                          enabled,
                                          creator,
                                          create_time,
                                          modifier,
                                          modify_time,
                                          version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionId,jdbcType=VARCHAR},
                #{demandCategory,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{partName,jdbcType=VARCHAR},
                #{demandType,jdbcType=VARCHAR}
                #{versionStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_clean_demand_data(
        id,
        version_id,
        demand_category,
        oem_code,
        vehicle_model_code,
        product_code,
        part_name,
        demand_type,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.partName,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.versionStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_clean_demand_data(
        id,
        version_id,
        demand_category,
        oem_code,
        vehicle_model_code,
        product_code,
        part_name,
        demand_type,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.partName,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.versionStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO">
        update fdp_clean_demand_data
        set version_id         = #{versionId,jdbcType=VARCHAR},
            demand_category    = #{demandCategory,jdbcType=VARCHAR},
            oem_code           = #{oemCode,jdbcType=VARCHAR},
            vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
            product_code       = #{productCode,jdbcType=VARCHAR},
            part_name          = #{partName,jdbcType=VARCHAR},
            demand_type        = #{demandType,jdbcType=VARCHAR},
            version_status     = #{versionStatus,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_clean_demand_data
        set version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO">
        update fdp_clean_demand_data
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.demandType != null and item.demandType != ''">
                demand_type = #{item.demandType,jdbcType=VARCHAR},
            </if>
            <if test="item.versionStatus != null and item.versionStatus != ''">
                version_status = #{item.versionStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_clean_demand_data set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_clean_demand_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_clean_demand_data set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_clean_demand_data
            <set>
                <if test="item.versionId != null and item.versionId != ''">
                    version_id = #{item.versionId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandCategory != null and item.demandCategory != ''">
                    demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.partName != null and item.partName != ''">
                    part_name = #{item.partName,jdbcType=VARCHAR},
                </if>
                <if test="item.demandType != null and item.demandType != ''">
                    demand_type = #{item.demandType,jdbcType=VARCHAR},
                </if>
                <if test="item.versionStatus != null and item.versionStatus != ''">
                    version_status = #{item.versionStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_clean_demand_data set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_clean_demand_data
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_clean_demand_data where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_clean_demand_data where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <delete id="deleteByVersionId">
        delete from fdp_clean_demand_data where version_id = #{versionId,jdbcType=VARCHAR}
    </delete>
    
    <select id="queryProductInventoryReportPage" resultMap="VOProductInventoryMap">
        <!-- TODO -->
        select
        	id,
        	version_id,
        	demand_category,
        	oem_code,
        	vehicle_model_code,
        	product_code,
        	part_name,
        	demand_type,
        	market_type,
        	customer_abbreviation
        from v_union_all_fdp_clean_demand_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>

    <select id="selectProductInventoryReportListByParams" resultMap="VOProductInventoryMap">
        select
        id,
        version_id,
        demand_category,
        oem_code,
        vehicle_model_code,
        product_code,
        part_name,
        demand_type,
        market_type,
        customer_abbreviation
        from v_union_all_fdp_clean_demand_data
        <include refid="Base_Where_Condition_Product_Inventory"/>
    </select>
</mapper>
