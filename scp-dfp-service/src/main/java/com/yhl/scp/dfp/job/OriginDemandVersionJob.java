package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName OriginDemandVersionJob
 * @Description TODO
 * @Date 2024-09-23 00:00:46
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class OriginDemandVersionJob {

    @Resource
    private SpringBeanUtils springBeanUtils;
    @XxlJob("originDemandVersionJob")
    private ReturnT<String> originDemandVersionJob() {
        IpsNewFeign ipsNewFeign = springBeanUtils.getBean(IpsNewFeign.class);
        OriginDemandVersionService originDemandVersionService = springBeanUtils.getBean(OriginDemandVersionService.class);
        LoadingDemandSubmissionService loadingDemandSubmissionService = springBeanUtils.getBean(LoadingDemandSubmissionService.class);
        OemService oemService = springBeanUtils.getBean(OemService.class);
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的原始版本生成", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

            String versionId = originDemandVersionService.selectLatestVersionId();
            if(Objects.isNull(versionId)){
                log.error("原始需求版本不存在");
                continue;
            }
            OriginDemandVersionVO originDemandVersionVO = originDemandVersionService.selectByPrimaryKey(versionId);

            if(!Objects.equals(DateUtils.dateToString(originDemandVersionVO.getCreateTime(), DateUtils.COMMON_DATE_STR3),
                    DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR3))){
                log.error("无今日原始需求版本");
                continue;
            }
            List<OemVO> oemVOS = oemService.selectProductEdiFlag(YesOrNoEnum.YES.getCode(), YesOrNoEnum.YES.getCode());

            loadingDemandSubmissionService.apiSubmission(oemVOS,versionId,scenario);

            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的原始版本生成结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
