package com.yhl.scp.dfp.job;/**
 * author:
 * created:
 */

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * description:仓库收发货定时任务
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/15
 */
@Component
@Slf4j
public class WarehouseReleaseJob {
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @XxlJob("warehouseReleaseJob")
    public ReturnT<String> WarehouseReleaseJob() {
        XxlJobHelper.log("开始运行仓库收发货定时任务");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    warehouseReleaseRecordService.syncData(null, null, scenario.getTenantId(), scenario.getDataBaseName());
                }
            } catch (Exception e) {
                XxlJobHelper.log("仓库收发货定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("仓库收发货定时任务结束");
        return ReturnT.SUCCESS;
    }

    @XxlJob("shippingRecordJob")
    public ReturnT<String> shippingRecordJob() {
        XxlJobHelper.log("开始运行FYSL发货计划定时任务。");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    //获取对应组织参数
                    List<String> orgIds = new ArrayList<>();
                    List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectAllStockPoint(scenario.getDataBaseName());
                    for (NewStockPointVO stockPoint : newStockPointVOS) {
                        String interfaceFlag = stockPoint.getInterfaceFlag();
                        if (StringUtils.isNotEmpty(interfaceFlag)) {
                            String[] split = interfaceFlag.split(",");
                            boolean flag = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.SHIPPING_RECORD.getCode().equals(x));
                            if (flag) {
                                orgIds.add(stockPoint.getOrganizeId());
                            }
                        }
                    }
                    for (String id : orgIds) {
                        warehouseReleaseRecordService.syncFYSLData(scenario.getTenantId(), id, scenario.getDataBaseName());
                    }
                }
            } catch (Exception e) {
                XxlJobHelper.log("FYSL发货计划定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("FYSL发货计划定时任务结束。");
        return ReturnT.SUCCESS;
    }

    @XxlJob("enRouteJob")
    public ReturnT<String> enRouteJob() {
        XxlJobHelper.log("开始运行FYDS在途数据定时任务");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    warehouseReleaseRecordService.syncEnRouteData(scenario.getTenantId(), scenario.getDataBaseName());
                }
            } catch (Exception e) {
                XxlJobHelper.log("FYDS在途数据定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("FYDS在途数据定时任务结束");
        return ReturnT.SUCCESS;
    }

    @XxlJob("fyeWarehouseJob")
    public ReturnT<String> fyeWarehouseJob() {
        XxlJobHelper.log("开始运行FYE仓库收发货定时任务");
        // 获取 XXL-JOB 平台配置的参数（字符串）
        String jobParam = XxlJobHelper.getJobParam();
        // 可选：打印日志
        log.info("接收到的任务参数: {}" , jobParam);
        XxlJobHelper.log("接收到的任务参数: " + jobParam);
        // 判断是否为空
        if (jobParam != null && !jobParam.trim().isEmpty()) {
            // 例如：参数是 JSON 格式，可以解析
            Map<String,String> map = JSONObject.parseObject(jobParam, Map.class);
            XxlJobHelper.log("解析参数成功，开始执行业务逻辑...");
            String scenario = map.get("scenario");
            String plateCode = map.get("plateCode");
            String tenantCode = map.get("tenantCode");
            if (StringUtils.isNotBlank(scenario) && StringUtils.isNotBlank(plateCode) && StringUtils.isNotBlank(tenantCode)){
                log.info("开始同步FYE仓库收发货数据,{},{},{}",scenario,plateCode,tenantCode);
                warehouseReleaseRecordService.syncFYEWarehouseData(scenario,plateCode,tenantCode,null,null);
            }
        } else {
            XxlJobHelper.log("未传入任务参数，使用默认逻辑...");
        }
        XxlJobHelper.log("结束运行FYE仓库收发货定时任务");
        return ReturnT.SUCCESS;
    }
}
