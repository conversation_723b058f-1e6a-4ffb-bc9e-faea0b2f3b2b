package com.yhl.scp.dfp.deliverydockingorder.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpEdiDeliveryDetail;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.carrier.service.CarrierDataService;
import com.yhl.scp.dfp.carrier.vo.CarrierDataVO;
import com.yhl.scp.dfp.common.enums.StatusEnum;
import com.yhl.scp.biz.common.excel.CustomCellWriteHandler;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.deliverydockingorder.convertor.DeliveryDockingOrderConvertor;
import com.yhl.scp.dfp.deliverydockingorder.domain.entity.DeliveryDockingOrderDO;
import com.yhl.scp.dfp.deliverydockingorder.domain.service.DeliveryDockingOrderDomainService;
import com.yhl.scp.dfp.deliverydockingorder.dto.*;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDao;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderDetailService;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderService;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryReturnVO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.newProduct.enums.ProductTallyOrderModeEnum;
import com.yhl.scp.dfp.oem.enums.OemTallyOrderModeEnum;
import com.yhl.scp.dfp.oem.service.OemAddressInventoryLogService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemAddressInventoryLogVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import com.yhl.scp.dfp.transport.service.TransportResourceService;
import com.yhl.scp.dfp.transport.service.TransportRoutingDetailService;
import com.yhl.scp.dfp.transport.service.TransportRoutingService;
import com.yhl.scp.dfp.transport.vo.TransportResourceVO;
import com.yhl.scp.dfp.transport.vo.TransportRoutingDetailVO;
import com.yhl.scp.dfp.transport.vo.TransportRoutingVO;
import com.yhl.scp.dfp.utils.LabelValueFour;
import com.yhl.scp.dfp.utils.LabelValueThree;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.customer.vo.CustomerVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DeliveryDockingOrderServiceImpl</code>
 * <p>
 * 发货对接单管理应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 10:34:52
 */
@Slf4j
@Service
public class DeliveryDockingOrderServiceImpl extends AbstractService implements DeliveryDockingOrderService {

    @Resource
    private DeliveryDockingOrderDao deliveryDockingOrderDao;

    @Resource
    private DeliveryDockingOrderDomainService deliveryDockingOrderDomainService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private OriginDemandVersionService originDemandVersionService;

    @Resource
    private TransportRoutingService transportRoutingService;

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;

    @Resource
    private DeliveryDockingOrderDetailService deliveryDockingOrderDetailService;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    private OemService oemService;

    @Resource
    private OemAddressInventoryLogService oemAddressInventoryLogService;

    @Resource
    private TransportRoutingDetailService transportRoutingDetailService;

    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private TransportResourceService transportResourceService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private CarrierDataService carrierDataService;

    @Resource
    private FdpOriginDemandInterfaceLogService fdpOriginDemandInterfaceLogService;

    @Resource
    private IpsFeign ipsFeign;

    private static final String PREFIX = "DN";

    private static final String ENABLED = "enabled";

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    @Override
    public BaseResponse<Void> doCreate(DeliveryDockingOrderDTO deliveryDockingOrderDTO) {
        if (deliveryDockingOrderDTO.getDeliveryDockingNumber() == null) {
            throw new BusinessException("新增前请先传递发货对接单号");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("deliveryDockingNumber", deliveryDockingOrderDTO.getDeliveryDockingNumber());
        List<DeliveryDockingOrderPO> deliveryDockingOrderPOS = deliveryDockingOrderDao.selectByParams(params);
        if (CollectionUtils.isNotEmpty(deliveryDockingOrderPOS)) {
            deliveryDockingOrderDTO.setId(deliveryDockingOrderPOS.get(0).getId());
            this.updateAndSync(deliveryDockingOrderDTO);
        } else {
            // 0.数据转换
            DeliveryDockingOrderDO deliveryDockingOrderDO = DeliveryDockingOrderConvertor.INSTANCE.dto2Do(deliveryDockingOrderDTO);
            DeliveryDockingOrderPO deliveryDockingOrderPO = DeliveryDockingOrderConvertor.INSTANCE.dto2Po(deliveryDockingOrderDTO);
            // 1.数据校验
            // TODO 完善validation()方法
            deliveryDockingOrderDomainService.validation(deliveryDockingOrderDO);
            // 2.数据持久化
            BasePOUtils.insertFiller(deliveryDockingOrderPO);
            deliveryDockingOrderDao.insert(deliveryDockingOrderPO);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryDockingOrderDTO deliveryDockingOrderDTO) {
        if (deliveryDockingOrderDTO.getDeliveryDockingNumber() == null) {
            throw new BusinessException("修改前请先传递发货对接单号");
        }
        // 0.数据转换
        DeliveryDockingOrderDO deliveryDockingOrderDO = DeliveryDockingOrderConvertor.INSTANCE.dto2Do(deliveryDockingOrderDTO);
        DeliveryDockingOrderPO deliveryDockingOrderPO = DeliveryDockingOrderConvertor.INSTANCE.dto2Po(deliveryDockingOrderDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryDockingOrderDomainService.validation(deliveryDockingOrderDO);
        deliveryDockingOrderDomainService.validationForUpdate(deliveryDockingOrderDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryDockingOrderPO);
        deliveryDockingOrderDao.update(deliveryDockingOrderPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryDockingOrderDTO> list) {
        List<DeliveryDockingOrderPO> newList = DeliveryDockingOrderConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryDockingOrderDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryDockingOrderDTO> list) {
        List<DeliveryDockingOrderPO> newList = DeliveryDockingOrderConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryDockingOrderDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        for (String deliveryDockingOrderId : idList) {
        	DeliveryDockingOrderPO deliveryDockingOrderPO = deliveryDockingOrderDao.selectByPrimaryKey(deliveryDockingOrderId);
        	if (!StatusEnum.OPEN.getCode().equals(deliveryDockingOrderPO.getStatus())) {
                throw new BusinessException("非打开状态不能删除");
            }
        	List<DeliveryDockingOrderDetailVO> details = deliveryDockingOrderDetailService.selectByParams(ImmutableMap.of(
        			"deliveryDockingNumber", deliveryDockingOrderPO.getDeliveryDockingNumber()));
        	if(CollectionUtils.isNotEmpty(details)){
        		List<String> detailIds = details.stream().map(DeliveryDockingOrderDetailVO::getId).collect(Collectors.toList());
        		deliveryDockingOrderDetailService.doDeleteBatch(detailIds);
        	}
        	deliveryDockingOrderDao.deleteByPrimaryKey(deliveryDockingOrderId);
		}
        return idList.size();
    }

    @Override
    public DeliveryDockingOrderVO selectByPrimaryKey(String id) {
        DeliveryDockingOrderPO po = deliveryDockingOrderDao.selectByPrimaryKey(id);
        return DeliveryDockingOrderConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_DOCKING_ORDER")
    public List<DeliveryDockingOrderVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_DOCKING_ORDER")
    public List<DeliveryDockingOrderVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        if (StringUtils.isBlank(sortParam)) {
            sortParam = "modify_time desc";
        }
        List<DeliveryDockingOrderVO> dataList = deliveryDockingOrderDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryDockingOrderServiceImpl target = SpringBeanUtils.getBean(DeliveryDockingOrderServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryDockingOrderVO> selectByParams(Map<String, Object> params) {
        List<DeliveryDockingOrderPO> list = deliveryDockingOrderDao.selectByParams(params);
        return DeliveryDockingOrderConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryDockingOrderVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String generateDeliveryDockingNumber() {
        String dateTimeStr = DATE_FORMAT.format(new Date());
        // 生成3位随机数字
        String randomNum = String.format("%03d", ThreadLocalRandom.current().nextInt(0, 1000));
        // 拼接成最终的单号
        return PREFIX + dateTimeStr + randomNum;
    }

    @Override
    public List<LabelValue<String>> getNewProductStockPoint(List<String> productCodeList) {
        List<NewProductStockPointVO> newProductStockPointVOS =
                newMdsFeign.selectByProductCode(SystemHolder.getScenario(), productCodeList);
        List<LabelValue<String>> list = newProductStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getProductName(), x.getProductCode()))
                .collect(Collectors.toList());
        return removeDuplicates(list);
    }

    @Override
    public List<String> getOriginDemandVersionCode() {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<OriginDemandVersionVO> originDemandVersionVOS = originDemandVersionService.selectByParams(params);
        return originDemandVersionVOS.stream()
                .map(OriginDemandVersionVO::getVersionCode)
                .distinct()
                .collect(Collectors.toList());

    }

    @Override
    public List<LabelValue<String>> getRoutingCode() {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<TransportRoutingVO> transportRoutingVOS = transportRoutingService.selectByParams(params);
        return transportRoutingVOS.stream()
                .map(x -> new LabelValue<>(x.getRoutingName(), x.getRoutingCode()))
                .collect(Collectors.toList());

    }

    @Override
    public List<String> getTransportMode() {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<TransportRoutingVO> transportRoutingVOS = transportRoutingService.selectByParams(params);
        return transportRoutingVOS.stream()
                .map(TransportRoutingVO::getTransportMode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getProductCodeByOemCodeAndVresionCode(String oemCode, String versionCode) {
        Map<String, Object> queryParamA = new HashMap<>(2);
        queryParamA.put(ENABLED, YesOrNoEnum.YES.getCode());
        queryParamA.put("versionCode", versionCode);
        queryParamA.put("oemCode", oemCode);
        queryParamA.put("versionStatus", "PUBLISHED");
        List<DeliveryPlanVersionVO> deliveryPlanVersionVOS = deliveryPlanVersionService.selectByParams(queryParamA);
        if (CollectionUtils.isEmpty(deliveryPlanVersionVOS)) {
            return Collections.emptyList();
        }
        DeliveryPlanVersionVO deliveryPlanVersionVO = deliveryPlanVersionVOS.get(0);
        Map<String, Object> queryParamB = new HashMap<>(2);
        queryParamB.put(ENABLED, YesOrNoEnum.YES.getCode());
        queryParamB.put("versionId", deliveryPlanVersionVO.getId());
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanService.selectByParams(queryParamB);
        return deliveryPlanVOS.stream()
                .map(DeliveryPlanVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> getSubInventory(String oemCode) {
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectOemStockPointByOemCodes(Collections.singletonList(oemCode));
        if (CollectionUtils.isEmpty(oemStockPointMapVOS)) {
            return Collections.emptyList();
        }
        List<String> stockPointCodes = oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode).distinct().collect(Collectors.toList());
        Map<String, Object> queryParam = new HashMap<>(2);
        queryParam.put("stockPointCodes", stockPointCodes);
        List<SubInventoryCargoLocationVO> subInventoryCargoLocationInformationVOS = mpsFeign.queryByParams1(SystemHolder.getScenario(), queryParam);
        List<LabelValue<String>> list = subInventoryCargoLocationInformationVOS.stream()
                .map(x -> new LabelValue<>(x.getStashName(), x.getStashCode()))
                .collect(Collectors.toList());
        return removeDuplicates(list);

    }

    @Override
    public List<LabelValue<String>> getLocation(String stashCode) {
        Map<String, Object> queryParam = new HashMap<>(2);
        queryParam.put("stashCode", stashCode);
        List<SubInventoryCargoLocationVO> subInventoryCargoLocationInformationVOS = mpsFeign.queryByParams1(SystemHolder.getScenario(), queryParam);
        List<LabelValue<String>> list = subInventoryCargoLocationInformationVOS.stream()
                .filter(vo -> vo.getFreightSpaceName() != null && !vo.getFreightSpaceName().isEmpty() &&
                        vo.getFreightSpaceCode() != null && !vo.getFreightSpaceCode().isEmpty())
                .map(x -> new LabelValue<>(x.getFreightSpaceName(), x.getFreightSpaceCode()))
                .collect(Collectors.toList());
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                LabelValue::getLabel, // 使用货位名称作为键
                                lv -> lv,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    @Override
    public List<LabelValue<String>> getBusinessAndMarketType(String oemCode) {
        Map<String, Object> queryParam = new HashMap<>(2);
        queryParam.put(ENABLED, YesOrNoEnum.YES.getCode());
        queryParam.put("oemCode", oemCode);
        List<OemVO> oemVOS = oemService.selectByParams(queryParam);
        return oemVOS.stream()
                .map(x -> new LabelValue<>(x.getBusinessType(), x.getMarketType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DeliveryDockingOrderDetailVO> selectDockingOrderDetail(String deliveryDockingNumber) {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        params.put("deliveryDockingNumber", deliveryDockingNumber);
        List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS = deliveryDockingOrderDetailService.selectVOByParams(params);
        if (CollectionUtils.isEmpty(deliveryDockingOrderDetailVOS)) {
            return Collections.emptyList();
        }
        return deliveryDockingOrderDetailVOS;
    }

    @Override
    public List<String> getDeliveryPlanVersionCode() {
        Map<String, Object> params = new HashMap<>();
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<DeliveryPlanVersionVO> deliveryPlanVersionVOS = deliveryPlanVersionService.selectByParams(params);
        if (CollectionUtils.isEmpty(deliveryPlanVersionVOS)) {
            return Collections.emptyList();
        }
        return deliveryPlanVersionVOS.stream()
                .map(DeliveryPlanVersionVO::getVersionCode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getProductCodeByOemCodeAndVersionCodeFromLoadDemand(String oemCode, String versionCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        params.put("versionCode", versionCode);
        params.put("oemCode", oemCode);
        List<OriginDemandVersionVO> originDemandVersionVOS = originDemandVersionService.selectByParams(params);
        if (CollectionUtils.isEmpty(originDemandVersionVOS)) {
            return Collections.emptyList();
        }
        OriginDemandVersionVO originDemandVersionVO = originDemandVersionVOS.get(0);
        Map<String, Object> queryParamB = new HashMap<>(2);
        queryParamB.put(ENABLED, YesOrNoEnum.YES.getCode());
        queryParamB.put("versionId", originDemandVersionVO.getId());
        List<LoadingDemandSubmissionVO> deliveryPlanVOS = loadingDemandSubmissionService.selectByParams(queryParamB);
        return deliveryPlanVOS.stream()
                .map(LoadingDemandSubmissionVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());

    }

    @Override
    public BaseResponse<Void> sync() {
        return BaseResponse.success("Test同步成功");
    }

    @Override
    public BaseResponse<Void> doChange(String status) {
        return BaseResponse.success("Test变更成功");
    }

    @Override
    public List<String> dropdown() {
        List<DeliveryDockingOrderVO> dockingOrderVOList = this.selectAll();
        if (CollectionUtils.isEmpty(dockingOrderVOList)) {
            return Collections.emptyList();
        }
        return dockingOrderVOList.stream()
                .map(DeliveryDockingOrderVO::getDeliveryDockingNumber)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getStatusByDeliveryDockingNumber(String deliveryDockingNumber) {
        Map<String, Object> queryParam = new HashMap<>(2);
        queryParam.put("deliveryDockingNumber", deliveryDockingNumber);
        List<DeliveryDockingOrderVO> dockingOrderVOList = this.selectByParams(queryParam);
        if (CollectionUtils.isEmpty(dockingOrderVOList)) {
            return Collections.emptyList();
        }
        return dockingOrderVOList.stream()
                .map(DeliveryDockingOrderVO::getStatus)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> getCustomerAddress(String oemCode) {
        Map<String, Object> paramsB = new HashMap<>(2);
        paramsB.put("oemCode", oemCode);
        List<OemVO> oemVOS = oemService.selectByParams(paramsB);
        if (CollectionUtils.isEmpty(oemVOS)) {
            return Collections.emptyList();
        }
        List<String> customerCodes = oemVOS.stream().map(OemVO::getCustomerCode).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        params.put("customerCodes", customerCodes);
        List<OemAddressInventoryLogVO> oemAddressInventoryLogVOS = oemAddressInventoryLogService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(oemAddressInventoryLogVOS)) {
            List<LabelValue<String>> list = oemAddressInventoryLogVOS.stream()
                    .filter(x -> x.getCustomerAddress() != null)
                    .map(x -> new LabelValue<>(x.getCustomerAddress(), x.getCustomerAddress()))
                    .collect(Collectors.toList());
            return removeDuplicates(list);
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValue<String>> getLineRoutingCode() {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<TransportRoutingDetailVO> transportRoutingDetailVOS = transportRoutingDetailService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(transportRoutingDetailVOS)) {
            List<LabelValue<String>> list = transportRoutingDetailVOS.stream()
                    .map(x -> new LabelValue<>(x.getRoutingDetailName(), x.getRoutingDetailCode()))
                    .collect(Collectors.toList());
            return removeDuplicates(list);
        }
        return Collections.emptyList();
    }

    @Override
    public String getTransportModelByLineCode(String routingDetailCode) {
        TransportRoutingDetailVO transportRoutingDetailVO = transportRoutingDetailService.selectTransportModelByLineCode(routingDetailCode);
        if (transportRoutingDetailVO != null) {
            return transportRoutingDetailVO.getTransportMode();
        }
        return "";
    }

    @Override
    public String getBoxTypeByProductCode(String productCode) {
    	List<ProductBoxRelationVO> boxTypeListByPorductCode = getBoxTypeListByProductCode(productCode);
    	return boxTypeListByPorductCode.get(0).getBoxType();
    }

    @Override
    public String getBoxTypeByProductCodeId(String productStockPointId) {
        List<ProductBoxRelationVO> productBoxRelationVOList =
                newMdsFeign.selectProductBoxRelationByProductStockPointId(SystemHolder.getScenario(),
                        Collections.singletonList(productStockPointId));
        if (CollectionUtils.isNotEmpty(productBoxRelationVOList)) {
            return productBoxRelationVOList.get(0).getBoxType();
        }
        return "";
    }

    @Override
    public List<DeliveryReturnVO> getProductCodeByOemCodeEdi(String oemCode, String selectTime, String currentDayFlag) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("oemCode", oemCode);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = oemService.selectByParams(params);
        if (CollectionUtils.isEmpty(oemVOS)) {
            throw new BusinessException("未找到对应的主机厂");
        }
        String scenario = SystemHolder.getScenario();
        //GRP查装车需求提报，不是GRP/未维护，走发货计划
        String tallyOrderMode = oemVOS.get(0).getTallyOrderMode();
        List<DeliveryReturnVO> deliveryReturnVOList = new ArrayList<>();
        if (StringUtils.isEmpty(tallyOrderMode) || OemTallyOrderModeEnum.MES.getCode().equals(tallyOrderMode)) {
            if (StringUtils.isNotEmpty(selectTime)) {
                params.put("demandTimeStrs", Arrays.asList(selectTime.substring(0, 10)));
            }
            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = deliveryPlanPublishedService.selectByParams(params);
            if (CollectionUtils.isEmpty(deliveryPlanPublishedVOS)) {
                throw new BusinessException("此主机厂 " + oemCode + " 在时间 " + selectTime + " 下无已发货计划");
            }
            List<String> productCodeList = deliveryPlanPublishedVOS.stream()
                    .map(DeliveryPlanPublishedVO::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(scenario, productCodeList);
            Map<String, NewProductStockPointVO> productCodeToNameMap = newProductStockPointVOS.stream()
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
            Map<String, Integer> productCodeToQuantityMap = deliveryPlanPublishedVOS.stream()
                    .collect(Collectors.groupingBy(
                            e-> String.join("&", e.getProductCode(), e.getDemandCategory()),
                            Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)
                    ));
            for (Entry<String, Integer> publishEntry : productCodeToQuantityMap.entrySet()) {
            	String[] splitKey = publishEntry.getKey().split("&");
            	DeliveryReturnVO deliveryReturnVO = new DeliveryReturnVO();
                deliveryReturnVO.setProductCode(splitKey[0]);
                deliveryReturnVO.setDemandCategory(splitKey[1]);
                NewProductStockPointVO stockPointVO = productCodeToNameMap.get(splitKey[0]);
                if (stockPointVO != null) {
                    deliveryReturnVO.setProductName(stockPointVO.getProductName());
                }
                deliveryReturnVO.setDeliveryQuantity(publishEntry.getValue() + "");
                deliveryReturnVOList.add(deliveryReturnVO);
			}
        } else if (OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
            String latestVersionId = originDemandVersionService.selectLatestVersionId();
            log.info("最新原始需求版本id:" + latestVersionId);
            Map<String, Object> param = new HashMap<>(2);
            param.put("oemCode", oemCode);
            param.put("versionId", latestVersionId);
            param.put("demandTime", selectTime);
            if (StringUtils.isNotEmpty(selectTime)) {
            	param.put("demandTime", selectTime.substring(0, 10));
            }
            param.put("submissionType", "DAY");
            List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectVOByParams(param);
            if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOS)) {
                throw new BusinessException("此主机厂 " + oemCode + " 在时间 " + selectTime + " 下无装车需求提报数据");
            }
            // 获取productCode和对应的demandQuantity的映射
            Map<String, BigDecimal> productCodeToQuantityMap = loadingDemandSubmissionDetailVOS.stream()
                    .collect(Collectors.groupingBy(
                    		e-> String.join("&", e.getProductCode(), e.getDemandCategory()),
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    detail -> Optional.ofNullable(detail.getDemandQuantity()).orElse(BigDecimal.ZERO),
                                    BigDecimal::add
                            )
                    ));
            // 获取productCode和productName的映射
            List<String> productCodeList = loadingDemandSubmissionDetailVOS.stream()
                    .map(LoadingDemandSubmissionDetailVO::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(scenario, productCodeList);
            Map<String, NewProductStockPointVO> productCodeToNameMap = newProductStockPointVOS.stream()
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
            for (Entry<String, BigDecimal> entry : productCodeToQuantityMap.entrySet()) {
            	String[] splitKey = entry.getKey().split("&");
                DeliveryReturnVO deliveryReturnVO = new DeliveryReturnVO();
                deliveryReturnVO.setProductCode(splitKey[0]);
                deliveryReturnVO.setDemandCategory(splitKey[1]);
                NewProductStockPointVO stockPointVO = productCodeToNameMap.get(splitKey[0]);
                if (stockPointVO != null) {
                    deliveryReturnVO.setProductName(stockPointVO.getProductName());
                }
                deliveryReturnVO.setDeliveryQuantity(entry.getValue().toBigInteger().toString());
                deliveryReturnVOList.add(deliveryReturnVO);
            }
        }
        //勾选只展示当天数据时，只展示需求需求大于0的数据
        if(YesOrNoEnum.YES.getCode().equals(currentDayFlag) && CollectionUtils.isNotEmpty(deliveryReturnVOList)) {
        	deliveryReturnVOList = deliveryReturnVOList.stream()
        			.filter( e -> new BigDecimal(e.getDeliveryQuantity()).compareTo(BigDecimal.ZERO) > 0)
        			.collect(Collectors.toList());
        }
        return deliveryReturnVOList;
    }

    /**
     * 保存时，如果还没发布就只是保存；否则每次保存都发送对接单
     *
     * @param deliveryDockingOrderDTO
     * @return
     */
    @Override
    public BaseResponse<Void> updateAndSync(DeliveryDockingOrderDTO deliveryDockingOrderDTO) {
    	String code = deliveryDockingOrderDTO.getDeliveryDockingNumber();
    	List<LabelValue<String>> oemAddressLableList = oemAddressInventoryLogService.selectOemAddressDropdown(
    			deliveryDockingOrderDTO.getTransferWarehouse(),
    			deliveryDockingOrderDTO.getCustomer(), deliveryDockingOrderDTO.getOemAddress());
    	List<String> customerAdds = oemAddressLableList.stream().map(LabelValue::getValue).collect(Collectors.toList());
    	if(!customerAdds.contains(deliveryDockingOrderDTO.getOemAddress())) {
    		throw new BusinessException("中转库（子库存）" + deliveryDockingOrderDTO.getTransferWarehouse()
    		+ "客户（库位）" + deliveryDockingOrderDTO.getCustomer() + "地址" + deliveryDockingOrderDTO.getOemAddress() + "不存在");
    	}
    	// 获取详情单数据
    	List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS = this.getDeliveryDetailData(code);
        if (StringUtils.isNotEmpty(deliveryDockingOrderDTO.getMesTallyNumber())) {
            // 转化对象
            DeliveryDockingOrderVO deliveryDockingOrderVO = new DeliveryDockingOrderVO();
            BeanUtils.copyProperties(deliveryDockingOrderDTO, deliveryDockingOrderVO);
            // 调用接口
            this.doCheckData(deliveryDockingOrderVO, deliveryDockingOrderDetailVOS, code);
        }else {
        	//校验理货单模式
        	List<String> productCodes = deliveryDockingOrderDetailVOS.stream()
            		.map(DeliveryDockingOrderDetailVO::getProductCode).collect(Collectors.toList());
        	List<OemVO> oemVOs = deliveryDockingOrderDetailService.getAndCheckOemCodes(deliveryDockingOrderDTO.getOemCode());
        	deliveryDockingOrderDetailService.getDeliveryDockingOrderTallyOrderMode(productCodes, oemVOs);
        }
        this.doUpdate(deliveryDockingOrderDTO);
        return BaseResponse.success("保存成功");

    }

    /**
     * 发布或撤回
     *
     * @param code      发货对接单号
     * @param flag      true发布 false撤回
     * @param detailVOS
     * @return
     */
    @Override
    public BaseResponse<String> isPublish(String code, Boolean flag, List<DeliveryDockingOrderDetailVO> detailVOS) {
        // 获取当前发货对接单数据
        DeliveryDockingOrderVO mainVo = this.getDeliveryDockingData(code);
        // 获取详情单数据
        List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS = this.getDeliveryDetailData(code);
        // 获取撤销前发货数据
        Map<String, Integer> oldDataMap = new HashMap<>();
        // 撤回多的操作
        if (!flag) {
            if (StringUtils.isEmpty(mainVo.getMesTallyNumber())) {
                log.info("理货单号为空，并无进行数据同步至对应系统。无法进行撤回操作。");
                return BaseResponse.error("理货单号为空，并无进行数据同步至对应系统。无法进行撤回操作。");
            }
            deliveryDockingOrderDetailVOS.forEach(x -> {
                oldDataMap.put(x.getDemandSourcesId(), x.getDeliveryQuantity());
                x.setDeliveryQuantity(0);
            });
            // 仅对MES起作用 撤回时 修改状态CANCELLED
            mainVo.setStatus(StatusEnum.CANCELLED.getCode());
        } else {
            // 仅对MES起作用 发布时 修改状态SUBMIT
            mainVo.setStatus(StatusEnum.SUBMIT.getCode());
            //校验详情表数据和前端界面是否匹配上
            //1、校验行数是否对上
            Map<String, List<DeliveryDockingOrderDetailVO>> collect = deliveryDockingOrderDetailVOS.stream()
                    .collect(Collectors.groupingBy(DeliveryDockingOrderDetailVO::getDeliveryDockingLineNumber));
            //数据库行号集合
            Set<String> lineNumberSet = collect.keySet();
            if (detailVOS.size() == collect.size()) {
                for (DeliveryDockingOrderDetailVO vo : detailVOS) {
                    String deliveryDockingLineNumber = vo.getDeliveryDockingLineNumber();
                    if (lineNumberSet.contains(deliveryDockingLineNumber)) {
                        List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS1 = collect.get(deliveryDockingLineNumber);
                        int sum = deliveryDockingOrderDetailVOS1.stream().mapToInt(DeliveryDockingOrderDetailVO::getDeliveryQuantity).sum();
                        if (sum != vo.getDeliveryQuantity()) {
                            log.error("发布操作失败，在发货对接单{}页面的详情数据中编码为{}行号为{}的数据。发货数量{}与数据库{}的发货数量匹配不上",
                                    code, vo.getProductCode(), deliveryDockingLineNumber, vo.getDeliveryQuantity(), sum);
                            return BaseResponse.error("网络异常问题，请刷新页面重试。");
                        }
                    } else {
                        log.error("发布操作失败，在发货对接单{}页面的详情数据中编码为{}行号为{}的数据在发货单详情数据库并没有匹配。", code, vo.getProductCode(), deliveryDockingLineNumber);
                        return BaseResponse.error("网络异常问题，请刷新页面重试。");
                    }
                }
            } else {
                log.error("发布操作失败，发货对接单{}前端界面详情数据有{}条，但是详情表数据按照行号分类只有{}条。", code, detailVOS.size(), collect.size());
                return BaseResponse.error("网络异常问题，请刷新页面重试。");
            }
        }
        this.doCheckData(mainVo, deliveryDockingOrderDetailVOS, code);
        if (flag) {
            return BaseResponse.success("发布成功");
        } else {
            // 撤回时更新详情表里发货数量都为空
            List<DeliveryDockingOrderDetailDTO> updateDetailDTOs = ListUtil.list(false);
            deliveryDockingOrderDetailVOS.stream().forEach(x -> {
                DeliveryDockingOrderDetailDTO deliveryDockingOrderDTO = new DeliveryDockingOrderDetailDTO();
                BeanUtils.copyProperties(x, deliveryDockingOrderDTO);
                updateDetailDTOs.add(deliveryDockingOrderDTO);
            });
            deliveryDockingOrderDetailService.doUpdateBatch(updateDetailDTOs);

            // 获取撤回时demandResourceId的集合
            List<String> collect = updateDetailDTOs.stream().map(DeliveryDockingOrderDetailDTO::getDemandSourcesId).collect(Collectors.toList());
            List<FdpOriginDemandInterfaceLogVO> fdpOriginDemandInterfaceLogVOS = fdpOriginDemandInterfaceLogService.selectByRelIds(collect, ApiSourceEnum.GRP.getCode());
            if (!fdpOriginDemandInterfaceLogVOS.isEmpty()) {
                List<FdpOriginDemandInterfaceLogDTO> updates = new ArrayList();
                fdpOriginDemandInterfaceLogVOS.forEach(x -> {
                    // 获取旧数据的已制单数
                    Integer oldOrderQty = oldDataMap.get(x.getRelId());
                    BigDecimal bigDecimal = new BigDecimal(oldOrderQty);
                    // 已制单数减去旧数据
                    x.setOrderedQty(x.getOrderedQty().subtract(bigDecimal));
                    // 可制单数加上旧数据
                    x.setAvailableQty(x.getAvailableQty().add(bigDecimal));
                    FdpOriginDemandInterfaceLogDTO fdpOriginDemandInterfaceLogDTO = new FdpOriginDemandInterfaceLogDTO();
                    BeanUtils.copyProperties(x, fdpOriginDemandInterfaceLogDTO);
                    updates.add(fdpOriginDemandInterfaceLogDTO);
                });
                fdpOriginDemandInterfaceLogService.doUpdateBatch(updates);
            }

            return BaseResponse.success("撤回成功");
        }
    }

    /**
     * 检查数据并且发送给grp或mes
     *
     * @param mainVo
     * @param deliveryDockingOrderDetailVOS
     * @param code
     */
    @Override
    public void doCheckData(DeliveryDockingOrderVO mainVo, List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS, String code) {
        String scenario = SystemHolder.getScenario();
        //数据保存校验
        DeliveryDockingOrderDO deliveryDockingOrderDO = new DeliveryDockingOrderDO();
        BeanUtils.copyProperties(mainVo, deliveryDockingOrderDO);
        deliveryDockingOrderDomainService.validationForUpdate(deliveryDockingOrderDO);

        String productCode = deliveryDockingOrderDetailVOS.get(0).getProductCode();
        if (StringUtils.isEmpty(productCode)) {
            log.error("发货对接单详情表中存在一条数据的本厂编码字段为空，请进行数据补充。");
            throw new BusinessException("发货对接单详情表中存在一条数据的本厂编码字段为空，请进行数据补充。");
        }
        // 获取主机厂数据
        String oemCode = mainVo.getOemCode();
        if (StringUtils.isEmpty(oemCode)) {
            log.error("缺少主机厂编码数据，无法关联到主机厂表获取组织id。");
            throw new BusinessException("缺少主机厂编码数据，请进行数据补充并保存。");
        }
        HashMap<String, Object> oemMap = MapUtil.newHashMap();
        oemMap.put("oemCodes", new ArrayList<>(Arrays.asList(oemCode.split(","))));
        oemMap.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = oemService.selectByParams(oemMap);
        if (oemVOS.isEmpty()) {
            log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", oemCode);
            throw new BusinessException("该数据的主机厂编码是" + oemCode + "，但是在主机厂表中匹配不到数据。");
        }
        //获取理货单模式
        List<String> productCodes = deliveryDockingOrderDetailVOS.stream()
        		.map(DeliveryDockingOrderDetailVO::getProductCode).collect(Collectors.toList());
        String tallyOrderMode = deliveryDockingOrderDetailService.getDeliveryDockingOrderTallyOrderMode(productCodes, oemVOS);

        OemVO oemVO = oemVOS.get(0);
        // 获取库存点数据(默认多选的主机厂都是同一个组织下的)
        HashMap<String, Object> stockPointMap = MapUtil.newHashMap();
        stockPointMap.put("organizeId", oemVO.getSaleOrgId());
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario, stockPointMap);
        if (newStockPointVOS.isEmpty()) {
            log.error("该数据的销售组织id是{}，但是在库存点表中匹配不到数据。", oemVO.getSaleOrgId());
            throw new BusinessException("该数据的销售组织id是" + oemVO.getSaleOrgId() + "，但是在库存点表中匹配不到数据。");
        }
        NewStockPointVO stockPointVO = newStockPointVOS.get(0);
        // 获取物料数据
        HashMap<String, Object> productStockPointMap = MapUtil.newHashMap();
        productStockPointMap.put("stockPointCode", stockPointVO.getStockPointCode());
        productStockPointMap.put("productCodes", productCodes);
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario, productStockPointMap);
        if (newProductStockPointVOS.isEmpty()) {
            log.error("该数据的库存点编码是{}，随机的一个详情表物料编码是{}，但是在物料表中匹配不到数据。", stockPointVO.getStockPointCode(), productCode);
            throw new BusinessException("该数据的库存点编码是" + stockPointVO.getStockPointCode() + "，随机的一个详情表物料编码是" + productCode + "，但是在物料表中匹配不到数据。");
        }

        //主机厂理货单模式为GRP走GRP逻辑，否则走MES逻辑
        if (OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
            this.doGrpDeliveryDocking(mainVo, deliveryDockingOrderDetailVOS, code, oemVO.getSaleOrgId());
        } else {
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("customerAddress",mainVo.getOemAddress());//主机厂地址
            map.put("sendLocatorCode",mainVo.getCustomer());//货位 客户
            map.put("sendWarehouseCode",mainVo.getTransferWarehouse());//子库存
            map.put("enableFlag","Y");
            List<OemAddressInventoryLogVO> oemAddressInventoryLogVOS = oemAddressInventoryLogService.selectByParams(map);
            if (oemAddressInventoryLogVOS.isEmpty()) {
                log.error("在主机厂地址中间表中，根据子库存，货位和地址匹配不到数据，请进行维护。");
                throw new BusinessException("在主机厂地址中间表中，根据子库存，货位和地址匹配不到数据，请进行维护。");
            } else {
                Optional<OemAddressInventoryLogVO> first = oemAddressInventoryLogVOS.stream().filter(x -> "Y".equals(x.getIsDefaultAddress())).findFirst();
                OemAddressInventoryLogVO oemAddressInventoryLogVO;
                if (first.isPresent()) {
                    oemAddressInventoryLogVO = first.get();
                } else {
                    oemAddressInventoryLogVO = oemAddressInventoryLogVOS.get(0);
                }
                this.doMesDeliveryDocking(mainVo, deliveryDockingOrderDetailVOS, stockPointVO, newProductStockPointVOS, oemAddressInventoryLogVO.getCustomerCode(), scenario);
            }
        }
    }

    /**
     * 通过发货对接单号获取详情表数据
     *
     * @param code 发货对接单号
     * @return
     */
    @Override
    public List<DeliveryDockingOrderDetailVO> getDeliveryDetailData(String code) {
        HashMap<String, Object> detailMap = MapUtils.newHashMap();
        detailMap.put("deliveryDockingNumber", code);
        List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS = deliveryDockingOrderDetailService.selectByParams(detailMap);
        if (deliveryDockingOrderDetailVOS.isEmpty()) {
            log.error("没有找到该发货对接单的详情表数据");
            throw new BusinessException("没有找到该发货对接单的详情表数据");
        }
        return deliveryDockingOrderDetailVOS;
    }

    /**
     * 通过发货对接单号获取表数据
     *
     * @param code 发货对接单号
     * @return
     */
    @Override
    public DeliveryDockingOrderVO getDeliveryDockingData(String code) {
        HashMap<String, Object> mainMap = MapUtils.newHashMap();
        mainMap.put("deliveryDockingNumber", code);
        List<DeliveryDockingOrderVO> deliveryDockingOrderVOS = this.selectByParams(mainMap);
        if (deliveryDockingOrderVOS.isEmpty()) {
            log.error("没有找到该发货对接单数据");
            throw new BusinessException("没有找到该发货对接单数据");
        } else {
            if (deliveryDockingOrderVOS.size() > 1) {
                deliveryDockingOrderVOS = deliveryDockingOrderVOS.stream()
                        .sorted((x1, x2) -> x2.getModifyTime().compareTo(x1.getModifyTime())).collect(Collectors.toList());
            }
        }
        return deliveryDockingOrderVOS.get(0);
    }

    /**
     * 向MES发送理货单
     *
     * @param mainVo                        发货对接单对象
     * @param deliveryDockingOrderDetailVOS 发货对接单详情对象集合
     * @param stockPointVO                  库存点对象
     * @param newProductStockPointVOS       物料对象集合
     * @param customerCode                  客户编码
     * @param scenario                      调用feigin
     * @return
     */
    private void doMesDeliveryDocking(DeliveryDockingOrderVO mainVo, List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS, NewStockPointVO stockPointVO, List<NewProductStockPointVO> newProductStockPointVOS, String customerCode, String scenario) {
        log.info("向MES发布发货对接单");
        // 接口实体对象
        MesEdiInterfaceDTO mesEdiInterfaceDTO = new MesEdiInterfaceDTO();
        // 发货对接单ID
        mesEdiInterfaceDTO.setReqNumId(mainVo.getId());
        // 发货组织  上海默认SJ
        mesEdiInterfaceDTO.setShipmentPlantId(stockPointVO.getStockPointCode());
        // 发货对接单号
        mesEdiInterfaceDTO.setReqNum(mainVo.getDeliveryDockingNumber());
        // 数据类型 MES理货单号为空时创建0 否则变更1
        // 发货对接单状态
        if (StringUtils.isEmpty(mainVo.getMesTallyNumber())) {
            mesEdiInterfaceDTO.setDataType(0);
            mesEdiInterfaceDTO.setReqStatus("RELEASE"); // 传递给MES
        } else {
            mesEdiInterfaceDTO.setDataType(1);
            if (StatusEnum.CANCELLED.getCode().equals(mainVo.getStatus())) {
                mesEdiInterfaceDTO.setReqStatus("DELETE"); // 撤回数据
            } else if (StatusEnum.SUBMIT.getCode().equals(mainVo.getStatus())) {
                mesEdiInterfaceDTO.setReqStatus("RELEASE"); // 传递给MES
            } else {
                log.error("同步数据给MES的这条数据，状态已经是完成，不可再同步。");
                throw new BusinessException("同步数据给MES的这条数据，状态已经是完成，不可再同步。");
            }
        }
        // 子库存
        if (Objects.isNull(mainVo.getTransferWarehouse())) {
            log.error("数据中的子库存字段为空,请补充数据。");
            throw new BusinessException("数据中的子库存字段为空,请补充数据。");
        }
        mesEdiInterfaceDTO.setShipmentWarehouseCode(mainVo.getTransferWarehouse());
        // 货位
        if (Objects.isNull(mainVo.getCustomer())) {
            log.error("数据中的货位字段为空,请补充数据。");
            throw new BusinessException("数据中的货位字段为空,请补充数据。");
        }
        mesEdiInterfaceDTO.setShipmentLocatorCode(mainVo.getCustomer());
        String lineCode = mainVo.getLineCode();
        // 运输路径编码
        if (Objects.isNull(lineCode)) {
            log.error("数据中的运输路径字段为空,请补充数据。");
            throw new BusinessException("数据中的运输路径字段为空,请补充数据。");
        } else {
            if (lineCode.contains("/")) {
                String[] split = lineCode.split("/");
                lineCode = split[0];
            }
        }
        mesEdiInterfaceDTO.setLineCode(lineCode);
        // 运输方式
        if (Objects.isNull(mainVo.getTransportMode())) {
            log.error("数据中的运输方式字段为空,请补充数据。");
            throw new BusinessException("数据中的运输方式字段为空,请补充数据。");
        }
        mesEdiInterfaceDTO.setTransportMode(mainVo.getTransportMode());
        // 运输方向
        if (Objects.isNull(mainVo.getTransportDirection())) {
            log.error("数据中的运输方向字段为空,请补充数据。");
            throw new BusinessException("数据中的运输方向字段为空,请补充数据。");
        }
        mesEdiInterfaceDTO.setTransDirection(mainVo.getTransportDirection());
        // 客户地址
        if (Objects.isNull(mainVo.getOemAddress())) {
            log.error("数据中的客户地址字段为空,请补充数据。");
            throw new BusinessException("数据中的客户地址字段为空,请补充数据。");
        }
        mesEdiInterfaceDTO.setCustomerAddress(mainVo.getOemAddress());
        // 客户ID 根据customerCode获取客户中间表
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("customerCode", customerCode);
        List<CustomerVO> customerVOS = newMdsFeign.selectCustomerByParams1(scenario, map);
        if (customerVOS.isEmpty()) {
            throw new BusinessException("通过该主机厂对应的客户编码"+customerCode+"，在客户表中未找到对应的数据，请进行数据维护。");
        }
        // 根据customerCode获取客户中间表
        mesEdiInterfaceDTO.setCustomerId(customerVOS.get(0).getCustomerId());
        // 发货时间
        if (Objects.isNull(mainVo.getDeliveryTime())) {
            log.error("数据中的发货时间字段为空,请补充数据。");
            throw new BusinessException("数据中的发货时间字段为空,请补充数据。");
        }
        mesEdiInterfaceDTO.setExpectShipmentDate(DateUtils.dateToString(mainVo.getDeliveryTime(),
                DateUtils.COMMON_DATE_STR1));
        // 车长
        String vehicleInfo = mainVo.getVehicleInfo();
        StringBuffer vehicleLength = new StringBuffer("");
        if (StringUtils.isEmpty(vehicleInfo)){
            vehicleLength.append(mainVo.getVehicleLength());
            if (Objects.isNull(vehicleLength)) {
                log.error("数据中的车长字段为空,请补充数据。");
                throw new BusinessException("数据中的车长字段为空,请补充数据。");
            }
            Integer vehicleNumber = mainVo.getVehicleNumber();
            if (Objects.nonNull(vehicleNumber)){
                if (vehicleNumber>1){
                    vehicleLength.append("*" + vehicleNumber);
                }
            }
        } else {
            List<Map> mapList = JSONObject.parseArray(vehicleInfo, Map.class);
            for (int i = 0; i < mapList.size(); i++) {
                Map itemMap = mapList.get(i);
                String length = (String) itemMap.get("vehicleLength");
                int number = Integer.parseInt(itemMap.get("vehicleNumber").toString());
                if (number > 1) {
                    vehicleLength.append(length + "*" + number);
                } else {
                    vehicleLength.append(length);
                }
                if (i != mapList.size() - 1) {
                    vehicleLength.append("/");
                }
            }
        }
        mesEdiInterfaceDTO.setCarLength(vehicleLength.toString());
        // 柜型  非必填
//        if (Objects.isNull(mainVo.getCabinetType())) {
//            log.error("数据中的柜型字段为空,请补充数据。");
//            throw new BusinessException("数据中的柜型字段为空,请补充数据。");
//        }
        mesEdiInterfaceDTO.setContainerType(mainVo.getCabinetType());
        // 创建人 根据creator找用户ID
        if (StringUtils.isEmpty(mainVo.getCreator())) {
            log.error("数据中的创建人字段为空，请联系管理员补充数据。");
            throw new BusinessException("数据中的创建人字段为空，请联系管理员补充数据。");
        }
        User user = ipsNewFeign.userById(mainVo.getCreator());
        if (Objects.isNull(user)) {
            log.error("用户表中匹配不到该数据的创建人{}", mainVo.getCreator());
            throw new BusinessException("用户表中匹配不到该数据的创建人" + mainVo.getCreator());
        }
        mesEdiInterfaceDTO.setCreationStaff(user.getUserName());
        if(Objects.isNull(mainVo.getExpectedArriveTime())){
            log.error("数据中的预计到货时间不能为空。");
            throw new BusinessException("数据中的预计到货时间不能为空。");
        }
        // 预计到货时间
        mesEdiInterfaceDTO.setExpectDeliverDate(DateUtils.dateToString(mainVo.getExpectedArriveTime(), DateUtils.COMMON_DATE_STR1));
        //------------非必录------------
        // 柜号
        mesEdiInterfaceDTO.setContainerNo(mainVo.getContainerNumber());
        // 发运编码
        mesEdiInterfaceDTO.setShippingNum(mainVo.getDeliveryTransportCode());

        // 头备注
        mesEdiInterfaceDTO.setRemark(mainVo.getRemark());
        //------------发货对接单集合-行表集合------------
        List<MesEdiInterfaceDetailDTO> list = ListUtil.list(false);
        // 物料表的字段过滤
        Map<String, String> collect = newProductStockPointVOS.stream().collect(Collectors.toMap(
                item -> item.getProductCode(),
                item -> item.getInventoryItemId(),
                (item1, item2) -> item1
        ));
        Set<String> existingProductCodes = collect.keySet();
        // 数据遍历
        deliveryDockingOrderDetailVOS.stream().forEach(item -> {
            MesEdiInterfaceDetailDTO mesEdiInterfaceDetailDTO = new MesEdiInterfaceDetailDTO();
            // 发货对接单行号
            mesEdiInterfaceDetailDTO.setLineNo(item.getId());
            // 物料id 根据productCode找inventory_item_id
            if (existingProductCodes.contains(item.getProductCode())) {
                if (StringUtils.isEmpty(collect.get(item.getProductCode()))) {
                    log.error("发货对接单详情表中本厂编码{}在物料表中匹配的物料id为空。", item.getProductCode());
                    throw new BusinessException("发货对接单详情表中本厂编码" + item.getProductCode() + "在物料表中匹配的物料id为空。");
                } else {
                    mesEdiInterfaceDetailDTO.setItemId(collect.get(item.getProductCode()));
                }
            } else {
                log.error("发货对接单详情表中本厂编码{}在物料表中匹配不到数据。", item.getProductCode());
                throw new BusinessException("发货对接单详情表中本厂编码" + item.getProductCode() + "在物料表中匹配不到数据。");
            }
            // 器具类型
            if (Objects.isNull(item.getMaterialEquipment())) {
                log.error("发货对接单详情表中器具类型字段为空。");
                throw new BusinessException("发货对接单详情表中器具类型字段为空。");
            }
            mesEdiInterfaceDetailDTO.setPackingBox(item.getMaterialEquipment());
            // 发货数量
            if (Objects.isNull(item.getDeliveryQuantity())) {
                log.error("发货对接单详情表中发货数量字段为空。");
                throw new BusinessException("发货对接单详情表中发货数量字段为空");
            }
            mesEdiInterfaceDetailDTO.setPlanShipQty(item.getDeliveryQuantity());
            // 必发数量
            if (Objects.isNull(item.getMustQuantity())) {
                log.error("发货对接单详情表中必发数量字段为空。");
                throw new BusinessException("发货对接单详情表中必发数量字段为空");
            }
            mesEdiInterfaceDetailDTO.setNeedShipQty(item.getMustQuantity());
            // 计划箱数
            if (Objects.isNull(item.getBoxNumber())) {
                log.error("发货对接单详情表中计划箱数字段为空。");
                throw new BusinessException("发货对接单详情表中计划箱数字段为空");
            }
            mesEdiInterfaceDetailDTO.setPlanShipBox(Integer.parseInt(item.getBoxNumber()));
            // 发票号  非必填
//            if (Objects.isNull(item.getInvoiceNumber())) {
//                log.error("发货对接单详情表中发票号字段为空。");
//                throw new BusinessException("发货对接单详情表中发票号字段为空。");
//            }
            mesEdiInterfaceDetailDTO.setInvoiceNum(item.getInvoiceNumber());
            //重量
            mesEdiInterfaceDetailDTO.setWeight(item.getGrossWeight());
            //体积
            mesEdiInterfaceDetailDTO.setVolume(item.getVolume());
            //提单号
            mesEdiInterfaceDetailDTO.setLadingNumber(item.getBillLadingNumber());
            // 行备注
            mesEdiInterfaceDetailDTO.setLineRemark(item.getRemark());
            list.add(mesEdiInterfaceDetailDTO);
        });
        mesEdiInterfaceDTO.setReqlineList(list);
        // 传输数据对象
        HashMap<String, Object> dcpMap = MapUtil.newHashMap();
        dcpMap.put("data", mesEdiInterfaceDTO);
        BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(SystemHolder.getTenantId(),
                ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.DELIVERY_DOCKING_MES.getCode(), dcpMap);
        if (baseResponse.getSuccess()) {
            Map<String, Object> map1 = JSONObject.parseObject(baseResponse.getData(), Map.class);
            this.updateMesDeliveryDockingStatus(map1);
        } else {
            log.error("调用mes理货单接口报错，{}", baseResponse.getMsg());
            throw new BusinessException(baseResponse.getMsg());
        }
    }

    /**
     * 向GRP发送理货单
     *
     * @param mainVo                        发货对接单数据
     * @param deliveryDockingOrderDetailVOS 发货对接单详表对象集合
     * @param code                          发货对接单编码
     * @param saleOrgId                     销售组织id
     * @return
     */
    private void doGrpDeliveryDocking(DeliveryDockingOrderVO mainVo, List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS, String code, String saleOrgId) {
        log.info("向GRP发布发货对接单");
        // 接口实体对象
        EdiInterfaceDTO ediInterfaceDTO = new EdiInterfaceDTO();
        //----------------必填----------------
        // 组织id
        ediInterfaceDTO.setOrgId(saleOrgId);
        // 跟踪号
        ediInterfaceDTO.setSthMstShipNo(code);
        // 数据类型 MES理货单号为空时创建0 否则变更1
        if (StringUtils.isEmpty(mainVo.getMesTallyNumber())) {
            ediInterfaceDTO.setType("0");
        } else {
            ediInterfaceDTO.setType("1");
        }
        // 计划发运日期
        if (Objects.isNull(mainVo.getDeliveryTime())) {
            log.error("缺少计划发运日期，请先补充数据并保存。");
            throw new BusinessException("缺少计划发运日期，请先补充数据并保存。");
        }
        ediInterfaceDTO.setSthPlanShipDate(DateUtils.dateToString(mainVo.getDeliveryTime(), DateUtils.COMMON_DATE_STR1));
        // 到货日期
        if (Objects.isNull(mainVo.getExpectedArriveTime())) {
            log.error("缺少到货日期，请先补充数据并保存。");
            throw new BusinessException("缺少到货日期，请先补充数据并保存。");
        }
        ediInterfaceDTO.setSthDeliveryDate(DateUtils.dateToString(mainVo.getExpectedArriveTime(), DateUtils.COMMON_DATE_STR1));
        // 用户工号 根据创建人判断工号
        if (StringUtils.isEmpty(mainVo.getCreator())) {
            log.error("缺少数据创建人，请先补充数据并保存。");
            throw new BusinessException("缺少数据创建人，请先补充数据并保存。");
        }
        User user = ipsNewFeign.userById(mainVo.getCreator());
        if (Objects.isNull(user)) {
            log.error("用户表中匹配不到该数据的创建人{}", mainVo.getCreator());
            throw new BusinessException("用户表中匹配不到该数据的创建人" + mainVo.getCreator());
        }
        ediInterfaceDTO.setUser(user.getUserName());
        //----------------详情表----------------
        List<EdiInterfaceDetailDTO> list = ListUtil.list(false);
        for (DeliveryDockingOrderDetailVO vo : deliveryDockingOrderDetailVOS) {
            EdiInterfaceDetailDTO ediInterfaceDetailDTO = new EdiInterfaceDetailDTO();
            // 需求来源id
            if (StringUtils.isEmpty(vo.getDemandSourcesId())) {
                log.error("发货对接单号为{}的发货对接详情数据没有需求来源id", code);
                throw new BusinessException("发货对接单号为" + code + "的发货对接详情数据没有需求来源id");
            }
            ediInterfaceDetailDTO.setRelId(vo.getDemandSourcesId());
            // 发运数量
            if (Objects.isNull(vo.getDeliveryQuantity())) {
                log.error("发货对接单号为{}的发货对接详情数据没有发运数量", code);
                throw new BusinessException("发货对接单号为" + code + "的发货对接详情数据没有发运数量");
            }
            ediInterfaceDetailDTO.setTargetOrderQty(vo.getDeliveryQuantity());
            list.add(ediInterfaceDetailDTO);
        }
        ediInterfaceDTO.setLines(list);
        //----------------非必填----------------
        // 发运备注
        ediInterfaceDTO.setSthRemarks(mainVo.getRemark());
        // 运单号
        ediInterfaceDTO.setSthFreightBill(mainVo.getDeliveryTransportCode());
        // 封条号
        ediInterfaceDTO.setSthSealNumber(mainVo.getExitBarCode());
        // 子库存
        ediInterfaceDTO.setSthSubinv(mainVo.getTransferWarehouse());
        // 货位
        ediInterfaceDTO.setSthLocator(mainVo.getCustomer());
        // 货柜号
        ediInterfaceDTO.setSthontainerNo(mainVo.getContainerNumber());
        // 新增字段
        ediInterfaceDTO.setSthAuthNo(mainVo.getSthAuthNo()); // 提单号
        ediInterfaceDTO.setSthRouteNo(mainVo.getSthRouteNo()); // 船东
        ediInterfaceDTO.setMrnNo(mainVo.getMrnNo()); // 海关跟踪号
        ediInterfaceDTO.setContact(mainVo.getContact()); // 收货联系人
        ediInterfaceDTO.setContactPhone(mainVo.getContactPhone()); // 收货联系人电话
        ediInterfaceDTO.setStdContract(mainVo.getStdContract()); // pus号
        ediInterfaceDTO.setSthCarrId(mainVo.getSthCarrId()); // 承运人ID
        ediInterfaceDTO.setSthFreightTerms(mainVo.getSthFreightTerms()); // 运输条款
        ediInterfaceDTO.setSthEquipNo(mainVo.getSthEquipNo()); // 运输工具编码
        // 传输数据对象
        List<EdiInterfaceDTO> syncList = ListUtil.list(false);
        syncList.add(ediInterfaceDTO);

        HashMap<String, Object> dcpMap = MapUtil.newHashMap();
        dcpMap.put("list", syncList);
        BaseResponse<String> voidBaseResponse = newDcpFeign.callExternalApi(SystemHolder.getTenantId(),
                ApiSourceEnum.GRP.getCode(),
                ApiCategoryEnum.DELIVERY_DOCKING_GRP.getCode(), dcpMap);
        if (voidBaseResponse.getSuccess()) {
            List<GrpEdiDeliveryDetail> grpEdiDeliveryDetails = JSONObject.parseArray(voidBaseResponse.getData(), GrpEdiDeliveryDetail.class);
            this.updateDeliveryDockingStatus(grpEdiDeliveryDetails);
        } else {
            log.error("调用grp理货单接口报错，{}", voidBaseResponse.getMsg());
            throw new BusinessException(voidBaseResponse.getMsg());
        }
    }

    @Override
    public BaseResponse<Void> updateDeliveryDockingStatus(List<GrpEdiDeliveryDetail> list) {
        Set<String> shipNos = list.stream().map(GrpEdiDeliveryDetail::getMstShipNo).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("deliveryDockingNumbers", shipNos);
        List<DeliveryDockingOrderVO> deliveryDockingOrderVOS = this.selectByParams(map);
        List<DeliveryDockingOrderDTO> updateList = ListUtil.list(false);
        for (DeliveryDockingOrderVO vo : deliveryDockingOrderVOS) {
            Optional<GrpEdiDeliveryDetail> first = list.stream().filter(x -> vo.getDeliveryDockingNumber().equals(x.getMstShipNo())).findFirst();
            if (first.isPresent()) {
                GrpEdiDeliveryDetail grpEdiDeliveryDetail = first.get();
                vo.setStatus(grpEdiDeliveryDetail.getSthShipperStatus());
                vo.setMesTallyNumber(grpEdiDeliveryDetail.getSthShipNo());
                DeliveryDockingOrderDTO deliveryDockingOrderDTO = new DeliveryDockingOrderDTO();
                BeanUtils.copyProperties(vo, deliveryDockingOrderDTO);
                updateList.add(deliveryDockingOrderDTO);
            }
        }
        if (!updateList.isEmpty()) {
            doUpdateBatch(updateList);
        }
        return null;
    }

    @Override
    public BaseResponse<Void> updateMesDeliveryDockingStatus(Map<String, Object> map) {
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("deliveryDockingNumber", map.get("deliveryDockingNumber"));
        List<DeliveryDockingOrderVO> deliveryDockingOrderVOS = this.selectByParams(queryMap);
        if (!deliveryDockingOrderVOS.isEmpty()) {
            DeliveryDockingOrderVO deliveryDockingOrderVO = deliveryDockingOrderVOS.get(0);
            deliveryDockingOrderVO.setStatus(map.get("status").toString());
            deliveryDockingOrderVO.setMesTallyNumber(deliveryDockingOrderVO.getDeliveryDockingNumber());
            DeliveryDockingOrderDTO deliveryDockingOrderDTO = new DeliveryDockingOrderDTO();
            BeanUtils.copyProperties(deliveryDockingOrderVO, deliveryDockingOrderDTO);
            this.doUpdate(deliveryDockingOrderDTO);
        }
        return null;
    }

    @Override
    public List<String> getVehicleLength() {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<TransportResourceVO> transportResourceVOS = transportResourceService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(transportResourceVOS)) {
            return transportResourceVOS.stream()
                    .map(TransportResourceVO::getResourceName)
                    .filter(Objects::nonNull)
                    .distinct()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValue<String>> getProductCodeByOemCode(String oemCode) {
    	List<String> oemCodeList = new ArrayList<>(Arrays.asList(oemCode.split(",")));
        Map<String, Object> params = new HashMap<>(2);
        params.put("oemCodeList", oemCodeList);
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectByParams(params);
        if (CollectionUtils.isEmpty(oemVehicleModelVOS)) {
            throw new BusinessException("主机厂车型信息无此主机厂：" + oemCode);
        }
        List<String> vehicleModelCodeList = oemVehicleModelVOS.stream()
                .map(OemVehicleModelVO::getOemVehicleModelCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(vehicleModelCodeList)) {
        	throw new BusinessException("主机厂车型信息无此主机厂的车型：" + oemCode);
        }
        params.clear();
        params.put("productClassifyLikeOr", YesOrNoEnum.YES.getCode());
        params.put("vehicleCodeList", vehicleModelCodeList);
        List<NewProductStockPointVO> newProductStockPointVOS =
                newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), params);
        // 获取该主机厂对应的组织id
        HashMap<String, Object> oemMap = MapUtil.newHashMap();
        oemMap.put("oemCodes", oemCodeList);
        List<OemVO> oemVOS = oemService.selectByParams(oemMap);
        if (oemVOS.isEmpty()) {
            log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", oemCode);
            throw new BusinessException("该数据的主机厂编码是" + oemCode + "，但是在主机厂表中匹配不到数据。");
        }
        List<String> saleOrgIds = oemVOS.stream().map(OemVO::getSaleOrgId).distinct().collect(Collectors.toList());
        newProductStockPointVOS = newProductStockPointVOS.stream()
        		.filter(x -> saleOrgIds.contains(x.getOrganizationId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("此主机厂：" + oemCode + ",对应车型无本厂编码映射关系");
        }
        List<LabelValue<String>> list = newProductStockPointVOS.stream()
                .map(newProductStockPointVO -> new LabelValue<>(newProductStockPointVO.getProductName(), newProductStockPointVO.getProductCode()))
                .collect(Collectors.toList());
        
        return removeDuplicates(list);
    }

    private List<LabelValue<String>> removeDuplicates(List<LabelValue<String>> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                lv -> Arrays.asList(lv.getLabel(), lv.getValue()), // 使用 label 和 value 作为键
                                lv -> lv, // 值为 LabelValue 对象
                                (existing, replacement) -> existing // 如果有重复，保留第一个出现的
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    @Override
    public BaseResponse<Void> doDockingOrderFeedback(DeliveryDockingOrderApiDTO deliveryDockingOrderApiDTO) {

        if (Objects.isNull(deliveryDockingOrderApiDTO.getDeliveryDockingNumber())) {
            throw new BusinessException("接口参数：发货对接单号字段不能为空");
        }
        if (Objects.isNull(deliveryDockingOrderApiDTO.getSourceSystem())) {
            throw new BusinessException("接口参数：来源系统字段不能为空");
        }
        if (Objects.isNull(deliveryDockingOrderApiDTO.getStatus())) {
            throw new BusinessException("接口参数:状态字段不能为空");
        }
        if (Objects.isNull(deliveryDockingOrderApiDTO.getActualArriveTime())) {
            throw new BusinessException("接口参数：实际到货时间字段不能为空");
        }
        if (Objects.isNull(deliveryDockingOrderApiDTO.getMesTallyNumber())) {
            throw new BusinessException("接口参数：mes理货单号字段不能为空");
        }
        List<DeliveryDockingOrderDetailApiDTO> detailList = deliveryDockingOrderApiDTO.getDetails();

        if (CollectionUtils.isEmpty(detailList)) {
            throw new BusinessException("接口参数发货对接单详情不能为空");
        }

        List<DeliveryDockingOrderDetailDTO> updateOrderDetailList = Lists.newArrayList();
        DeliveryDockingOrderDTO deliveryDockingOrderDTO = new DeliveryDockingOrderDTO();

        if (ApiSourceEnum.GRP.getCode().equals(deliveryDockingOrderApiDTO.getSourceSystem())) {

            String deliveryDockingNumber = deliveryDockingOrderApiDTO.getDeliveryDockingNumber();

            Map<String, Object> params = new HashMap<>(2);
            params.put(ENABLED, YesOrNoEnum.YES.getCode());
            params.put("deliveryDockingNumber", deliveryDockingNumber);

            List<DeliveryDockingOrderVO> deliveryDockingOrderVOS = this.selectByParams(params);

            if (CollectionUtils.isEmpty(deliveryDockingOrderVOS)) {
                throw new BusinessException("无此发货对接单");
            }

            DeliveryDockingOrderVO deliveryDockingOrderVO = deliveryDockingOrderVOS.get(0);
            BeanUtils.copyProperties(deliveryDockingOrderVO, deliveryDockingOrderDTO);
            deliveryDockingOrderDTO.setStatus(deliveryDockingOrderApiDTO.getStatus());
            deliveryDockingOrderDTO.setMesTallyNumber(deliveryDockingOrderApiDTO.getMesTallyNumber());
            deliveryDockingOrderDTO.setActualArriveTime(deliveryDockingOrderApiDTO.getActualArriveTime());

            if (Objects.nonNull(deliveryDockingOrderApiDTO.getShoppingHouse())) {
                deliveryDockingOrderDTO.setSthRouteNo(deliveryDockingOrderApiDTO.getShoppingHouse());
            }
            Map<String, Object> detailParam = new HashMap<>(2);
            detailParam.put(ENABLED, YesOrNoEnum.YES.getCode());
            detailParam.put("deliveryDockingNumber", deliveryDockingNumber);
            List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS =deliveryDockingOrderDetailService
                    .selectByParams(detailParam);

            Map<String, DeliveryDockingOrderDetailVO> oldDetailVOMap = deliveryDockingOrderDetailVOS.stream()
                    .collect(Collectors.toMap(DeliveryDockingOrderDetailVO::getDemandSourcesId, Function.identity()));

            if (CollectionUtils.isEmpty(deliveryDockingOrderDetailVOS)) {
                throw new BusinessException("无此发货对接单详情");
            }

            if (detailList.size() > deliveryDockingOrderDetailVOS.size()) {
                throw new BusinessException("发货对接单详情数量不匹配");
            }

            for (DeliveryDockingOrderDetailApiDTO detailApiDTO : detailList) {
                String dataKey = detailApiDTO.getDeliveryDockingLineId();
                if (!oldDetailVOMap.containsKey(dataKey)) {
                    throw new BusinessException("无此发货对接单行号:" + dataKey);
                }
                DeliveryDockingOrderDetailVO oldDetailVO = oldDetailVOMap.get(dataKey);
                DeliveryDockingOrderDetailDTO detailDTO = new DeliveryDockingOrderDetailDTO();
                BeanUtils.copyProperties(oldDetailVO, detailDTO);
                if (Objects.isNull(detailApiDTO.getActualDeliveryQuantity())) {
                    throw new BusinessException("发货对接单实际发货数量字段为空");
                }
                detailDTO.setActualDeliveryBoxQuantity(detailApiDTO.getActualDeliveryBoxQuantity());
                detailDTO.setActualDeliveryQuantity(detailApiDTO.getActualDeliveryQuantity());
                updateOrderDetailList.add(detailDTO);
            }

        } else if (ApiSourceEnum.MES.getCode().equals(deliveryDockingOrderApiDTO.getSourceSystem())) {

            Map<String, Object> params = new HashMap<>(2);
            params.put(ENABLED, YesOrNoEnum.YES.getCode());
            params.put("id", deliveryDockingOrderApiDTO.getDeliveryDockingId());

            List<DeliveryDockingOrderVO> deliveryDockingOrderVOS = this.selectByParams(params);

            if (CollectionUtils.isEmpty(deliveryDockingOrderVOS)) {
                throw new BusinessException("无此发货对接单");
            }

            DeliveryDockingOrderVO deliveryDockingOrderVO = deliveryDockingOrderVOS.get(0);
            BeanUtils.copyProperties(deliveryDockingOrderVO, deliveryDockingOrderDTO);
            if (deliveryDockingOrderApiDTO.getStatus().equals("RELEASE")) {
                deliveryDockingOrderDTO.setStatus("SUBMIT");
            } else if (deliveryDockingOrderApiDTO.getStatus().equals("COMPLETE")) {
                deliveryDockingOrderDTO.setStatus("CLOSED");
            } else {
                throw new BusinessException("发货对接单状态错误");
            }
            deliveryDockingOrderDTO.setMesTallyNumber(deliveryDockingOrderApiDTO.getMesTallyNumber());
            deliveryDockingOrderDTO.setActualArriveTime(deliveryDockingOrderApiDTO.getActualArriveTime());

            if (Objects.nonNull(deliveryDockingOrderApiDTO.getShoppingHouse())) {
                deliveryDockingOrderDTO.setSthRouteNo(deliveryDockingOrderApiDTO.getShoppingHouse());
            }
            List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS =
                    this.selectDockingOrderDetail(deliveryDockingOrderVO.getDeliveryDockingNumber());

            Map<String, DeliveryDockingOrderDetailVO> oldDetailVOMap = deliveryDockingOrderDetailVOS.stream()
                    .collect(Collectors.toMap(DeliveryDockingOrderDetailVO::getId, Function.identity()));

            if (CollectionUtils.isEmpty(deliveryDockingOrderDetailVOS)) {
                throw new BusinessException("无此发货对接单详情");
            }
            if (detailList.size() > deliveryDockingOrderDetailVOS.size()) {
                throw new BusinessException("发货对接单详情数量不匹配");
            }
            for (DeliveryDockingOrderDetailApiDTO detailApiDTO : detailList) {

                String dataKey = detailApiDTO.getDeliveryDockingLineId();
                if (!oldDetailVOMap.containsKey(dataKey)) {
                    log.error("无此发货对接单行号:{}", dataKey);
                    throw new BusinessException("无此发货对接单行号:" + dataKey);
                }

                DeliveryDockingOrderDetailVO oldDetailVO = oldDetailVOMap.get(dataKey);
                DeliveryDockingOrderDetailDTO detailDTO = new DeliveryDockingOrderDetailDTO();
                BeanUtils.copyProperties(oldDetailVO, detailDTO);
                if (Objects.isNull(detailApiDTO.getActualDeliveryBoxQuantity())) {
                    throw new BusinessException("发货对接单详情数量字段为空");
                }
                if (Objects.isNull(detailApiDTO.getActualDeliveryQuantity())) {
                    throw new BusinessException("发货对接单实际发货数量字段为空");
                }
                detailDTO.setActualDeliveryBoxQuantity(detailApiDTO.getActualDeliveryBoxQuantity());
                detailDTO.setActualDeliveryQuantity(detailApiDTO.getActualDeliveryQuantity());
                updateOrderDetailList.add(detailDTO);
            }
        } else {
            throw new BusinessException("接口来源不合法");
        }
        doUpdate(deliveryDockingOrderDTO);
        deliveryDockingOrderDetailService.doUpdateBatch(updateOrderDetailList);
        log.info("{}发货单回传请求处理完成，更新发货对接单详情数据量:{}，发货对接单号:{}", deliveryDockingOrderApiDTO.getSourceSystem()
                ,updateOrderDetailList.size(), deliveryDockingOrderDTO.getDeliveryDockingNumber());

        return BaseResponse.success();
    }

    @Override
    public List<LabelValueFour<String>> carrierDataDropDown() {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<CarrierDataVO> carrierDataVOS = carrierDataService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(carrierDataVOS)) {
            List<LabelValueFour<String>> list = carrierDataVOS.stream()
                    .map(t -> new LabelValueFour<>(t.getCarrierName(), t.getTransportModeName(), t.getTransportPartnerNo(), t.getCarrierId().toString()))
                    .collect(Collectors.toList());
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValueThree<String>> getConveyanceByCode(String id) {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        params.put("id", id);
        List<CarrierDataVO> carrierDataVOS = carrierDataService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(carrierDataVOS)) {
            List<LabelValueThree<String>> list = carrierDataVOS.stream()
                    .map(t -> new LabelValueThree<>(t.getConveyanceName(), t.getTransportModeName(), t.getTransportPartnerNo()))
                    .collect(Collectors.toList());
            return removeDuplicatesThree(list);
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValue<String>> getTransitClauseByCode(String oemCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put(ENABLED, YesOrNoEnum.YES.getCode());
        params.put("oemCode", oemCode);
        List<OemVO> oemVOS = oemService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(oemVOS)) {
            List<LabelValue<String>> list = oemVOS.stream()
                    .map(t -> new LabelValue<>(t.getTransitClause(), t.getTransitClause()))
                    .collect(Collectors.toList());
            return removeDuplicates(list);
        }
        return Collections.emptyList();
    }

    @Override
    public DeliveryDockingOrderVO getMainDetailByDockingNumber(String deliveryDockingNumber) {
        if (StringUtils.isBlank(deliveryDockingNumber)) {
            return null;
        }
        Map<String, Object> params = new HashMap<>(2);
        params.put("deliveryDockingNumber", deliveryDockingNumber);
        List<DeliveryDockingOrderVO> dockingOrderVOList = deliveryDockingOrderDao.selectVOByParams(params);
        if (CollectionUtils.isNotEmpty(dockingOrderVOList)) {
        	DeliveryDockingOrderVO deliveryDockingOrderVO = dockingOrderVOList.get(0);
        	if(StringUtils.isEmpty(deliveryDockingOrderVO.getVehicleInfo())) {
        		JSONObject vehicleInfo = new JSONObject();
        		vehicleInfo.put("vehicleLength", deliveryDockingOrderVO.getVehicleLength());
        		vehicleInfo.put("vehicleNumber", deliveryDockingOrderVO.getVehicleNumber() == null ? 1
        				: deliveryDockingOrderVO.getVehicleNumber());
        		JSONArray ja = new JSONArray();
        		ja.add(vehicleInfo);
        		deliveryDockingOrderVO.setVehicleInfo(ja.toString());
        	}
            return deliveryDockingOrderVO;
        }
        return null;
    }

    private List<LabelValueThree<String>> removeDuplicatesThree(List<LabelValueThree<String>> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                lv -> Arrays.asList(lv.getValue1(), lv.getValue2(), lv.getValue3()),
                                lv -> lv,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_DOCKING_ORDER.getCode();
    }

    @Override
    public List<DeliveryDockingOrderVO> invocation(List<DeliveryDockingOrderVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

	@Override
	public void exportTemplate(HttpServletResponse response) throws Exception {
		response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition",
                "attachment;filename=" + URLEncoder.encode("发货对接单导入模板", "UTF-8") + ".xlsx");
        List<DeliveryDockingOrderExcelDTO> purchaseOrderDTOS = new ArrayList<>();
        DeliveryDockingOrderExcelDTO one = DeliveryDockingOrderExcelDTO.builder()
        		.oemCode("120002_001,400027_002")
        		.transferWarehouse("QRLH")
        		.customer("QR01")
        		.oemAddress("南汇区泥城镇兴隆千祥村330号（靠近南芦公路上海银行）")
        		.lineCode("FYSHQRLHCNCSSQY001")
        		.expectedArriveTime("2025-02-21 08:20")
        		.transportDirection("往返运输")
        		.transportMode("汽运")
        		.vehicleLength("9.6")
//        		.cabinetType("")
//        		.exitBarCode("")
//        		.overageReport("")
        		.build();
        purchaseOrderDTOS.add(one);
        DeliveryDockingOrderExcelDTO twoHead = DeliveryDockingOrderExcelDTO.builder()
        		.oemCode("*本厂编号")
        		.transferWarehouse("*发货数量")
        		.customer("*必发数量")
        		.oemAddress("*物料器具")
        		.lineCode("*箱数")
        		.expectedArriveTime("*发货时间")
        		.transportDirection("柜型")
        		.transportMode("毛重")
        		.vehicleLength("体积")
        		.cabinetType("发票号")
        		.exitBarCode("提单号")
        		.overageReport("备注")
        		.build();
        purchaseOrderDTOS.add(twoHead);
        Map<Integer, Short> rowBackColor = new HashMap<>();
        for (int i = 0; i < purchaseOrderDTOS.size(); i++) {
        	if(i == 1) {
        		rowBackColor.put(i + 1, IndexedColors.LEMON_CHIFFON.index);
        	}
        }
        EasyExcel.write(response.getOutputStream(), DeliveryDockingOrderExcelDTO.class)
        	.registerWriteHandler(new CustomCellWriteHandler(rowBackColor, null))
        	.sheet("发货对接单")
        	.doWrite(purchaseOrderDTOS);
	}

	@Override
	public int importExcel(List<DeliveryDockingOrderExcelDTO> deliveryDockingOrderList,
			String deliveryDockingNumber) {
		//进行数据解析校验，第一行数据为发货对接单数据，第二行为发货对接单详情表头，第三行及后面为发货对接单详情数据
		DeliveryDockingOrderDTO deliveryDockingOrderAdd = new DeliveryDockingOrderDTO();
		List<DeliveryDockingOrderDetailDTO> batchAddDetails = new ArrayList<>();
		//获取物料器具数据信息
		List<String> productCodes = new ArrayList<>();
		List<OemVO> oemVOS = new ArrayList<>();
		//校验导入模板
		checkExcelTemplate(deliveryDockingOrderList);

		for (int i = 0; i < deliveryDockingOrderList.size(); i++) {
			if(i == 0) {
				String oemCode = deliveryDockingOrderList.get(i).getOemCode();
				oemVOS = getOemList(oemCode);
			}
			if(i > 1) {
				DeliveryDockingOrderExcelDTO deliveryDockingOrder = deliveryDockingOrderList.get(i);
				String productCode = deliveryDockingOrder.getOemCode();
				if(!productCodes.contains(productCode)){
					productCodes.add(productCode);
				}
			}
		}
		if(CollectionUtils.isEmpty(productCodes)) {
			throw new BusinessException("发货对接单详情本厂编号必填");
		}
		String scenario = SystemHolder.getScenario();
        List<ProductBoxRelationVO> productBoxRelationVOS = newMdsFeign
        		.selectProductBoxRelationVOByProductCodeList(scenario, productCodes)
        		.stream().filter( e-> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
        Map<String, List<ProductBoxRelationVO>> productBoxRelationMap = productBoxRelationVOS.stream()
        		.collect(Collectors.groupingBy(ProductBoxRelationVO::getProductCode));

        //获取箱子类型
        List<CollectionValueVO> boxModes = ipsFeign.getByCollectionCode("BOX_TYPE");
    	Map<String, String> boxModeMap = boxModes.stream()
    			.collect(Collectors.toMap(CollectionValueVO::getValueMeaning,CollectionValueVO::getCollectionValue,(v1, v2) -> v1));
    	//运输方式
    	List<CollectionValueVO> transportModes = ipsFeign.getByCollectionCode("TRANSPORT_MODE");
    	Map<String, String> transportModeMap = transportModes.stream()
    			.collect(Collectors.toMap(CollectionValueVO::getValueMeaning,CollectionValueVO::getCollectionValue,(v1, v2) -> v1));
    	
    	List<String> errorMsgList = new ArrayList<>();
		for (int i = 0; i < deliveryDockingOrderList.size(); i++) {
			int rowNum = i + 2;
			DeliveryDockingOrderExcelDTO deliveryDockingOrder = deliveryDockingOrderList.get(i);
			if(i == 0) {
				//发货对接单
				deliveryDockingOrderAdd.setOemCode(deliveryDockingOrder.getOemCode());
				deliveryDockingOrderAdd.setTransferWarehouse(deliveryDockingOrder.getTransferWarehouse());
				deliveryDockingOrderAdd.setCustomer(deliveryDockingOrder.getCustomer());
				deliveryDockingOrderAdd.setOemAddress(deliveryDockingOrder.getOemAddress());
				deliveryDockingOrderAdd.setLineCode(deliveryDockingOrder.getLineCode());
				deliveryDockingOrderAdd.setExpectedArriveTime(DateUtils.stringToDate(deliveryDockingOrder.getExpectedArriveTime(), "yyyy-MM-dd HH:mm"));
				deliveryDockingOrderAdd.setTransportDirection(deliveryDockingOrder.getTransportDirection());
				deliveryDockingOrderAdd.setTransportMode(deliveryDockingOrder.getTransportMode());
				deliveryDockingOrderAdd.setVehicleLength(deliveryDockingOrder.getVehicleLength());
				deliveryDockingOrderAdd.setCabinetType(deliveryDockingOrder.getCabinetType());
				deliveryDockingOrderAdd.setExitBarCode(deliveryDockingOrder.getExitBarCode());
				deliveryDockingOrderAdd.setOverageReport(deliveryDockingOrder.getOverageReport());
				deliveryDockingOrderAdd.setStatus("OPEN");
				deliveryDockingOrderAdd.setVehicleNumber(1);

				//6.运输方式
				if(!transportModeMap.containsKey(deliveryDockingOrderAdd.getTransportMode())){
					throw new BusinessException("行数：" + rowNum + ";[运输方式]未维护");
				}
				deliveryDockingOrderAdd.setTransportMode(transportModeMap.get(deliveryDockingOrderAdd.getTransportMode()));
			}else if (i > 1) {
				if(StringUtils.isEmpty(deliveryDockingOrder.getTransferWarehouse())
						|| Integer.valueOf(deliveryDockingOrder.getTransferWarehouse()) == 0) {
					continue;
				}
				//发货对接单详情
				if(StringUtils.isEmpty(deliveryDockingOrder.getOemCode())) {
					throw new BusinessException("行数：" + rowNum + ";[本厂编号]必填");
				}else if(StringUtils.isEmpty(deliveryDockingOrder.getCustomer())) {
					throw new BusinessException("行数：" + rowNum + ";[必发数量]必填");
				}else if(StringUtils.isEmpty(deliveryDockingOrder.getOemAddress())) {
					throw new BusinessException("行数：" + rowNum + ";[物料器具]必填");
				}else if(StringUtils.isEmpty(deliveryDockingOrder.getLineCode())) {
					throw new BusinessException("行数：" + rowNum + ";[箱数]必填");
				}else if(StringUtils.isEmpty(deliveryDockingOrder.getExpectedArriveTime())) {
					throw new BusinessException("行数：" + rowNum + ";[发货时间]必填");
				}
				if(!boxModeMap.containsKey(deliveryDockingOrder.getOemAddress())) {
					throw new BusinessException("行数：" + rowNum + ";[物料器具]数据字典未维护");
				}
				//箱数数据格式判断
				if(!deliveryDockingOrder.getLineCode().matches("-?\\d+")) {
					throw new BusinessException("行数：" + rowNum + ";[箱数]必须为整数格式");
				}
				if(!productBoxRelationMap.containsKey(deliveryDockingOrder.getOemCode())) {
					errorMsgList.add("行数：" + rowNum + ";[产品编码]未维护成箱关系");
					continue;
				}
				//校验产品成品箱关系是否维护对应的物料器具
				String materialEquipment = boxModeMap.get(deliveryDockingOrder.getOemAddress());
				List<ProductBoxRelationVO> pbrList = productBoxRelationMap.get(deliveryDockingOrder.getOemCode())
						.stream().filter(e -> Objects.equals(e.getBoxType(), materialEquipment))
						.collect(Collectors.toList());
				if(CollectionUtils.isEmpty(pbrList)){
					errorMsgList.add("行数：" + rowNum + ";产品未维护【" + deliveryDockingOrder.getOemAddress() + "】成箱关系，请检查");
					continue;
				}
				pbrList = pbrList
						.stream().filter( e -> e.getPriority() != null && Objects.equals(e.getBoxType(), materialEquipment))
						.collect(Collectors.toList());
				if(CollectionUtils.isEmpty(pbrList)) {
					errorMsgList.add("行数：" + rowNum + ";[产品编码]未维护产品与成品箱关系优先级");
					continue;
	        	}
				pbrList = pbrList.stream().filter( e -> e.getStandardLoad() != null).collect(Collectors.toList());
	        	if(CollectionUtils.isEmpty(pbrList)) {
	        		errorMsgList.add("行数：" + rowNum + ";[产品编码]未维护产品与成品箱关系标准装载量");
	        		continue;
	        	}
				DeliveryDockingOrderDetailDTO detailAdd = new DeliveryDockingOrderDetailDTO();
				detailAdd.setProductCode(deliveryDockingOrder.getOemCode());
				detailAdd.setDeliveryQuantity(Integer.valueOf(deliveryDockingOrder.getTransferWarehouse()));
				detailAdd.setMustQuantity(Integer.valueOf(deliveryDockingOrder.getCustomer()));
				detailAdd.setMaterialEquipment(materialEquipment);
				detailAdd.setBoxNumber(deliveryDockingOrder.getLineCode());
				detailAdd.setDeliveryTime(StringUtils.isEmpty(deliveryDockingOrder.getExpectedArriveTime()) ? null
						: DateUtils.stringToDate(deliveryDockingOrder.getExpectedArriveTime(), "yyyy-MM-dd HH:mm"));
				detailAdd.setCabinetType(deliveryDockingOrder.getTransportDirection());
				detailAdd.setGrossWeight(
						StringUtils.isEmpty(deliveryDockingOrder.getTransportMode()) ? null
								: new BigDecimal(deliveryDockingOrder.getTransportMode()));
				detailAdd.setVolume(StringUtils.isEmpty(deliveryDockingOrder.getVehicleLength()) ? null
						: new BigDecimal(deliveryDockingOrder.getVehicleLength()));
				detailAdd.setInvoiceNumber(deliveryDockingOrder.getCabinetType());
				detailAdd.setBillLadingNumber(deliveryDockingOrder.getExitBarCode());
				detailAdd.setRemark(deliveryDockingOrder.getOverageReport());
				batchAddDetails.add(detailAdd);
			}
		}
		if(CollectionUtils.isNotEmpty(errorMsgList)) {
			throw new BusinessException(String.join("<br/>", errorMsgList));
		}
		BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"stockPointCode", rangeData,
    			"productCodes" , productCodes));
		Map<String, String> productTallyOrderModeMap = productInfoList.stream()
				.filter(e -> StringUtils.isNotEmpty(e.getTallyOrderMode()))
				.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getTallyOrderMode,(v1, v2) -> v1));


		//按照发货时间分组维护发货送货单号数据
		if(CollectionUtils.isNotEmpty(batchAddDetails)) {
			Map<String, List<DeliveryDockingOrderDetailDTO>> detailMap = batchAddDetails.stream()
					.collect(Collectors.groupingBy(e -> DateUtils.dateToString(e.getDeliveryTime(),
							DateUtils.COMMON_DATE_STR1)));
			Random random = new Random();
			List<DeliveryDockingOrderPO> batchAdd = new ArrayList<>();
			List<DeliveryDockingOrderDetailDTO> batchAddDetailList = new ArrayList<>();
			String currDeliveryDockingNumber = deliveryDockingNumber;
			List<String> allDeliveryDockingNumber = new ArrayList<>();
			for (List<DeliveryDockingOrderDetailDTO> detailList : detailMap.values()) {
				//校验理货单模式
				List<String> currentTallyOrderModes = new ArrayList<>();
				for (DeliveryDockingOrderDetailDTO e : detailList) {
					currentTallyOrderModes.add(productTallyOrderModeMap.getOrDefault(e.getProductCode(), "空"));
				}
				currentTallyOrderModes = new ArrayList<>(new HashSet<>(currentTallyOrderModes));
				if(currentTallyOrderModes.size() > 1 && currentTallyOrderModes.contains(ProductTallyOrderModeEnum.GRP.getCode())){
					throw new BusinessException("发货日期" +DateUtils.dateToString(detailList.get(0).getDeliveryTime(),
							DateUtils.COMMON_DATE_STR1)+ "存在产品编码理货单模式不一致");
				}
				Boolean grpFlag = false;
				if(currentTallyOrderModes.size() == 1 && "空".equals(currentTallyOrderModes.get(0))) {
					//获取对应主机厂的理货单模式
					if(oemVOS.size() > 1) {
			        	for (OemVO oemVO : oemVOS) {
			            	if(OemTallyOrderModeEnum.GRP.getCode().equals(oemVO.getTallyOrderMode())) {
			    				throw new BusinessException("主机厂" + oemVO.getOemCode() + "存在理货单模式为GRP");
			    			}
			    		}
			        }
					if(OemTallyOrderModeEnum.GRP.getCode().equals(oemVOS.get(0).getTallyOrderMode())) {
						grpFlag = true;
					}
				}
				//如果物料没有维护理货单模式，则取主机厂的理货单模式，如果维护了，直接取物料的理货单模式
				if(grpFlag || ProductTallyOrderModeEnum.GRP.getCode().equals(currentTallyOrderModes.get(0))) {
					//如果是GRP模式，则不能维护相同产品编码详情
					List<String> productCodeList = detailList.stream().map(DeliveryDockingOrderDetailDTO::getProductCode)
							.distinct().collect(Collectors.toList());
		        	if(productCodeList.size() < detailList.size()) {
		        		throw new BusinessException("当前理货单模式为GRP,发货日期" +DateUtils.dateToString(detailList.get(0).getDeliveryTime(),
								DateUtils.COMMON_DATE_STR1)+ "存在相同产品的发货单详情!");
		        	}
				}
				DeliveryDockingOrderPO deliveryDockingOrderPO = DeliveryDockingOrderConvertor.INSTANCE.dto2Po(deliveryDockingOrderAdd);
				BasePOUtils.insertFiller(deliveryDockingOrderPO);
				deliveryDockingOrderPO.setDeliveryTime(detailList.get(0).getDeliveryTime());
				//处理发货单号
				if(detailMap.keySet().size() > 1) {
					while(!allDeliveryDockingNumber.contains(currDeliveryDockingNumber)){
						String randomNumber = random.nextInt(900) + 100 + "";
						currDeliveryDockingNumber = "DN" + DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4) + randomNumber;
						allDeliveryDockingNumber.add(currDeliveryDockingNumber);
					}
				}
				int i = 0;
				for (DeliveryDockingOrderDetailDTO detail : detailList) {
					detail.setDeliveryDockingNumber(currDeliveryDockingNumber);
					detail.setDeliveryDockingLineNumber(i+"");
					i++;
				}
				deliveryDockingOrderPO.setDeliveryDockingNumber(currDeliveryDockingNumber);
				batchAdd.add(deliveryDockingOrderPO);
				batchAddDetailList.addAll(detailList);
				currDeliveryDockingNumber = deliveryDockingNumber;
			}
			BasePOUtils.insertBatchFiller(batchAdd);
			deliveryDockingOrderDao.insertBatchWithPrimaryKey(batchAdd);
			BasePOUtils.insertBatchFiller(batchAddDetailList);
			deliveryDockingOrderDetailService.doCreateBatch(batchAddDetailList);
			return detailMap.keySet().size();
		}else {
			throw new BusinessException("未维护发货数量");
		}
	}

	/**
	 * 校验导入模板
	 * @param deliveryDockingOrderList
	 */
	private void checkExcelTemplate(List<DeliveryDockingOrderExcelDTO> deliveryDockingOrderList) {
		DeliveryDockingOrderExcelDTO excelHead = deliveryDockingOrderList.get(1);
		if(org.apache.commons.lang3.StringUtils.isAnyEmpty(excelHead.getOemCode(),
				excelHead.getTransferWarehouse(), excelHead.getCustomer(), excelHead.getOemAddress(),
				excelHead.getLineCode(), excelHead.getExpectedArriveTime(), excelHead.getTransportDirection(),
				excelHead.getTransportMode(), excelHead.getVehicleLength())
				|| !excelHead.getOemCode().contains("本厂编号")
				|| !excelHead.getTransferWarehouse().contains("发货数量")
				|| !excelHead.getCustomer().contains("必发数量")
				|| !excelHead.getOemAddress().contains("物料器具")
				|| !excelHead.getLineCode().contains("箱数")
				|| !excelHead.getExpectedArriveTime().contains("发货时间")
				|| !excelHead.getTransportDirection().contains("柜型")
				|| !excelHead.getTransportMode().contains("毛重")
				|| !excelHead.getVehicleLength().contains("体积")
				|| !excelHead.getCabinetType().contains("发票号")
				|| !excelHead.getExitBarCode().contains("提单号")
				|| !excelHead.getOverageReport().contains("备注")) {
			throw new BusinessException("请检查导入模板!");
		}
		DeliveryDockingOrderExcelDTO excelDto = deliveryDockingOrderList.get(0);
		if(StringUtils.isEmpty(excelDto.getOemCode())) {
			throw new BusinessException("行数：" + 2 + ";[主机厂编码]必填");
		}else if(StringUtils.isEmpty(excelDto.getTransferWarehouse())) {
			throw new BusinessException("行数：" + 2 + ";[中转库（子库存）]必填");
		}else if(StringUtils.isEmpty(excelDto.getCustomer())) {
			throw new BusinessException("行数：" + 2 + ";[客户（库位）]必填");
		}else if(StringUtils.isEmpty(excelDto.getOemAddress())) {
			throw new BusinessException("行数：" + 2 + ";[主机厂地址]必填");
		}else if(StringUtils.isEmpty(excelDto.getLineCode())) {
			throw new BusinessException("行数：" + 2 + ";[线路编码]必填");
		}else if(StringUtils.isEmpty(excelDto.getExpectedArriveTime())) {
			throw new BusinessException("行数：" + 2 + ";[收货日期]必填");
		}else if(StringUtils.isEmpty(excelDto.getTransportDirection())) {
			throw new BusinessException("行数：" + 2 + ";[运输方向]必填");
		}else if(StringUtils.isEmpty(excelDto.getTransportMode())) {
			throw new BusinessException("行数：" + 2 + ";[运输方式]必填");
		}else if(StringUtils.isEmpty(excelDto.getVehicleLength())) {
			throw new BusinessException("行数：" + 2 + ";[车长]必填");
		}
	}

	private List<OemVO> getOemList(String oemCode) {
		List<String> oemCodeList = new ArrayList<>(Arrays.asList(oemCode.split(",")));
		HashMap<String, Object> oemMap = MapUtil.newHashMap();
		oemMap.put("oemCodes", new ArrayList<>(Arrays.asList(oemCode.split(","))));
		oemMap.put(ENABLED, YesOrNoEnum.YES.getCode());
		List<OemVO> oemVOS = oemService.selectByParams(oemMap);
		if (oemVOS.isEmpty()) {
			log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", oemCodeList.toString());
		    throw new BusinessException("该数据的主机厂编码是" + oemCodeList.toString() + "，但是在主机厂表中匹配不到数据。");
		}else {
			List<String> currOemCodes = oemVOS.stream().map(OemVO::getOemCode).collect(Collectors.toList());
			List<String> requestOemCodes = new ArrayList<>(Arrays.asList(oemCode.split(",")));
			requestOemCodes.removeAll(currOemCodes);
			if(CollectionUtils.isNotEmpty(requestOemCodes)) {
				log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", requestOemCodes.toString());
		        throw new BusinessException("该数据的主机厂编码是" + requestOemCodes.toString() + "，但是在主机厂表中匹配不到数据。");
			}
		}
		return oemVOS;
	}

	@Override
	public Map<String,List<LabelValueThree<String>>> getBoxTypeDropDown(String productCode) {
		Map<String,List<LabelValueThree<String>>> resultMap = new HashMap<>();
		List<String> productCodes = Arrays.asList(productCode.split(","));
        List<ProductBoxRelationVO> productBoxRelationVOS =
                newMdsFeign.selectProductBoxRelationVOByProductCodeList(SystemHolder.getScenario(), productCodes);
        productBoxRelationVOS = productBoxRelationVOS.stream()
        		.filter( e-> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
        Map<String, List<ProductBoxRelationVO>> productBoxRelationMap = productBoxRelationVOS.stream()
        		.collect(Collectors.groupingBy(ProductBoxRelationVO::getProductCode));
		for (String pc : productCodes) {
			List<ProductBoxRelationVO> pbrList = productBoxRelationMap.get(pc);
			if (CollectionUtils.isEmpty(pbrList)) {
				throw new BusinessException("产品 " + pc + " 未维护产品与成品箱关系");
			}
			pbrList = pbrList.stream().filter( e -> e.getPriority() != null).collect(Collectors.toList());
        	if(CollectionUtils.isEmpty(pbrList)) {
        		throw new BusinessException("产品 " + pc + " 未维护产品与成品箱关系优先级");
        	}
        	pbrList = pbrList.stream().filter( e -> e.getStandardLoad() != null).collect(Collectors.toList());
        	if(CollectionUtils.isEmpty(pbrList)) {
        		throw new BusinessException("产品 " + pc + " 未维护产品与成品箱关系标准装载量");
        	}
        	pbrList.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
    		List<LabelValueThree<String>> list = pbrList.stream()
                    .map(x -> new LabelValueThree<>(x.getBoxType() + "(" + x.getBoxCode() + "&" + x.getStandardLoad() +")", x.getBoxType(), x.getBoxId()))
                    .collect(Collectors.toList());
    		resultMap.put(pc, list);
		}
		return resultMap;
	}

	private List<ProductBoxRelationVO> getBoxTypeListByProductCode(String productCode) {
        List<ProductBoxRelationVO> productBoxRelationVOS =
                newMdsFeign.selectProductBoxRelationVOByProductCodeList(SystemHolder.getScenario(),
                 Collections.singletonList(productCode));
        if (CollectionUtils.isNotEmpty(productBoxRelationVOS)) {
        	productBoxRelationVOS = productBoxRelationVOS.stream().filter( e -> e.getPriority() != null).collect(Collectors.toList());
        	if(CollectionUtils.isEmpty(productBoxRelationVOS)) {
        		throw new BusinessException("产品 " + productCode + " 未维护产品与成品箱关系优先级");
        	}
        	productBoxRelationVOS = productBoxRelationVOS.stream().filter( e -> e.getStandardLoad() != null).collect(Collectors.toList());
        	if(CollectionUtils.isEmpty(productBoxRelationVOS)) {
        		throw new BusinessException("产品 " + productCode + " 未维护产品与成品箱关系标准装载量");
        	}
        	productBoxRelationVOS.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
            return productBoxRelationVOS;
        }
        throw new BusinessException("产品 " + productCode + " 未维护产品与成品箱关系");
	}
}
