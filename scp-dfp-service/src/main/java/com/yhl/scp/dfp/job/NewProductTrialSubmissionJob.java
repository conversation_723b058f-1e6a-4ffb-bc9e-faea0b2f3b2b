package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.common.enums.SubmissionTypeEnum;
import com.yhl.scp.dfp.newProduct.service.NewProductTrialSubmissionService;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * description:获取OA新品试制卡
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/15
 */
@Component
@Slf4j
public class NewProductTrialSubmissionJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private NewProductTrialSubmissionService newProductTrialSubmissionService;

    @XxlJob("newProductTrialSubmissionJob")
    public ReturnT<String> newProductTrialSubmissionJob() {
        XxlJobHelper.log("开始运行OA新品试制卡定时任务。");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        //查询OA公司
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    if (StringUtils.isEmpty(scenario.getAlternativeColumn())) {
                        throw new BusinessException("{}租户下没有对应的组织ID", scenario.getDataBaseName());
                    }
                    List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectAllStockPoint(scenario.getDataBaseName());
                    Optional<NewStockPointVO> first = newStockPointVOS.stream().filter(x -> scenario.getAlternativeColumn().equals(x.getOrganizeId())).findFirst();
                    if (first.isPresent()) {
                        newProductTrialSubmissionService.syncSubmission(null, null, SubmissionTypeEnum.API.getCode(), first.get().getCompanyName(), scenario.getTenantId(), scenario.getDataBaseName());
                    }
                }
            } catch (Exception e) {
                XxlJobHelper.log("oa新品试制卡定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("OA新品试制卡定时任务结束。");
        return ReturnT.SUCCESS;
    }
}
