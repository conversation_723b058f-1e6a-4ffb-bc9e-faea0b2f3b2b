package com.yhl.scp.mps.highValueMaterials.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.highValueMaterials.convertor.MpsHighValueMaterialsConvertor;
import com.yhl.scp.mps.highValueMaterials.domain.entity.MpsHighValueMaterialsDO;
import com.yhl.scp.mps.highValueMaterials.domain.service.MpsHighValueMaterialsDomainService;
import com.yhl.scp.mps.highValueMaterials.dto.MpsHighValueMaterialsDTO;
import com.yhl.scp.mps.highValueMaterials.infrastructure.dao.MpsHighValueMaterialsDao;
import com.yhl.scp.mps.highValueMaterials.infrastructure.po.MpsHighValueMaterialsPO;
import com.yhl.scp.mps.highValueMaterials.service.MpsHighValueMaterialsService;
import com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MpsHighValueMaterialsServiceImpl extends AbstractService implements MpsHighValueMaterialsService {

    @Resource
    private MpsHighValueMaterialsDao mpsHighValueMaterialsDao;

    @Resource
    private MpsHighValueMaterialsDomainService mpsHighValueMaterialsDomainService;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MpsHighValueMaterialsDTO mpsHighValueMaterialsDTO) {
        // 0.数据转换
        MpsHighValueMaterialsDO mpsHighValueMaterialsDO = MpsHighValueMaterialsConvertor.INSTANCE.dto2Do(mpsHighValueMaterialsDTO);
        MpsHighValueMaterialsPO mpsHighValueMaterialsPO = MpsHighValueMaterialsConvertor.INSTANCE.dto2Po(mpsHighValueMaterialsDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mpsHighValueMaterialsDomainService.validation(mpsHighValueMaterialsDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mpsHighValueMaterialsPO);
        mpsHighValueMaterialsDao.insert(mpsHighValueMaterialsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> createNewData(String scenario, List<MpsHighValueMaterialsDTO> mpsHighValueMaterialsDTOs) {
        //获取库存点编码（组织编码）
        String stockPointCode = mpsHighValueMaterialsDTOs.get(0).getStockPointCode();
        //通过获取库存点编码获取旧数据
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("stockPointCode", stockPointCode);
        List<MpsHighValueMaterialsPO> mpsHighValueMaterialsPOS1 = mpsHighValueMaterialsDao.selectByParams(map);
        //先删除旧数据
        if (!mpsHighValueMaterialsPOS1.isEmpty()) {
            List<String> ids = mpsHighValueMaterialsPOS1.stream().map(x -> x.getId()).collect(Collectors.toList());
            doDelete(ids);
        }
        //通过库存点编码寻找库存点名称
        HashMap<String, Object> stockPointMap = MapUtil.newHashMap();
        stockPointMap.put("stockPointCode", stockPointCode);
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario, stockPointMap);
        String stockPointName;
        if (newStockPointVOS.isEmpty()) {
            stockPointName = null;
        } else {
            stockPointName = newStockPointVOS.get(0).getStockPointName();
        }
        //获取全部物料
        Set<String> collect = mpsHighValueMaterialsDTOs.stream().map(MpsHighValueMaterialsDTO::getProductCode).collect(Collectors.toSet());
        Map<String, Object> productMap = MapUtil.newHashMap();
        productMap.put("stockPointCode", stockPointCode);
        productMap.put("productCodes", collect);
        List<NewProductStockPointVO> productStockVOs = mdsFeign.selectProductStockPointByParams(scenario, productMap);
        //数据塞值
        mpsHighValueMaterialsDTOs.stream().forEach(x -> {
            //塞值库存点名称
            x.setStockPointName(stockPointName);
            //塞入物料编码
            Optional<NewProductStockPointVO> first = productStockVOs.stream().filter(v -> v.getProductCode().equals(x.getProductCode())).findFirst();
            if (first.isPresent()) {
                x.setProductName(first.get().getProductName());
            }
        });
        //获取物料数据
        doCreateBatch(mpsHighValueMaterialsDTOs);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MpsHighValueMaterialsDTO mpsHighValueMaterialsDTO) {
        // 0.数据转换
        MpsHighValueMaterialsDO mpsHighValueMaterialsDO = MpsHighValueMaterialsConvertor.INSTANCE.dto2Do(mpsHighValueMaterialsDTO);
        MpsHighValueMaterialsPO mpsHighValueMaterialsPO = MpsHighValueMaterialsConvertor.INSTANCE.dto2Po(mpsHighValueMaterialsDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mpsHighValueMaterialsDomainService.validation(mpsHighValueMaterialsDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mpsHighValueMaterialsPO);
        mpsHighValueMaterialsDao.update(mpsHighValueMaterialsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MpsHighValueMaterialsDTO> list) {
        List<MpsHighValueMaterialsPO> newList = MpsHighValueMaterialsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mpsHighValueMaterialsDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MpsHighValueMaterialsDTO> list) {
        List<MpsHighValueMaterialsPO> newList = MpsHighValueMaterialsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mpsHighValueMaterialsDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mpsHighValueMaterialsDao.deleteBatch(idList);
        }
        return mpsHighValueMaterialsDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MpsHighValueMaterialsVO selectByPrimaryKey(String id) {
        MpsHighValueMaterialsPO po = mpsHighValueMaterialsDao.selectByPrimaryKey(id);
        return MpsHighValueMaterialsConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "HIGH_VALUE_MATERIALS")
    public List<MpsHighValueMaterialsVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "HIGH_VALUE_MATERIALS")
    public List<MpsHighValueMaterialsVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MpsHighValueMaterialsVO> dataList = mpsHighValueMaterialsDao.selectByCondition(sortParam, queryCriteriaParam);
        MpsHighValueMaterialsServiceImpl target = SpringBeanUtils.getBean(MpsHighValueMaterialsServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MpsHighValueMaterialsVO> selectByParams(Map<String, Object> params) {
        List<MpsHighValueMaterialsPO> list = mpsHighValueMaterialsDao.selectByParams(params);
        return MpsHighValueMaterialsConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MpsHighValueMaterialsVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.HIGH_VALUE_MATERIALS.getCode();
    }

    @Override
    public List<MpsHighValueMaterialsVO> invocation(List<MpsHighValueMaterialsVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> syncData(String scenario, String tenantCode) {
        try {
            log.info("开始同步高价值物料");
            HashMap<String, Object> map = MapUtil.newHashMap();
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantId();
                scenario = SystemHolder.getScenario();
            }
            List<NewStockPointVO> newStockPointVOS = mdsFeign.selectAllStockPoint(scenario);
            String purchaseCode = null;
            List<String> sonOrganizations = new ArrayList<>();
            //获取采购组织和生产组织
            for (NewStockPointVO vo : newStockPointVOS) {
                String interfaceFlag = vo.getInterfaceFlag();
                if (StringUtils.isNotEmpty(interfaceFlag)) {
                    String[] split = interfaceFlag.split(",");
                    boolean flag = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.HIGH_VALUE_PRODUCT.getCode().equals(x));
                    if (flag) {
                        if (StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode().equals(vo.getOrganizeType())) {
                            purchaseCode = vo.getStockPointCode();
                        } else if (StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(vo.getOrganizeType())) {
                            sonOrganizations.add(vo.getStockPointCode());
                        }
                    }
                }
            }
            //调用同步接口
            if (StringUtils.isNotEmpty(purchaseCode) && !sonOrganizations.isEmpty()){
                map.put("orgCode", purchaseCode);
                map.put("bomCodes",sonOrganizations.toString().substring(1,sonOrganizations.toString().length()-1));
                map.put("scenario",scenario);
                //获取ERP高价值物料数据
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.ERP.getCode(),
                        ApiCategoryEnum.HIGH_VALUE_PRODUCT.getCode(), map);
                return BaseResponse.success("同步操作完成");
            }else{
                return BaseResponse.error("没有找到高价值物料接口对应的采购组织和或生产组织。");
            }
        } catch (Exception e) {
            log.error("同步高价值物料报错,{}", e.getMessage());
            throw new BusinessException("同步高价值物料报错", e.getMessage());
        }
    }
}
