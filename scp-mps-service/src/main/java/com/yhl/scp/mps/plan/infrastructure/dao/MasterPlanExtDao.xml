<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao">
    <resultMap id="BaseResultMap" extends="com.yhl.scp.sds.order.infrastructure.dao.OperationDao.BaseResultMap"
               type="com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>
    <resultMap id="VOResultMap" extends="com.yhl.scp.sds.order.infrastructure.dao.OperationDao.VOResultMap"
               type="com.yhl.scp.sds.extension.order.vo.OperationVO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
    </resultMap>
    <sql id="Base_Column_List">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.sds.order.infrastructure.dao.OperationDao.Base_Column_List"/>
        <!--,new_column-->
    </sql>

    <resultMap id="SplitRoutingStepResourceMap"
               type="com.yhl.scp.mps.domain.dispatch.model.dto.SplitRoutingStepResourceDTO">
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="sequence_no" jdbcType="INTEGER" property="stepSequenceNo"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="standard_step_type" jdbcType="VARCHAR" property="standardStepType"/>
        <result column="yield" jdbcType="DECIMAL" property="yield"/>
        <collection property="physicalResources"
                    ofType="com.yhl.scp.mps.domain.dispatch.model.dto.SplitResourceDTO">
            <result column="physical_resource_id" jdbcType="VARCHAR" property="id"/>
            <result column="physical_resource_code" jdbcType="VARCHAR" property="physicalResourceCode"/>
            <result column="priority" jdbcType="INTEGER" property="priority"/>
            <result column="unit_production_time" jdbcType="DECIMAL" property="unitProductionTime"/>
        </collection>
    </resultMap>
    <resultMap id="SafetyStockLeveVOResultMap" type="com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="stock_code" jdbcType="VARCHAR" property="stockCode"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="oem_risk_level" jdbcType="VARCHAR" property="oemRiskLevel"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="material_risk_level" jdbcType="VARCHAR" property="materialRiskLevel"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="min_stock_day" jdbcType="DECIMAL" property="minStockDay"/>
        <result column="standard_stock_day" jdbcType="DECIMAL" property="standardStockDay"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_operation
        where id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        or id in (
        select parent_id
        from sds_ord_operation
        where id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectByKeyStep" resultMap="BaseResultMap">
        SELECT soo.*
        FROM sds_ord_operation soo,
        mds_rou_standard_step mrss
        WHERE soo.standard_step_id = mrss.id
        AND mrss.key_step = 'YES'
        AND soo.parent_id IS NULL
        <if test="orderIds != null and orderIds.size > 0">
            and soo.order_id in
            <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <delete id="deletePlanUnitByOrderId">
        delete
        from sds_ord_plan_unit where order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByOperationIds">
        delete
        from sds_ord_operation_extend where operation_id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="doClearFulfillment">
        delete
        from sds_peg_fulfillment
    </delete>

    <!-- 查询工序供应数据 -->
    <resultMap id="SupplyVOResultMap" extends="com.yhl.scp.sds.pegging.infrastructure.dao.SupplyBasicDao.VOResultMap"
               type="com.yhl.scp.sds.extension.pegging.vo.SupplyVO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
    </resultMap>

    <!-- 查询工序供应数据 -->
    <resultMap id="DemandVOResultMap" extends="com.yhl.scp.sds.pegging.infrastructure.dao.DemandBasicDao.VOResultMap"
               type="com.yhl.scp.sds.extension.pegging.vo.DemandVO">
    </resultMap>

    <resultMap id="WindFenceProductFixtureRelationVO"
               extends="com.yhl.scp.mps.fixtureRelation.infrastructure.dao.ProductFixtureRelationDao.BaseResultMap"
               type="com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO">
    </resultMap>

    <sql id="WindFenceProductFixtureRelationColumnList">
        <include refid="com.yhl.scp.mps.fixtureRelation.infrastructure.dao.ProductFixtureRelationDao.Base_Column_List"/>
    </sql>

    <select id="selectSupplyVOByOperationIds" resultMap="SupplyVOResultMap">
        SELECT `s`.`id` AS `id`,
        `s`.`supply_code` AS `supply_code`,
        `s`.`product_stock_point_id` AS `product_stock_point_id`,
        `s`.`product_id` AS `product_id`,
        `s`.`stock_point_id` AS `stock_point_id`,
        `s`.`supply_time` AS `supply_time`,
        `s`.`quantity` AS `quantity`,
        `s`.`unfulfilled_quantity` AS `unfulfilled_quantity`,
        `s`.`supply_order_id` AS `supply_order_id`,
        `s`.`operation_output_id` AS `operation_output_id`,
        `s`.`supply_type` AS `supply_type`,
        `s`.`fulfillment_status` AS `fulfillment_status`,
        `s`.`counting_unit_id` AS `counting_unit_id`,
        `s`.`appoint_customer_id` AS `appoint_customer_id`,
        `s`.`appoint_demand_type` AS `appoint_demand_type`,
        `s`.`appoint_demand_order_id` AS `appoint_demand_order_id`,
        `s`.`fixed` AS `fixed`,
        `s`.`remark` AS `remark`,
        `s`.`enabled` AS `enabled`,
        `s`.`creator` AS `creator`,
        `s`.`create_time` AS `create_time`,
        `s`.`modifier` AS `modifier`,
        `s`.`modify_time` AS `modify_time`,
        `p`.`product_code` AS `product_code`,
        `p`.`product_name` AS `product_name`,
        `sp`.`stock_point_code` AS `stock_point_code`,
        `sp`.`stock_point_name` AS `stock_point_name`,
        `wo`.`order_no` AS `order_no`,
        `pu`.`plan_unit_no` AS `plan_unit_no`,
        `pu`.`id` AS `plan_unit_id`,
        <!--			`rso`.`standard_step_code` AS `routing_step_code`,-->
        `rso`.`standard_step_name` AS `standard_step_name`,
        oo.operation_id AS `operation_id`
        FROM (((((((
        `sds_peg_supply` `s`
        LEFT JOIN `mds_product_stock_point` `p` ON ((
        `p`.`id` = `s`.`product_id`
        )))
        LEFT JOIN `mds_stock_point` `sp` ON ((
        `sp`.`stock_point_code` = `p`.`stock_point_code`
        )))
        LEFT JOIN `sds_ord_work_order` `wo` ON ((
        `wo`.`id` = `s`.`supply_order_id`
        )))
        LEFT JOIN `sds_ord_operation_output` `oo` ON ((
        `oo`.`id` = `s`.`operation_output_id`
        )))
        LEFT JOIN `sds_ord_plan_unit` `pu` ON ((
        `pu`.`id` = `oo`.`plan_unit_id`
        )))
        LEFT JOIN `mds_rou_routing_step` `rs` ON ((
        `rs`.`id` = `oo`.`routing_step_id`
        )))
        LEFT JOIN `mds_rou_standard_step` `rso` ON ((
        `rso`.`id` = `rs`.`standard_step_id`
        )))
        where oo.operation_id in
        <foreach collection="params.operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="WorkOrderVOResultMap" extends="com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao.VOResultMap"
               type="com.yhl.scp.sds.extension.order.vo.WorkOrderVO">
    </resultMap>
    <select id="selectDelayAdjustWorkOrder" resultMap="WorkOrderVOResultMap">
        SELECT wo.*
        FROM sds_ord_work_order wo,
             mds_product_stock_point mpsp
        WHERE wo.plan_status = 'PLANNED'
          AND wo.demand_category = 'OUTPUT_DEMAND'
          AND wo.product_id = mpsp.id
          AND EXISTS (SELECT 1
                      FROM fdp_delivery_plan_published fdpp,
                           fdp_delivery_plan fdp
                      WHERE mpsp.product_code = fdpp.product_code
                        AND fdpp.delivery_plan_data_id = fdp.id
                        AND fdp.supply_type = 'MTS')
          AND EXISTS (SELECT 1
                      FROM fdp_part_risk_level fprl
                      WHERE mpsp.product_code = fprl.product_code
                        AND fprl.material_risk_level = '低')
    </select>
    <resultMap id="FeedbackProductionResultMap"
               extends="com.yhl.scp.sds.feedback.infrastructure.dao.FeedbackProductionDao.BaseResultMap"
               type="com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO">
        <result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
        <result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
    </resultMap>
    <select id="selectLockFeedbackList" resultMap="FeedbackProductionResultMap">
        SELECT cc.id AS id,
        cc.task_type AS task_type,
        cc.reporting_type AS reporting_type,
        cc.cleanup_type AS cleanup_type,
        cc.routing_step_id AS routing_step_id,
        cc.work_order_id AS work_order_id,
        cc.operation_id AS operation_id,
        cc.physical_resource_id AS physical_resource_id,
        cc.reporting_time AS reporting_time,
        cc.reporting_status AS reporting_status,
        cc.reporting_quantity AS reporting_quantity,
        cc.reporting_scrap AS reporting_scrap,
        cc.reporting_progress AS reporting_progress,
        cc.reporting_batch_no AS reporting_batch_no,
        cc.processing_type AS processing_type,
        IFNULL(cc.start_time, ope.start_time) AS start_time,
        IFNULL(cc.end_time, ope.end_time) AS end_time,
        cc.counting_unit_id AS counting_unit_id,
        cc.parent_operation_id AS parent_operation_id,
        cc.remark AS remark,
        cc.enabled AS enabled,
        cc.creator AS creator,
        cc.create_time AS create_time,
        cc.modifier AS modifier,
        cc.modify_time AS modify_time,
        ms.standard_step_type AS standard_step_code,
        ms.standard_step_name AS standard_step_name
        FROM sds_ord_operation ope
        JOIN sds_fee_feedback_production cc ON ope.order_id = cc.work_order_id
        join mds_rou_standard_step ms on ope.standard_step_id = ms.id
        AND ope.id = cc.operation_id
        AND cc.whether_feed_back = 'NO'
        <if test="permissionResourceIds != null and permissionResourceIds.size() > 0">
            AND cc.physical_resource_id in
            <foreach collection="permissionResourceIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="startTime != null">
            AND ope.end_time &gt; #{startTime}
        </if>
    </select>
    <select id="selectByIds2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_operation_published where id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="WorkOrdewrResultMap"
               extends="com.yhl.scp.sds.order.infrastructure.dao.WorkOrderBasicDao.BaseResultMap"
               type="com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO">
        <!--项目自定义字段,参考示例-->
        <!--<result column="new_column" jdbcType="TIMESTAMP" property="newColumn"/>-->
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
    </resultMap>

    <sql id="Order_Column_List">
        <!--项目自定义字段,参考示例,逗号不能缺失-->
        <include refid="com.yhl.scp.sds.order.infrastructure.dao.WorkOrderBasicDao.Base_Column_List"/>
        <!--,new_column-->
        order_type
    </sql>

    <select id="selectPublishWorkOrder" resultMap="WorkOrdewrResultMap">
        select
        <include refid="Order_Column_List"/>
        from sds_ord_work_order_published
    </select>
    <select id="selectParentWorkOrder" resultType="java.lang.String">
        select id
        from sds_ord_work_order where parent_id in
        <foreach collection="parentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectDemandVOByOperationIds" resultMap="DemandVOResultMap">
        select
        `d`.`id` AS `id`,
        `d`.`demand_code` AS `demand_code`,
        `d`.`demand_time` AS `demand_time`,
        `d`.`product_stock_point_id` AS `product_stock_point_id`,
        `d`.`product_id` AS `product_id`,
        `d`.`stock_point_id` AS `stock_point_id`,
        `d`.`quantity` AS `quantity`,
        `d`.`unfulfilled_quantity` AS `unfulfilled_quantity`,
        `d`.`customer_id` AS `customer_id`,
        `d`.`demand_order_id` AS `demand_order_id`,
        `d`.`operation_input_id` AS `operation_input_id`,
        `d`.`demand_type` AS `demand_type`,
        `d`.`fulfillment_status` AS `fulfillment_status`,
        `d`.`counting_unit_id` AS `counting_unit_id`,
        `d`.`remark` AS `remark`,
        `d`.`enabled` AS `enabled`,
        `d`.`creator` AS `creator`,
        `d`.`create_time` AS `create_time`,
        `d`.`modifier` AS `modifier`,
        `d`.`modify_time` AS `modify_time`,
        `d`.`finished_product_code` AS `finished_product_code`,
        `p`.`product_code` AS `product_code`,
        `p`.`product_name` AS `product_name`,
        `sp`.`stock_point_code` AS `stock_point_code`,
        `sp`.`stock_point_name` AS `stock_point_name`,
        `wo`.`order_no` AS `order_no`,
        `o`.`operation_code` AS `operation_code`,
        `pu`.`plan_unit_no` AS `plan_unit_no`,
        `rs`.`sequence_no` AS `sequence_number`,
        `oi`.`routing_step_id` AS `routing_step_id`
        from
        `sds_peg_demand` `d`
        left join `mds_product_stock_point` `p` on `p`.`id` = `d`.`product_id`
        left join `mds_stock_point` `sp` on `sp`.`stock_point_code` = `p`.`stock_point_code`
        left join `sds_ord_work_order` `wo` on `wo`.`id` = `d`.`demand_order_id`
        left join `sds_ord_operation_input` `oi` on `oi`.`id` = `d`.`operation_input_id`
        left join `sds_ord_operation` `o` on `o`.`id` = `oi`.`operation_id`
        left join `sds_ord_plan_unit` `pu` on `pu`.`id` = `oi`.`plan_unit_id`
        left join `mds_rou_routing_step` `rs` on `rs`.`id` = `oi`.`routing_step_id`
        where `o`.`id` in
        <foreach collection="params.operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectWorkOrderYield" resultType="com.yhl.scp.mps.plan.vo.WorkOrderYieldVO">
        SELECT so.order_no as workOrderNumber,
        st.routing_id AS routingId,
        st.id AS routingStepId,
        st.sequence_no AS sequenceNo,
        st.yield AS yield
        FROM sds_ord_work_order so
        LEFT JOIN mds_rou_routing ro ON so.routing_id = ro.id
        LEFT JOIN mds_rou_routing_step st ON ro.id = st.routing_id
        WHERE
        so.order_no IN
        <foreach collection="orderNoList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSemiStep" resultType="com.yhl.scp.mds.extension.routing.vo.RoutingStepVO">
        SELECT rs.routing_id         as routingId,
               rs.sequence_no        as sequenceNo,
               rs.yield              as yield,
               ss.standard_step_type as standardStepCode,
               ss.standard_step_name as standardStepName
        FROM mds_rou_routing ro
                 LEFT JOIN mds_rou_routing_step rs ON ro.id = rs.routing_id
                 LEFT JOIN mds_rou_standard_step ss on rs.standard_step_id = ss.id
        WHERE ro.product_id IN (SELECT psp.id
                                FROM mds_rou_routing rt
                                         LEFT JOIN mds_rou_routing_step_input re ON rt.id = re.routing_id
                                         LEFT JOIN mds_product_stock_point psp ON psp.id = re.input_product_id
                                WHERE psp.product_type = 'SA'
                                    <if test="productCode != null">
                                        and rt.product_code = #{productCode,jdbcType=VARCHAR}
                                    </if>
                                    <if test="productId != null">
                                        and rt.product_id = #{productId,jdbcType=VARCHAR}
                                    </if>
                                )
    </select>

    <select id="selectFeedBackList" resultMap="FeedbackProductionResultMap">
        select work_order_id, operation_id, physical_resource_id, reporting_time, reporting_status,
        reporting_quantity,whether_feed_back
        from sds_fee_feedback_production
        where operation_id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and reporting_time >= #{reportTime,jdbcType=TIMESTAMP}
    </select>

    <delete id="deleteDemandByOperationIds">
        delete
        from sds_peg_demand where demand_type != 'CUSTOMER_ORDER_DEMAND'
        and operation_input_id in(
        select id
        from sds_ord_operation_input where operation_id in
        <foreach collection="deleteDemandIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </delete>


    <select id="selectWorkOrderByLineGroupAndProductLine" resultMap="WorkOrderVOResultMap">
        with b as (
        SELECT
        *
        FROM
        sds_ord_work_order wo1
        WHERE
        wo1.product_id IN (
        SELECT
        a.product_id
        FROM
        (
        SELECT
        mrrsr.id,
        mrrsr.routing_id,
        mrr.product_id,
        mrrsr.physical_resource_id,
        ROW_NUMBER( ) OVER ( PARTITION BY mrrsr.routing_step_id ORDER BY mrrsr.priority ASC ) AS rn
        FROM
        mds_rou_routing_step_resource mrrsr,
        mds_rou_routing mrr
        WHERE
        mrrsr.routing_id = mrr.id
        AND mrr.product_code IN ( SELECT product_code
        FROM mds_product_stock_point_base
        WHERE line_group IN
        <foreach collection="lineGroupList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY product_code )
        ) a,
        mds_res_physical_resource mrpr
        WHERE
        a.rn = 1
        AND a.physical_resource_id = mrpr.id
        AND mrpr.physical_resource_code IN
        <foreach collection="productLineList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        )
        select wo2.*
        from b ,
        sds_ord_work_order wo2 where b.parent_id = wo2.id
        union
        select *
        from b
    </select>
    <select id="selectResourceByUserIdAndRoutingStepId"
            resultType="com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO">
        select mpr.id,
        mpr.physical_resource_code as physicalResourceCode,
        mpr.physical_resource_name as physicalResourceName
        from mds_rou_routing_step_resource mrsr,
        mds_res_physical_resource mpr
        where mrsr.routing_step_id = #{routingStepId,jdbcType=VARCHAR}
        and mrsr.physical_resource_id = mpr.id
        and mrsr.enabled = 'YES'
        <if test="userId != null and userId != ''">
            and find_in_set(#{userId,jdbcType=VARCHAR}, mpr.production_planner) > 0
        </if>
    </select>

    <select id="selectDeleteWorkOrder" resultType="java.lang.String">
        with a as (
        SELECT
        id,
        parent_id
        FROM
        sds_ord_work_order
        WHERE
        id IN ( SELECT order_id FROM sds_ord_operation
        WHERE id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        )
        select wo.id
        from sds_ord_work_order wo,a where wo.id = a.parent_id
        union
        select wo.id
        from sds_ord_work_order wo,a where wo.parent_id = a.parent_id
        union
        select wo.id
        from sds_ord_work_order wo,a where wo.id = a.id
    </select>

    <select id="getSplitRoutingStepResources" resultMap="SplitRoutingStepResourceMap">
        SELECT
        rrs.routing_id,
        rrs.sequence_no,
        rrs.standard_step_id,
        rrs.yield,
        rss.standard_step_type,
        rrsr.physical_resource_id,
        rrsr.priority,
        rrsr.unit_production_time,
        rpr.physical_resource_code
        FROM
        mds_rou_routing_step rrs
        left join mds_rou_standard_step rss on rss.id = rrs.standard_step_id
        left join mds_rou_routing_step_resource rrsr on rrsr.routing_step_id = rrs.id and rrsr.enabled = 'YES'
        left join mds_res_physical_resource rpr on rpr.id = rrsr.physical_resource_id
        where rrs.routing_id in
        <foreach collection="routingIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectWorkOrderMaxPriority" resultType="java.lang.Integer">
        select max(priority)
        from sds_ord_work_order
    </select>

    <update id="clearBatchWorkOrderPriority">
        update sds_ord_work_order set priority = null
        where id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectByProductCodeList" resultMap="SafetyStockLeveVOResultMap">
        select *
        from fdp_safety_stock_level
        where product_code in
        <foreach collection="codeList" item="productCode" index="index" open="(" separator="," close=")">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
        and stock_point_id in (
        SELECT
        msp.organize_id
        FROM
        mds_stock_point msp
        WHERE
        msp.plan_area = #{planArea,jdbcType=VARCHAR}
        AND organize_type = #{organizeType,jdbcType=VARCHAR}
        )
    </select>

    <select id="selectOrderOperation" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_operation where order_id in(
        select order_id from sds_ord_operation where id in
        <foreach collection="operationIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectOperationByParentWorkOrderIds" resultMap="VOResultMap">
        SELECT
            soo.id,
            soo.operation_code,
            soo.stock_point_id,
            soo.product_id,
            soo.routing_step_id,
            mpsp.product_code,
            mpsp.product_name,
            soo.quantity,
            woo2.parent_id as order_id
        FROM
            sds_ord_work_order woo2,
            sds_ord_operation soo,
            mds_product_stock_point mpsp,
            mds_rou_standard_step mrss
        WHERE
            woo2.parent_id IN
        <foreach collection="parentWorkOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
          and woo2.id = soo.order_id
          and soo.parent_id is null
          and soo.standard_step_id = mrss.id
          and mrss.key_step = 'YES'
          and soo.product_id = mpsp.id
    </select>

    <select id="selectWindFenceProductFixtureRelationsByProductCodes" resultMap="WindFenceProductFixtureRelationVO">
        select
        <include refid="WindFenceProductFixtureRelationColumnList"/>
        from mps_product_fixture_relation
        where 1 = 1
        and tooling_type = 'WIND_FENCE'
        <if test="productCodes != null and productCodes.size() > 0">
            and product_code in
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectWorkOrderIdsByResourceId" resultType="String">
        select DISTINCT a.order_id
        from sds_ord_operation a
        where a.planned_resource_id in
        <foreach collection="resourceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="startTime != null">
            AND a.end_time &gt; #{startTime}
        </if>
    </select>

    <select id="selectInventoryShift" resultType="com.yhl.scp.dfp.stock.vo.InventoryShiftVO">
        SELECT
            t.product_code as productCode,
            t.planned_date as plannedDate,
            sum( t.ending_inventory_min_safe_diff ) as endingInventoryMinSafeDiff
        FROM
            fdp_inventory_shift t
        WHERE
            t.version_id = #{versionId,jdbcType=VARCHAR}
        GROUP BY
            t.product_code,
            t.planned_date
    </select>

</mapper>
