package com.yhl.scp.biz.common.enums;

/**
 * redis key管理枚举
 *
 * <AUTHOR>
 */
public enum RedisKeyManageEnum {
    /**
     * IPS
     */
    MASTER_SCENARIO("IPS", "MASTER_SCENARIO#{tenantId}", "企业场景信息"),

    BUSINESS_MONITOR_LOG_QUEUE("IPS", "businessMonitorLogQueue", "业务监控日志"),

    ACCESS_TOKEN("IPS", "ACCESS_TOKEN:%s", "accessToken锁"),

    AUTH_CODE_KEY_PREFIX("IPS", "AMS%s", "验证码锁"),

    COLLECTION_KEY_PREFIX("IPS", "collection:management:{tenantId}:{%s}", "字典表缓存key"),

    WEBSOCKET_SESSION_PREFIX("IPS", "WEBSOCKET_SESSION_%s", "websocket session锁"),

    WEBSOCKET_DATA_RANGE_PREFIX("IPS", "WEBSOCKET_DATA_RANGE_%s", "websocket 数据范围锁"),

    WEBSOCKET_BROADCAST_CHANNEL("IPS", "WEBSOCKET_BROADCAST_CHANNEL", "websocket 广播锁"),

    WEBSOCKET_SERVICE_MESSAGE_CHANNEL_PREFIX("IPS", "WEBSOCKET_POD_MESSAGE_CHANNEL_%s", "websocket 特定用户消息锁"),


    /**
     * MDS
     */
    SPECIAL_PHYSICAL_RESOURCE("MDS", "specialPhysicalResourceVOS:%s", "特殊物理资源信息"),

    YHL_OBJ_UNIT("MDS", "YHL_OBJ_UNIT_%s", "对象单元信息"),

    MDS_ROUTING_INFO_ALL_SYNC("MDS", "MDS_ROUTING_INFO_ALL_SYNC", "工艺路径全量同步锁"),

    MDS_PRODUCT_ROUTING_INFO_OR_MOLD_CHANGE_TIME_SYNC("MDS", "MDS_PRODUCT_ROUTING_INFO_OR_MOLD_CHANGE_TIME_SYNC#{scenario}",
            "工艺路径及生产节拍"),
    /**
     * DFP
     */
    DELIVERY_PLAN_PUBLISH("DFP", "DELIVERY_PLAN_PUBLISH_LOCK#{userId}", "发货计划发布锁"),

    CLEAN_FORECAST_DATA_RECALCULATE_KEY("DFP", "CLEAN_FORECAST_DATA_RECALCULATE_KEY", "滚动预测数据重新计算锁"),

    CONSISTENCE_DEMAND_FORECAST_SAVE_AND_PUBLISH("DFP", "CONSISTENCE_DEMAND_FORECAST_SAVE_AND_PUBLISH#{scenario}",
            "一致性需求预测版本创建并发布"),
    
    DFP_CALENDAR_RULE_KEY("DFP", "DFP_CALENDAR_RULE_KEY", "日历规则保存锁"),
    /**
     * MPS
     */
    MATER_PLAN_DO_PUBLISH("MPS", "MATER_PLAN_DOPUBLISH_{userId}", "生产计划发布"),

    MATER_PLAN_DO_PUBLISH_STATUS("MPS", "MATER_PLAN_DOPUBLISH_STATUS_{userId}", "生产计划发布状态"),

    ADJUST_RESOURCE_LOCK_PREFIX("MPS", "EXECUTE_RESOURCE_LIST%s", "手工编辑资源锁"),

    CAPACITY_BALANCE_PUBLISH("MPS", "CAPACITY_BALANCE_PUBLISH", "产能平衡发布互斥锁"),

    CAPACITY_WEEK_BALANCE("MPS", "CAPACITY_WEEK_BALANCE", "周产能平衡计算互斥锁"),

    CAPACITY_BALANCE("MPS", "CAPACITY_BALANCE%s", "月产能平衡计算互斥锁"),

    RESOURCE_LOCK_KEY_PREFIX("MPS", "adjust_resource_operate_user_id:%s", "手工编辑限制资源使用人互斥锁"),

    ALGORITHM_RUNNING_LOCK_KEY_PREFIX("MPS", "adjust_algorithm_running_lock_key:{userId}", "手工编辑算法运行互斥锁"),

    ALGORITHM_RUNNING_PARAM_KEY_PREFIX("MPS", "adjust_algorithm_running_param_key:{userId}", "手工编辑算法参数缓存key"),

    REQUEST_DEFERRED_MAP_KEY("MPS", "adjust_request_deferred_key:{userId}", "手工编辑延迟请求缓存缓存key"),

    REQUEST_QUANTITY_LOCK_KEY_PREFIX("MPS", "adjust_request_quantity_lock_key_prefix:{userId}", "手工编辑请求次数缓存key"),

    FEEDBACK_EXECUTE_RESOURCE_LIST("MPS", "FEEDBACK_EXECUTE_RESOURCE_LIST", "生产反馈执行资源互斥锁"),

    MPS_ALGORITHM_DELETE_WORK_ORDER("MPS", "MPS_ALGORITHM_DELETE_WORK_ORDER&{userId}", "待删除工单缓存key"),

    MPS_ALGORITHM_UN_PLAN_WORK_ORDER("MPS", "MPS_ALGORITHM_UN_PLAN_WORK_ORDER&{userId}", "待排产工单缓存key"),

    MPS_ALGORITHM_LOCK_WORK_ORDER("MPS", "MPS_ALGORITHM_LOCK_WORK_ORDER&{userId}", "锁定工单缓存key"),

    MPS_ALGORITHM_FIX_WORK_ORDER("MPS", "MPS_ALGORITHM_FIX_WORK_ORDER&{userId}", "固定工单缓存key"),

    MPS_UN_PLAN_SCHEDULE_WORK_ORDER("MPS", "MPS_UN_PLAN_SCHEDULE_WORK_ORDER&{userId}", "未计划工单缓存key"),

    /**
     * MRP
     */
    MRP_COMPUTE("MRP", "MRP_COMPUTE", "MRP计算互斥锁"),

    MRP_COMPUTE_RESULT("MRP", "MRP_COMPUTE_RESULT", "MRP计算结果互斥锁"),

    PLAN_NEED_SERIAL_NUMBER_KEY("MRP", "PLAN_NEED", "计划需求序号互斥锁"),

    MATERIAL_RISK_LEVEL_RISK_KEY("MRP", "MATERIAL_RISK", "材料风险等级锁"),

    GLASS_MRP_PUBLISHED_KEY("MRP", "GLASS_MRP_PUBLISHED", "原片发布推移结果互斥锁"),

    NO_GLASS_MRP_PUBLISHED_KEY("MRP", "NO_GLASS_MRP_PUBLISHED", "非原片发布推移结果互斥锁"),

    INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE("MRP", "INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE", "浮法发运互斥锁"),

    INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_USER("MRP", "INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_USER", "浮法发运用户互斥锁"),

    INVENTORY_OCEAN_FREIGHT_ISSUE("MRP", "INVENTORY_OCEAN_FREIGHT_ISSUE", "浮法海运互斥锁"),

    INVENTORY_OCEAN_FREIGHT_USER("MRP", "INVENTORY_OCEAN_FREIGHT_USER", "浮法海运用户互斥锁"),

    MATERIAL_PURCHASE_REQUIREMENT_ISSUE("MRP", "MATERIAL_PURCHASE_REQUIREMENT_ISSUE", "材料采购需求互斥锁"),

    MATERIAL_PURCHASE_REQUIREMENT_USER("MRP", "MATERIAL_PURCHASE_REQUIREMENT_USER", "材料采购需求用户互斥锁"),

    MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE("MRP", "MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE#{userId}", "计算使用毛需求版本互斥锁"),

    LONG_TERM_FORECAST_PUBLISH("MRP", "LONG_TERM_FORECAST_PUBLISH", "材料长期预测下发互斥锁"),

    MATERIAL_PLAN_NEED_ISSUE("MRP", "PLAN_NEED_ISSUE", "要货计划下发互斥锁"),

    MATERIAL_ARRIVAL_TRACKING_ISSUE("MRP", "ARRIVAL_TRACKING_ISSUE", "到货跟踪下发互斥锁"),

    INVENTORY_OVERDUE_STAGNANT_PUBLISH("MRP", "INVENTORY_OVERDUE_STAGNANT_PUBLISH", "超期呆滞库存发布Key"),

    MATERIAL_PURCHASE_REVIEW_CALC("MRP", "MATERIAL_PURCHASE_REVIEW_CALC", "材料采购评审计算Key"),

    MATERIAL_PURCHASE_REVIEW_RELEASE("MRP", "MATERIAL_PURCHASE_REVIEW_RELEASE", "材料采购评审发布Key"),

    MATERIAL_PURCHASE_REVIEW_VERSION("MRP", "MATERIAL_PURCHASE_REVIEW_VERSION", "材料采购评审版本Key"),

    GLASS_PURCHASE_DATA_CALC("MRP", "GLASS_PURCHASE_DATA_CALC", "原片采购计划计算Key"),

    GLASS_PURCHASE_DATA_RELEASE("MRP", "GLASS_PURCHASE_DATA_RELEASE", "原片采购计划发布Key"),

    VEHICLE_INVENTORY_CLASS_B_FACTORY_REPORT_GENERATE("MRP", "VEHICLE_INVENTORY_CLASS_B_FACTORY_REPORT_GENERATE", "车型库存（B类本厂）报表生成Key"),

    VEHICLE_INVENTORY_CLASS_B_DEDICATED_REPORT_GENERATE("MRP", "VEHICLE_INVENTORY_CLASS_B_DEDICATED_REPORT_GENERATE", "车型库存（B类专用）报表生成Key"),

    /**
     * DCP
     */
    EXTERNAL_API_TOKEN("DCP", "bpim:external:api:token:%s", "外部api token锁"),

    EXTERNAL_API("DCP", "bpim:external:api:%s", "外部api锁"),

    IAM_TOKEN("DCP", "token:%s", "iam token锁"),
    ;

    private String moduleCode;

    private String key;

    private String name;

    RedisKeyManageEnum(String moduleCode, String key, String name) {
        this.moduleCode = moduleCode;
        this.key = key;
        this.name = name;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
