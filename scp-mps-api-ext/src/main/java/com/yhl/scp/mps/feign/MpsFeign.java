package com.yhl.scp.mps.feign;

import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderQuery;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.*;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO;
import com.yhl.scp.mps.highValueMaterials.dto.MpsHighValueMaterialsDTO;
import com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO;
import com.yhl.scp.mps.nakedGlass.dto.NakedGlassDTO;
import com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO;
import com.yhl.scp.mps.operationPublished.vo.DemandPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationInputPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationLogDTO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.*;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.rule.dto.AlgorithmConstraintRuleDTO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.dto.FulfillmentDTO;
import com.yhl.scp.sds.extension.pegging.dto.SupplyDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.FulfillmentVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <code>MpsFeign</code>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-06 16:33:35
 */
@FeignClient(value = ServletContextConstants.MPS, path = "/", configuration = FeignConfig.class,
        url = "${mps.feign.url:}")
public interface MpsFeign {

    /**
     * 增加高价值物                                                                                                                                                              Q料数据
     *
     * @param mpsHighValueMaterialsDTOs
     * @return
     */
    @PostMapping(value = "/api/mpsHighValueMaterials/createNewData")
    BaseResponse<Void> createNewData(@RequestHeader("scenario") String scenario, @RequestBody List<MpsHighValueMaterialsDTO> mpsHighValueMaterialsDTOs);

    /**
     * 查询唯一性高价值物                                                                                                                                                              Q料数据
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/api/mpsHighValueMaterials/queryByParams")
    List<MpsHighValueMaterialsVO> queryByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    /**
     * 同步子库存货位信息
     */

    @ApiOperation(value = "同步子库存货位信息")
    @PostMapping("api/subInventoryCargoLocation/handleData")
    BaseResponse<Void> syncSubInventoryCargoLocation(@RequestHeader("scenario") String scenario,
                                                     @RequestBody List<MesSubInventoryCargoLocation> subInventoryCargoLocations);

    /**
     * 查询子库存货位信息
     *
     * @param scenario
     * @param params
     * @return
     */
    @PostMapping(value = "/api/subInventoryCarloLocation")
    List<SubInventoryCargoLocationVO> queryByParams1(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @PostMapping(value = "/api/subInventoryCarloLocation/queryByFreightSpaces")
    List<SubInventoryCargoLocationVO> queryByFreightSpaces(@RequestHeader("scenario") String scenario,
                                                           @RequestBody List<String> spaceList,
                                                           @RequestParam("stockPointType") String stockPointType);

    /**
     * 获取最新发布的产能平衡版本
     *
     * @return
     */
    @PostMapping("/api/capacityBalanceVersion/selectLatestCapacityBalanceVersionCode")
    CapacityBalanceVersionVO selectLatestCapacityBalanceVersionCode(@RequestHeader("scenario") String scenario);

    /**
     * 获取最新发布的产能平衡版本
     *
     * @return
     */
    @PostMapping("/api/capacityBalanceVersion/selectCapacitySupplyRelationshipByVersionCode")
    List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipByVersionCode(@RequestHeader("scenario") String scenario, @RequestParam(value = "versionCode") String versionCode);

    /**
     * 获取最新已发布的主生产计划版本（未发布）
     *
     * @return
     */
    @GetMapping("/api/masterPlanVersion/getLatestPublishedMpsVersion")
    MasterPlanVersionVO getLatestPublishedMpsVersion();

    /**
     * 获取最新已发布的主生产计划版本（已发布）
     *
     * @return
     */
    @GetMapping("/api/masterPlanVersion/getLatestPublishedMpsVersionPublished")
    MasterPlanVersionVO getLatestPublishedMpsVersionPublished();

    @GetMapping("/api/masterPlanIssuedData/getMasterPlanIssuedDataByVersionId")
    List<MasterPlanIssuedDataVO> getMasterPlanIssuedDataByVersionId(@RequestParam(value = "masterPlanVersionId") String masterPlanVersionId);

    @ApiOperation(value = "同步生产报工反馈信息")
    @PostMapping("reprotFeedback/handleData")
    BaseResponse<Void> doSyncReportFeedback(@RequestHeader("scenario") String scenario, @RequestBody List<MesReportFeedback> feedbacks);

    @ApiOperation(value = "获取需求")
    @PostMapping("demand/getDemandListByParams")
    List<DemandVO> getDemandListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "添加需求")
    @PostMapping("api/supply/addDemand")
    BaseResponse<Void> addDemand(@RequestBody List<DemandDTO> demandDTOList);

    @ApiOperation(value = "修改需求")
    @PostMapping("api/supply/updateDemand")
    BaseResponse<Void> updateDemand(@RequestHeader("scenario") String scenario, @RequestBody List<DemandDTO> demandDTOList);

    @ApiOperation(value = "获取分配关系")
    @PostMapping("api/fulfillment/getFulfillmentListByParams")
    List<FulfillmentVO> getFulfillmentListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "添加分配关系")
    @PostMapping("api/supply/addFulfillment")
    BaseResponse<Void> addFulfillment(@RequestBody List<FulfillmentDTO> fulfillmentDTOList);

    @ApiOperation(value = "修改分配关系")
    @PostMapping("api/supply/updateFulfillment")
    BaseResponse<Void> updateFulfillment(@RequestHeader("scenario") String scenario, @RequestBody List<FulfillmentDTO> fulfillmentDTOList);

    @ApiOperation(value = "删除分配关系（根据供应id）")
    @PostMapping("api/supply/deleteFulfillmentBySupplyId")
    BaseResponse<Void> deleteFulfillmentBySupplyId(@RequestBody List<String> idList);

    @ApiOperation(value = "删除分配关系（根据需求id）")
    @PostMapping("api/supply/deleteFulfillmentByDemandId")
    BaseResponse<Void> deleteFulfillmentByDemandId(@RequestBody List<String> idList);

    @ApiOperation(value = "获取供应")
    @PostMapping("api/supply/getSupplyListByParams")
    List<SupplyVO> getSupplyListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "添加供应")
    @PostMapping("api/supply/addSupply")
    BaseResponse<Void> addSupply(@RequestHeader("scenario") String scenario, @RequestBody List<SupplyDTO> supplyDTOList);

    @ApiOperation(value = "修改供应")
    @PostMapping("api/supply/updateSupply")
    BaseResponse<Void> updateSupply(@RequestHeader("scenario") String scenario, @RequestBody List<SupplyDTO> supplyDTOList);

    @ApiOperation(value = "同步换模换型时间")
    @PostMapping("api/moldChangeTime/handleData")
    BaseResponse<Void> syncMoldChangeTime(@RequestHeader("scenario") String scenario,
                                          @RequestBody List<MesMoldChangeTime> moldChangeTimes);

    @PostMapping(value = "chainLine/handleData")
    BaseResponse<Void> handleChainLine(@RequestHeader("scenario") String scenario,
                                       @RequestBody List<ChainLineInventoryLogDTO> chainLineInventoryLogDTOList);

    @ApiOperation(value = "查询提前批次生产规则")
    @PostMapping("api/productAdvanceBatchRule/selectAll")
    List<ProductAdvanceBatchRuleVO> selectAllProductAdvanceBatchRule();

    @ApiOperation(value = "同步MES裸玻成品映射")
    @PostMapping("nakedGlass/syncNakedGlassData")
    BaseResponse<Void> syncNakedGlassData(@RequestHeader("scenario") String scenario,
                                          @RequestBody List<NakedGlassDTO> list);

    @ApiOperation(value = "根据客户零件号获取裸玻映射关系")
    @PostMapping("api/nakedGlass/selectNakedGlassByFinishedProducts")
    List<NakedGlassVO> selectNakedGlassByFinishedProducts(@RequestBody List<String> productCodes);

    @ApiOperation(value = "获取厂能平衡数据")
    @PostMapping("api/capacitySupplyRelationship/selectCapacitySupplyRelationshipByParams")
    List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipByParams(
            @RequestHeader("scenario") String scenario,
            @RequestBody Map<String, Object> params);

    @PostMapping("api/capacityBalanceWeekly/dataConsistentCheck")
    List<CapacitySupplyRelationshipVO> selectCapacityBalanceWeeklyDataConsistentCheck(@RequestHeader("scenario") String scenario);

    @PostMapping("api/capacityBalanceMonthly/dataConsistentCheck")
    List<CapacitySupplyRelationshipVO>  selectCapacityBalanceMonthlyDataConsistentCheck(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "主生产计划-工单")
    @PostMapping("api/masterPlan/getMasterPlan")
    List<MasterPlanWorkOrderBodyVO> getMasterPlan(@RequestBody MasterPlanReq masterPlanReq);

    @ApiOperation(value = "发货计划总览查询")
    @PostMapping("api/masterPlan/getDeliveryPlanGeneralViewVO")
    List<DeliveryPlanGeneralViewVO> getDeliveryPlanGeneralViewVO(@RequestBody MasterPlanReq masterPlanReq);

    @ApiOperation(value = "获取算法约束规则数据")
    @PostMapping("api/algorithmConstraintRule/getAlgorithmConstraintRuleMap")
    AlgorithmConstraintRuleDTO getAlgorithmConstraintRuleMap(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "计划单同步")
    @PostMapping("api/erpPlanOrder/handleData")
    BaseResponse<Void> handlePlanOrder(@RequestHeader("scenario") String scenario, @RequestBody List<ErpPlanOrderQuery> list);

    @ApiOperation(value = "工单号同步")
    @PostMapping("api/masterPlanRelation/syncMasterPlanRelation")
    BaseResponse<Void> syncMasterPlanRelation(@RequestHeader("scenario") String scenario, @RequestBody List<MasterPlanRelationLogDTO> map);

    @ApiOperation(value = "查询制造订单发布数据")
    @PostMapping("api/workOrderPublished/selectWorkOrderPublishedByParams")
    List<WorkOrderPublishedVO> selectWorkOrderPublishedByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询工序发布数据")
    @PostMapping("api/operationPublished/selectOperationPublishedByParams")
    List<OperationPublishedVO> selectOperationPublishedByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询需求发布数据")
    @PostMapping("api/demandPublished/selectDemandPublishedByParams")
    List<DemandPublishedVO> selectDemandPublishedByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询工序输入发布数据")
    @PostMapping("api/operationInputPublished/selectOperationInputPublishedByParams")
    List<OperationInputPublishedVO> selectOperationInputPublishedByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);


    @ApiOperation(value = "需求预测发布时触发产能平衡计算")
    @GetMapping("api/capacity/doRefreshCapacityBalance")
    void doRefreshCapacityBalance(@RequestParam(value = "scenario") String scenario,
                                  @RequestParam(value = "userId") String userId,
                                  @RequestParam(value = "planPeriod") String planPeriod,
                                  @RequestParam(value = "capacityPeriod") String capacityPeriod);

    @ApiOperation(value = "获取logID")
    @GetMapping("api/masterPlanPublishedLog/selectMasterPlanPublishedLogIdByOperatorId")
    String selectMasterPlanPublishedLogIdByOperatorId();

    @ApiOperation(value = "获取委外需求")
    @PostMapping("api/outsourceTransferDemandDetail/selectOutsourceTransferDemandDetailByParams")
    List<OutsourceTransferDemandDetailVO> selectOutsourceTransferDemandDetailByParams(
            @RequestHeader("scenario") String scenario,
            @RequestBody Map<String, Object> params);

    @ApiOperation(value = "周产能平衡最新版本时间")
    @GetMapping("api/capacityBalanceVersion/selectWeekMaxVersionTime")
    String selectWeekMaxVersionTime(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "获取制造订单VO")
    @PostMapping("api/workOrder/selectWorkOrderByParams")
    List<WorkOrderVO> selectWorkOrderByParams(@RequestHeader("scenario") String scenario,
                                              @RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取工序VO")
    @PostMapping("api/operation/selectOperationByParams")
    List<OperationVO> selectOperationByParams(@RequestHeader("scenario") String scenario,
                                              @RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取需求VO")
    @PostMapping("api/demand/selectDemandByParams")
    List<DemandVO> selectDemandByParams(@RequestHeader("scenario") String scenario,
                                        @RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取工序输入VO")
    @PostMapping("api/operationInput/selectOperationInputByParams")
    List<OperationInputVO> selectOperationInputByParams(@RequestHeader("scenario") String scenario,
                                                        @RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取工序输入VO")
    @PostMapping("api/operationTask/selectByMasterReq")
    List<MasterPlanTaskVO> selectByMasterReq(@RequestHeader("scenario") String scenario,
                                             @RequestBody MasterPlanReq masterPlanReq);

    @ApiOperation(value = "处理MES生产反馈数据")
    @PostMapping("api/mesFeedBack/handleMesFeedBack")
    void handleMesFeedBack(@RequestHeader("scenario") String scenario,
                           @RequestBody List<MesFeedBack> mesFeedBacks);


    @ApiOperation(value = "同步工装目录关系")
    @PostMapping(value = "synMoldToolingGroupDir")
    BaseResponse<Void> synMoldToolingGroupDir(@RequestParam("tenantId") String tenantId,
                                              @RequestParam("scenario") String scenario);

    @ApiOperation(value = "同步系统模具工装族与目录号关系数据")
    @PostMapping("MoldToolingGroupDir/handleData")
    BaseResponse<Void> handleMoldToolingGroupDir(@RequestHeader("scenario") String scenario,
                                                 @RequestBody List<MesMoldToolingGroupDir> o);

    @ApiOperation(value = "根据物品编码查询工装模具与目录号关系")
    @PostMapping("api/selectMoldToolingRelationsByProductCodes")
    Map<String, String> selectMoldToolingRelationsByProductCodes(@RequestHeader("scenario") String scenario,
                                                                 @RequestBody List<String> productCodes);

    @ApiOperation(value = "根据物品编码查询工装模具与目录号数量")
    @PostMapping("api/selectMoldToolingRelationCountByProductCodes")
    List<ProductFixtureRelationVO> selectMoldToolingRelationCountByProductCodes(@RequestHeader("scenario") String scenario,
                                                                                @RequestBody List<String> productCodes);

    @ApiOperation(value = "预警配置查询sql数据")
    @PostMapping("api/selectByManualSql")
    List<LinkedHashMap<String, Object>> selectByManualSql(@RequestHeader("scenario") String scenario,
                                                          @RequestBody Map<String,String> sqlContext);

    @ApiOperation(value = "查询反馈数据")
    @PostMapping("api/selectFeedbackProductionByOperationIds")
    List<FeedbackProductionVO> selectFeedbackProductionByOperationIds(@RequestHeader("scenario") String scenario,
                                                                @RequestBody List<String> operationIds);

    @ApiOperation(value = "计划调整并获取工序数据")
    @GetMapping("api/planAdjustGetData")
    BaseResponse<List<OperationVO>> planAdjustGetData(@RequestHeader("scenario") String scenario, @RequestParam("taskId") String taskId);

    @ApiOperation(value = "获取库存货位")
    @PostMapping("api/subInventoryCargoLocation")
    List<SubInventoryCargoLocationVO> subInventoryCargoLocation(@RequestHeader("scenario") String scenario,
                                                                @RequestBody List<String> freightSpaces);
    @ApiOperation(value = "组装视图对象枚举")
    @PostMapping("api/getObjectTypeEnumMap")
    Map<String, String> getObjectTypeEnumMap();

    @ApiOperation(value = "组装视图对象")
    @PostMapping("api/getObjectTypeClassMap")
    Map<String,Class<?>> getObjectTypeClassMap();

    @ApiOperation(value = "批量修改工序")
    @PostMapping("api/batchUpdateOperation")
    void batchUpdateOperation(@RequestBody List<OperationVO> operation);

    @ApiOperation(value = "批量修改订单")
    @PostMapping("api/batchUpdateWorkOrder")
    void batchUpdateWorkOrder(@RequestBody List<WorkOrderVO> workOrderVOS);
}
